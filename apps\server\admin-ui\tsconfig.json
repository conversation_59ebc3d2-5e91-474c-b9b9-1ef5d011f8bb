{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "importHelpers": true, "module": "es2020", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "moduleResolution": "node", "experimentalDecorators": true, "strict": true, "noImplicitAny": false, "strictPropertyInitialization": false, "target": "ES2022", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "react", "typeRoots": [], "lib": ["es2019", "dom", "esnext.asynciterable"], "useDefineForClassFields": false}}