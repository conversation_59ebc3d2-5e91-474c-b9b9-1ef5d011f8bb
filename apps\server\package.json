{"name": "sugarjays-shop", "version": "0.1.0", "private": true, "scripts": {"dev:server": "ts-node ./src/index.ts", "dev:worker": "ts-node ./src/index-worker.ts", "dev": "concurrently npm:dev:*", "build": "tsc", "start:server": "node ./dist/index.js", "start:worker": "node ./dist/index-worker.js", "start": "concurrently npm:start:*"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/graphql": "^12.0.0", "@vendure/admin-ui-plugin": "3.3.1", "@vendure/asset-server-plugin": "3.3.1", "@vendure/common": "^3.3.1", "@vendure/core": "3.3.1", "@vendure/email-plugin": "3.3.1", "@vendure/graphiql-plugin": "3.3.1", "dotenv": "16.5.0", "graphql-tag": "^2.12.6", "pg": "8.16.0", "typeorm": "^0.3.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/pg": "^8.0.0", "@types/react": "19.1.6", "@vendure/cli": "3.3.1", "@vendure/ui-devkit": "3.3.1", "concurrently": "9.1.2", "ts-node": "^10.9.2", "typescript": "5.8.2"}}