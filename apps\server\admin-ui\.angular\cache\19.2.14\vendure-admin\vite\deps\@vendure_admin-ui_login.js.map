{"version": 3, "sources": ["../../../../../../../node_modules/@vendure/admin-ui/fesm2022/vendure-admin-ui-login.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, Injectable, NgModule } from '@angular/core';\nimport * as i1 from '@vendure/admin-ui/core';\nimport { ADMIN_UI_VERSION, getAppConfig, AUTH_REDIRECT_PARAM, SharedModule } from '@vendure/admin-ui/core';\nimport * as i1$1 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i3 from '@angular/common/http';\nimport * as i4 from '@clr/angular';\nimport * as i5 from '@angular/common';\nimport * as i6 from '@angular/forms';\nimport * as i7 from '@angular/cdk/overlay';\nimport * as i8 from '@ngx-translate/core';\nimport { map } from 'rxjs/operators';\nconst _c0 = a0 => ({\n  brand: a0\n});\nfunction LoginComponent_p_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 20);\n    i0.ɵɵtext(1, \" Photo by \");\n    i0.ɵɵelementStart(2, \"a\", 21);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" on \");\n    i0.ɵɵelementStart(5, \"a\", 21);\n    i0.ɵɵtext(6, \"Unsplash\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"href\", ctx_r0.imageCreatorUrl, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.imageCreator);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"href\", ctx_r0.imageUnsplashUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction LoginComponent_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.imageLocation);\n  }\n}\nfunction LoginComponent_img_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 23);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.imageUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.imageUrl);\n  }\n}\nfunction LoginComponent_img_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 24);\n  }\n}\nclass LoginComponent {\n  constructor(authService, router, httpClient, localizationService) {\n    this.authService = authService;\n    this.router = router;\n    this.httpClient = httpClient;\n    this.localizationService = localizationService;\n    this.username = '';\n    this.password = '';\n    this.rememberMe = false;\n    this.version = ADMIN_UI_VERSION;\n    this.brand = getAppConfig().brand;\n    this.hideVendureBranding = getAppConfig().hideVendureBranding;\n    this.customImageUrl = getAppConfig().loginImageUrl;\n    this.imageUrl = '';\n    this.imageUnsplashUrl = '';\n    this.imageLocation = '';\n    this.imageCreator = '';\n    this.imageCreatorUrl = '';\n    if (this.customImageUrl) {\n      this.imageUrl = this.customImageUrl;\n    } else {\n      this.loadImage();\n    }\n  }\n  ngOnInit() {\n    this.direction$ = this.localizationService.direction$;\n  }\n  logIn() {\n    this.errorMessage = undefined;\n    this.authService.logIn(this.username, this.password, this.rememberMe).subscribe(result => {\n      switch (result.__typename) {\n        case 'CurrentUser':\n          const redirect = this.getRedirectRoute();\n          this.router.navigateByUrl(redirect ? redirect : '/');\n          break;\n        case 'InvalidCredentialsError':\n        case 'NativeAuthStrategyError':\n          this.errorMessage = result.message;\n          break;\n      }\n    });\n  }\n  loadImage() {\n    this.httpClient.get('https://login-image.vendure.io').toPromise().then(res => {\n      this.updateImage(res);\n    });\n  }\n  updateImage(res) {\n    const user = res.user;\n    const location = res.location;\n    this.imageUrl = res.urls.regular + '?utm_source=Vendure+Login+Image&utm_medium=referral';\n    this.imageCreator = user.name;\n    this.imageLocation = location.name;\n    this.imageCreatorUrl = user.links.html + '?utm_source=Vendure+Login+Image&utm_medium=referral';\n    this.imageUnsplashUrl = res.links.html;\n  }\n  /**\n   * Attempts to read a redirect param from the current url and parse it into a\n   * route from which the user was redirected after a 401 error.\n   */\n  getRedirectRoute() {\n    let redirectTo;\n    const re = new RegExp(`${AUTH_REDIRECT_PARAM}=(.*)`);\n    try {\n      const redirectToParam = window.location.search.match(re);\n      if (redirectToParam && 1 < redirectToParam.length) {\n        redirectTo = atob(decodeURIComponent(redirectToParam[1]));\n      }\n    } catch (e) {\n      // ignore\n    }\n    return redirectTo;\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i1$1.Router), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i1.LocalizationService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"vdr-login\"]],\n      standalone: false,\n      decls: 36,\n      vars: 37,\n      consts: [[1, \"login-wrapper\", 3, \"dir\"], [1, \"login-wrapper-inner\"], [1, \"login-wrapper-image\"], [1, \"login-wrapper-image-content\"], [1, \"login-wrapper-image-title\"], [1, \"login-wrapper-image-copyright\"], [\"class\", \"creator\", 4, \"ngIf\"], [\"class\", \"location\", 4, \"ngIf\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"login-wrapper-form\"], [1, \"login-title\"], [1, \"login-form\"], [1, \"login-group\"], [\"type\", \"text\", \"name\", \"username\", \"id\", \"login_username\", 1, \"username\", 3, \"ngModelChange\", \"ngModel\", \"placeholder\"], [\"name\", \"password\", \"type\", \"password\", \"id\", \"login_password\", 1, \"password\", 3, \"ngModelChange\", \"ngModel\", \"placeholder\"], [1, \"login-error\", 3, \"clrAlertType\", \"clrAlertClosable\"], [1, \"alert-text\"], [\"type\", \"checkbox\", \"clrCheckbox\", \"\", \"id\", \"rememberme\", \"name\", \"rememberme\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"submit\", 1, \"button\", \"primary\", \"login-button\", 3, \"click\", \"disabled\"], [\"class\", \"login-wrapper-logo\", \"src\", \"assets/logo-login.webp\", 4, \"ngIf\"], [1, \"creator\"], [\"target\", \"_blank\", 3, \"href\"], [1, \"location\"], [3, \"src\", \"alt\"], [\"src\", \"assets/logo-login.webp\", 1, \"login-wrapper-logo\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelementStart(2, \"div\", 1)(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4);\n          i0.ɵɵtext(6);\n          i0.ɵɵpipe(7, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 5);\n          i0.ɵɵtemplate(9, LoginComponent_p_9_Template, 7, 3, \"p\", 6)(10, LoginComponent_p_10_Template, 2, 1, \"p\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(11, LoginComponent_img_11_Template, 1, 2, \"img\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"p\", 10);\n          i0.ɵɵtext(14);\n          i0.ɵɵpipe(15, \"translate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"form\", 11)(17, \"div\", 12)(18, \"input\", 13);\n          i0.ɵɵpipe(19, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_18_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.username, $event) || (ctx.username = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"input\", 14);\n          i0.ɵɵpipe(21, \"translate\");\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_20_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.password, $event) || (ctx.password = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"clr-alert\", 15)(23, \"clr-alert-item\")(24, \"span\", 16);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"clr-checkbox-wrapper\")(27, \"input\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_27_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.rememberMe, $event) || (ctx.rememberMe = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"label\");\n          i0.ɵɵtext(29);\n          i0.ɵɵpipe(30, \"translate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\")(32, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_32_listener() {\n            return ctx.logIn();\n          });\n          i0.ɵɵtext(33);\n          i0.ɵɵpipe(34, \"translate\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(35, LoginComponent_img_35_Template, 1, 0, \"img\", 19);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"dir\", i0.ɵɵpipeBind1(1, 20, ctx.direction$));\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 22, \"common.login-image-title\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.imageCreator);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.imageLocation);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.imageUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(15, 24, \"common.login-title\", i0.ɵɵpureFunction1(35, _c0, ctx.hideVendureBranding ? ctx.brand : \"Vendure\")), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.username);\n          i0.ɵɵproperty(\"placeholder\", i0.ɵɵpipeBind1(19, 27, \"common.username\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.password);\n          i0.ɵɵproperty(\"placeholder\", i0.ɵɵpipeBind1(21, 29, \"common.password\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"visible\", ctx.errorMessage);\n          i0.ɵɵproperty(\"clrAlertType\", \"danger\")(\"clrAlertClosable\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.errorMessage, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.rememberMe);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(30, 31, \"common.remember-me\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", !ctx.username || !ctx.password);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(34, 33, \"common.login\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hideVendureBranding);\n        }\n      },\n      dependencies: [i4.ClrAlert, i4.ClrAlertItem, i4.ClrAlertText, i4.ClrLabel, i4.ClrCheckbox, i4.ClrCheckboxWrapper, i5.NgIf, i6.ɵNgNoValidate, i6.DefaultValueAccessor, i6.CheckboxControlValueAccessor, i6.NgControlStatus, i6.NgControlStatusGroup, i6.NgModel, i6.NgForm, i7.ɵɵDir, i1.FormFieldControlDirective, i5.AsyncPipe, i8.TranslatePipe],\n      styles: [\".login-wrapper[_ngcontent-%COMP%]{background:var(--color-weight-100);background-image:none;height:100vh;display:flex;align-items:center;justify-content:center;padding:20px}.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]{background:var(--login-wrapper-inner-bg);width:1120px;height:590px;display:flex;justify-content:flex-start;align-items:stretch;position:relative;border-radius:var(--border-radius);border:1px solid var(--color-weight-150);overflow:hidden}@media (max-width: 992px){.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]{flex-direction:column;height:auto;width:100%}}.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-image[_ngcontent-%COMP%]{height:100%;flex-grow:1;position:relative}@media (max-width: 992px){.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-image[_ngcontent-%COMP%]{height:300px}}.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{display:block;width:100%;height:100%;object-fit:cover;object-position:center;position:relative;z-index:1}.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-image[_ngcontent-%COMP%]   .login-wrapper-image-content[_ngcontent-%COMP%]{width:100%;height:100%;position:absolute;left:0;bottom:0;z-index:10;background:#020024;background:linear-gradient(180deg,#02002400,#000000bf);display:flex;flex-direction:column;align-items:flex-start;justify-content:flex-end;padding:30px}.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-image[_ngcontent-%COMP%]   .login-wrapper-image-content[_ngcontent-%COMP%]   .login-wrapper-image-title[_ngcontent-%COMP%]{font-size:1.6rem;font-weight:700;color:#fff;margin-bottom:20px}@media (max-width: 992px){.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-image[_ngcontent-%COMP%]   .login-wrapper-image-content[_ngcontent-%COMP%]   .login-wrapper-image-title[_ngcontent-%COMP%]{font-size:1.2rem}}.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-image[_ngcontent-%COMP%]   .login-wrapper-image-content[_ngcontent-%COMP%]   .login-wrapper-image-copyright[_ngcontent-%COMP%]{opacity:.8}.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-image[_ngcontent-%COMP%]   .login-wrapper-image-content[_ngcontent-%COMP%]   .login-wrapper-image-copyright[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.6rem;color:#fff;margin:0!important}.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-image[_ngcontent-%COMP%]   .login-wrapper-image-content[_ngcontent-%COMP%]   .login-wrapper-image-copyright[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff;text-decoration:underline}.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-form[_ngcontent-%COMP%]{height:100%;width:400px;padding:40px;display:flex;flex-direction:column;align-items:stretch;justify-content:center;box-shadow:0 20px 25px #0000001a;overflow:hidden;flex-shrink:0}@media (max-width: 992px){.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-form[_ngcontent-%COMP%]{height:auto;width:100%;padding:20px}}.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-form[_ngcontent-%COMP%]   .login-title[_ngcontent-%COMP%]{font-weight:700;font-size:1.2rem;margin-bottom:20px;color:var(--color-weight-600)}.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-form[_ngcontent-%COMP%]   .login-group[_ngcontent-%COMP%]   input.username[_ngcontent-%COMP%], .login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-form[_ngcontent-%COMP%]   .login-group[_ngcontent-%COMP%]   input.password[_ngcontent-%COMP%]{display:block;width:100%;margin-bottom:15px;padding:12px 16px!important;background:#fff;font-size:14px;line-height:22px;color:#52667a;outline:none;-webkit-appearance:none}.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-form[_ngcontent-%COMP%]   .login-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:100%!important;margin-top:20px!important}.login-wrapper[_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-logo[_ngcontent-%COMP%]{width:60px;height:auto;position:absolute;right:20px;top:20px}.login-button[_ngcontent-%COMP%]{width:100%;margin-top:var(--space-unit);justify-content:center}.version[_ngcontent-%COMP%]{flex:1;flex-grow:1;display:flex;align-items:flex-end;justify-content:center;color:var(--color-grey-300)}.version[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] + span[_ngcontent-%COMP%]{margin-inline-start:5px}.login-error[_ngcontent-%COMP%]{max-height:0;overflow:hidden;display:block}.login-error.visible[_ngcontent-%COMP%]{max-height:46px;transition:max-height .2s;animation:_ngcontent-%COMP%_shake .82s cubic-bezier(.36,.07,.19,.97) both;animation-delay:.2s;transform:translateZ(0);backface-visibility:hidden;perspective:1000px}@keyframes _ngcontent-%COMP%_shake{10%,90%{transform:translate3d(-1px,0,0)}20%,80%{transform:translate3d(2px,0,0)}30%,50%,70%{transform:translate3d(-4px,0,0)}40%,60%{transform:translate3d(4px,0,0)}}.login-wrapper[dir=rtl][_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-logo[_ngcontent-%COMP%]{right:auto;left:20px}.login-wrapper[dir=rtl][_ngcontent-%COMP%]   .login-wrapper-inner[_ngcontent-%COMP%]   .login-wrapper-image[_ngcontent-%COMP%]   .login-wrapper-image-content[_ngcontent-%COMP%]{left:auto;right:0}\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LoginComponent, [{\n    type: Component,\n    args: [{\n      selector: 'vdr-login',\n      standalone: false,\n      template: \"<div class=\\\"login-wrapper\\\" [dir]=\\\"direction$ | async\\\">\\n    <div class=\\\"login-wrapper-inner\\\">\\n        <div class=\\\"login-wrapper-image\\\">\\n            <div class=\\\"login-wrapper-image-content\\\">\\n                <div class=\\\"login-wrapper-image-title\\\">\\n                    {{ 'common.login-image-title' | translate }}\\n                </div>\\n                <div class=\\\"login-wrapper-image-copyright\\\">\\n                    <p *ngIf=\\\"imageCreator\\\" class=\\\"creator\\\">\\n                        Photo by <a [href]=\\\"imageCreatorUrl\\\" target=\\\"_blank\\\">{{ imageCreator }}</a> on\\n                        <a [href]=\\\"imageUnsplashUrl\\\" target=\\\"_blank\\\">Unsplash</a>\\n                    </p>\\n                    <p *ngIf=\\\"imageLocation\\\" class=\\\"location\\\">{{ imageLocation }}</p>\\n                </div>\\n            </div>\\n            <img *ngIf=\\\"imageUrl\\\" [src]=\\\"imageUrl\\\" [alt]=\\\"imageUrl\\\" />\\n        </div>\\n        <div class=\\\"login-wrapper-form\\\">\\n            <p class=\\\"login-title\\\">\\n                {{ 'common.login-title' | translate : { brand: hideVendureBranding ? brand : 'Vendure' } }}\\n            </p>\\n            <form class=\\\"login-form\\\">\\n                <div class=\\\"login-group\\\">\\n                    <input\\n                        class=\\\"username\\\"\\n                        type=\\\"text\\\"\\n                        name=\\\"username\\\"\\n                        id=\\\"login_username\\\"\\n                        [(ngModel)]=\\\"username\\\"\\n                        [placeholder]=\\\"'common.username' | translate\\\"\\n                    />\\n                    <input\\n                        class=\\\"password\\\"\\n                        name=\\\"password\\\"\\n                        type=\\\"password\\\"\\n                        id=\\\"login_password\\\"\\n                        [(ngModel)]=\\\"password\\\"\\n                        [placeholder]=\\\"'common.password' | translate\\\"\\n                    />\\n                    <clr-alert\\n                        [clrAlertType]=\\\"'danger'\\\"\\n                        [clrAlertClosable]=\\\"false\\\"\\n                        [class.visible]=\\\"errorMessage\\\"\\n                        class=\\\"login-error\\\"\\n                    >\\n                        <clr-alert-item>\\n                            <span class=\\\"alert-text\\\">\\n                                {{ errorMessage }}\\n                            </span>\\n                        </clr-alert-item>\\n                    </clr-alert>\\n                    <clr-checkbox-wrapper>\\n                        <input\\n                            type=\\\"checkbox\\\"\\n                            clrCheckbox\\n                            id=\\\"rememberme\\\"\\n                            name=\\\"rememberme\\\"\\n                            [(ngModel)]=\\\"rememberMe\\\"\\n                        />\\n                        <label>{{ 'common.remember-me' | translate }}</label>\\n                    </clr-checkbox-wrapper>\\n                    <div>\\n                        <button\\n                            type=\\\"submit\\\"\\n                            class=\\\"button primary login-button\\\"\\n                            (click)=\\\"logIn()\\\"\\n                            [disabled]=\\\"!username || !password\\\"\\n                        >\\n                            {{ 'common.login' | translate }}\\n                        </button>\\n                    </div>\\n                </div>\\n            </form>\\n        </div>\\n        <img class=\\\"login-wrapper-logo\\\" src=\\\"assets/logo-login.webp\\\" *ngIf=\\\"!hideVendureBranding\\\" />\\n    </div>\\n</div>\\n\",\n      styles: [\".login-wrapper{background:var(--color-weight-100);background-image:none;height:100vh;display:flex;align-items:center;justify-content:center;padding:20px}.login-wrapper .login-wrapper-inner{background:var(--login-wrapper-inner-bg);width:1120px;height:590px;display:flex;justify-content:flex-start;align-items:stretch;position:relative;border-radius:var(--border-radius);border:1px solid var(--color-weight-150);overflow:hidden}@media (max-width: 992px){.login-wrapper .login-wrapper-inner{flex-direction:column;height:auto;width:100%}}.login-wrapper .login-wrapper-inner .login-wrapper-image{height:100%;flex-grow:1;position:relative}@media (max-width: 992px){.login-wrapper .login-wrapper-inner .login-wrapper-image{height:300px}}.login-wrapper .login-wrapper-inner .login-wrapper-image img{display:block;width:100%;height:100%;object-fit:cover;object-position:center;position:relative;z-index:1}.login-wrapper .login-wrapper-inner .login-wrapper-image .login-wrapper-image-content{width:100%;height:100%;position:absolute;left:0;bottom:0;z-index:10;background:#020024;background:linear-gradient(180deg,#02002400,#000000bf);display:flex;flex-direction:column;align-items:flex-start;justify-content:flex-end;padding:30px}.login-wrapper .login-wrapper-inner .login-wrapper-image .login-wrapper-image-content .login-wrapper-image-title{font-size:1.6rem;font-weight:700;color:#fff;margin-bottom:20px}@media (max-width: 992px){.login-wrapper .login-wrapper-inner .login-wrapper-image .login-wrapper-image-content .login-wrapper-image-title{font-size:1.2rem}}.login-wrapper .login-wrapper-inner .login-wrapper-image .login-wrapper-image-content .login-wrapper-image-copyright{opacity:.8}.login-wrapper .login-wrapper-inner .login-wrapper-image .login-wrapper-image-content .login-wrapper-image-copyright p{font-size:.6rem;color:#fff;margin:0!important}.login-wrapper .login-wrapper-inner .login-wrapper-image .login-wrapper-image-content .login-wrapper-image-copyright a{color:#fff;text-decoration:underline}.login-wrapper .login-wrapper-inner .login-wrapper-form{height:100%;width:400px;padding:40px;display:flex;flex-direction:column;align-items:stretch;justify-content:center;box-shadow:0 20px 25px #0000001a;overflow:hidden;flex-shrink:0}@media (max-width: 992px){.login-wrapper .login-wrapper-inner .login-wrapper-form{height:auto;width:100%;padding:20px}}.login-wrapper .login-wrapper-inner .login-wrapper-form .login-title{font-weight:700;font-size:1.2rem;margin-bottom:20px;color:var(--color-weight-600)}.login-wrapper .login-wrapper-inner .login-wrapper-form .login-group input.username,.login-wrapper .login-wrapper-inner .login-wrapper-form .login-group input.password{display:block;width:100%;margin-bottom:15px;padding:12px 16px!important;background:#fff;font-size:14px;line-height:22px;color:#52667a;outline:none;-webkit-appearance:none}.login-wrapper .login-wrapper-inner .login-wrapper-form .login-group .btn{width:100%!important;margin-top:20px!important}.login-wrapper .login-wrapper-inner .login-wrapper-logo{width:60px;height:auto;position:absolute;right:20px;top:20px}.login-button{width:100%;margin-top:var(--space-unit);justify-content:center}.version{flex:1;flex-grow:1;display:flex;align-items:flex-end;justify-content:center;color:var(--color-grey-300)}.version span+span{margin-inline-start:5px}.login-error{max-height:0;overflow:hidden;display:block}.login-error.visible{max-height:46px;transition:max-height .2s;animation:shake .82s cubic-bezier(.36,.07,.19,.97) both;animation-delay:.2s;transform:translateZ(0);backface-visibility:hidden;perspective:1000px}@keyframes shake{10%,90%{transform:translate3d(-1px,0,0)}20%,80%{transform:translate3d(2px,0,0)}30%,50%,70%{transform:translate3d(-4px,0,0)}40%,60%{transform:translate3d(4px,0,0)}}.login-wrapper[dir=rtl] .login-wrapper-inner .login-wrapper-logo{right:auto;left:20px}.login-wrapper[dir=rtl] .login-wrapper-inner .login-wrapper-image .login-wrapper-image-content{left:auto;right:0}\\n\"]\n    }]\n  }], () => [{\n    type: i1.AuthService\n  }, {\n    type: i1$1.Router\n  }, {\n    type: i3.HttpClient\n  }, {\n    type: i1.LocalizationService\n  }], null);\n})();\n\n/**\n * This guard prevents loggen-in users from navigating to the login screen.\n */\nclass LoginGuard {\n  constructor(router, authService) {\n    this.router = router;\n    this.authService = authService;\n  }\n  canActivate(route) {\n    return this.authService.checkAuthenticatedStatus().pipe(map(authenticated => {\n      if (authenticated) {\n        this.router.navigate(['/']);\n      }\n      return !authenticated;\n    }));\n  }\n  static {\n    this.ɵfac = function LoginGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginGuard)(i0.ɵɵinject(i1$1.Router), i0.ɵɵinject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LoginGuard,\n      factory: LoginGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LoginGuard, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1$1.Router\n  }, {\n    type: i1.AuthService\n  }], null);\n})();\nconst loginRoutes = [{\n  path: '',\n  component: LoginComponent,\n  pathMatch: 'full',\n  canActivate: [LoginGuard]\n}];\nclass LoginModule {\n  static {\n    this.ɵfac = function LoginModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LoginModule,\n      declarations: [LoginComponent],\n      imports: [SharedModule, i1$1.RouterModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [SharedModule, RouterModule.forChild(loginRoutes)]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LoginModule, [{\n    type: NgModule,\n    args: [{\n      imports: [SharedModule, RouterModule.forChild(loginRoutes)],\n      exports: [],\n      declarations: [LoginComponent]\n    }]\n  }], null, null);\n})();\n\n// This file was generated by the build-public-api.ts script\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { LoginComponent, LoginGuard, LoginModule, loginRoutes };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,IAAM,MAAM,SAAO;AAAA,EACjB,OAAO;AACT;AACA,SAAS,4BAA4B,IAAI,KAAK;AAC5C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,OAAO,GAAG,YAAY;AACzB,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,OAAO,GAAG,MAAM;AACnB,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,OAAO,GAAG,UAAU;AACvB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,iBAAoB,aAAa;AAC9D,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,YAAY;AACxC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,kBAAqB,aAAa;AAAA,EACjE;AACF;AACA,SAAS,6BAA6B,IAAI,KAAK;AAC7C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,aAAa;AAAA,EAC3C;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,OAAO,UAAa,aAAa,EAAE,OAAO,OAAO,QAAQ;AAAA,EAChF;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACF;AACA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,aAAa,QAAQ,YAAY,qBAAqB;AAChE,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,sBAAsB;AAC3B,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,QAAQ,aAAa,EAAE;AAC5B,SAAK,sBAAsB,aAAa,EAAE;AAC1C,SAAK,iBAAiB,aAAa,EAAE;AACrC,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,QAAI,KAAK,gBAAgB;AACvB,WAAK,WAAW,KAAK;AAAA,IACvB,OAAO;AACL,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,aAAa,KAAK,oBAAoB;AAAA,EAC7C;AAAA,EACA,QAAQ;AACN,SAAK,eAAe;AACpB,SAAK,YAAY,MAAM,KAAK,UAAU,KAAK,UAAU,KAAK,UAAU,EAAE,UAAU,YAAU;AACxF,cAAQ,OAAO,YAAY;AAAA,QACzB,KAAK;AACH,gBAAM,WAAW,KAAK,iBAAiB;AACvC,eAAK,OAAO,cAAc,WAAW,WAAW,GAAG;AACnD;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,eAAK,eAAe,OAAO;AAC3B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY;AACV,SAAK,WAAW,IAAI,gCAAgC,EAAE,UAAU,EAAE,KAAK,SAAO;AAC5E,WAAK,YAAY,GAAG;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,KAAK;AACf,UAAM,OAAO,IAAI;AACjB,UAAM,WAAW,IAAI;AACrB,SAAK,WAAW,IAAI,KAAK,UAAU;AACnC,SAAK,eAAe,KAAK;AACzB,SAAK,gBAAgB,SAAS;AAC9B,SAAK,kBAAkB,KAAK,MAAM,OAAO;AACzC,SAAK,mBAAmB,IAAI,MAAM;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,QAAI;AACJ,UAAM,KAAK,IAAI,OAAO,GAAG,mBAAmB,OAAO;AACnD,QAAI;AACF,YAAM,kBAAkB,OAAO,SAAS,OAAO,MAAM,EAAE;AACvD,UAAI,mBAAmB,IAAI,gBAAgB,QAAQ;AACjD,qBAAa,KAAK,mBAAmB,gBAAgB,CAAC,CAAC,CAAC;AAAA,MAC1D;AAAA,IACF,SAAS,GAAG;AAAA,IAEZ;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAmB,kBAAqB,WAAW,GAAM,kBAAuB,MAAM,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,mBAAmB,CAAC;AAAA,IAC7M;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,MACzB,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,iBAAiB,GAAG,KAAK,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,GAAG,+BAA+B,GAAG,CAAC,SAAS,WAAW,GAAG,MAAM,GAAG,CAAC,SAAS,YAAY,GAAG,MAAM,GAAG,CAAC,GAAG,OAAO,OAAO,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,QAAQ,QAAQ,QAAQ,YAAY,MAAM,kBAAkB,GAAG,YAAY,GAAG,iBAAiB,WAAW,aAAa,GAAG,CAAC,QAAQ,YAAY,QAAQ,YAAY,MAAM,kBAAkB,GAAG,YAAY,GAAG,iBAAiB,WAAW,aAAa,GAAG,CAAC,GAAG,eAAe,GAAG,gBAAgB,kBAAkB,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,QAAQ,YAAY,eAAe,IAAI,MAAM,cAAc,QAAQ,cAAc,GAAG,iBAAiB,SAAS,GAAG,CAAC,QAAQ,UAAU,GAAG,UAAU,WAAW,gBAAgB,GAAG,SAAS,UAAU,GAAG,CAAC,SAAS,sBAAsB,OAAO,0BAA0B,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,UAAU,UAAU,GAAG,MAAM,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,OAAO,KAAK,GAAG,CAAC,OAAO,0BAA0B,GAAG,oBAAoB,CAAC;AAAA,MAC9mC,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACpE,UAAG,OAAO,CAAC;AACX,UAAG,OAAO,GAAG,WAAW;AACxB,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,6BAA6B,GAAG,GAAG,KAAK,CAAC,EAAE,IAAI,8BAA8B,GAAG,GAAG,KAAK,CAAC;AAC1G,UAAG,aAAa,EAAE;AAClB,UAAG,WAAW,IAAI,gCAAgC,GAAG,GAAG,OAAO,CAAC;AAChE,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,OAAO,CAAC,EAAE,IAAI,KAAK,EAAE;AAC3C,UAAG,OAAO,EAAE;AACZ,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,QAAQ,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,SAAS,EAAE;AAChE,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,iBAAiB,iBAAiB,SAAS,wDAAwD,QAAQ;AAC5G,YAAG,mBAAmB,IAAI,UAAU,MAAM,MAAM,IAAI,WAAW;AAC/D,mBAAO;AAAA,UACT,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,SAAS,EAAE;AACjC,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,iBAAiB,iBAAiB,SAAS,wDAAwD,QAAQ;AAC5G,YAAG,mBAAmB,IAAI,UAAU,MAAM,MAAM,IAAI,WAAW;AAC/D,mBAAO;AAAA,UACT,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,aAAa,EAAE,EAAE,IAAI,gBAAgB,EAAE,IAAI,QAAQ,EAAE;AAC3E,UAAG,OAAO,EAAE;AACZ,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,eAAe,IAAI,sBAAsB,EAAE,IAAI,SAAS,EAAE;AAC7D,UAAG,iBAAiB,iBAAiB,SAAS,wDAAwD,QAAQ;AAC5G,YAAG,mBAAmB,IAAI,YAAY,MAAM,MAAM,IAAI,aAAa;AACnE,mBAAO;AAAA,UACT,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,eAAe,IAAI,OAAO;AAC7B,UAAG,OAAO,EAAE;AACZ,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,aAAa,EAAE;AAClB,UAAG,eAAe,IAAI,KAAK,EAAE,IAAI,UAAU,EAAE;AAC7C,UAAG,WAAW,SAAS,SAAS,mDAAmD;AACjF,mBAAO,IAAI,MAAM;AAAA,UACnB,CAAC;AACD,UAAG,OAAO,EAAE;AACZ,UAAG,OAAO,IAAI,WAAW;AACzB,UAAG,aAAa,EAAE,EAAE,EAAE,EAAE;AACxB,UAAG,WAAW,IAAI,gCAAgC,GAAG,GAAG,OAAO,EAAE;AACjE,UAAG,aAAa,EAAE;AAAA,QACpB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,OAAU,YAAY,GAAG,IAAI,IAAI,UAAU,CAAC;AAC1D,UAAG,UAAU,CAAC;AACd,UAAG,mBAAmB,KAAQ,YAAY,GAAG,IAAI,0BAA0B,GAAG,GAAG;AACjF,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,YAAY;AACtC,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,aAAa;AACvC,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,QAAQ;AAClC,UAAG,UAAU,CAAC;AACd,UAAG,mBAAmB,KAAQ,YAAY,IAAI,IAAI,sBAAyB,gBAAgB,IAAI,KAAK,IAAI,sBAAsB,IAAI,QAAQ,SAAS,CAAC,GAAG,GAAG;AAC1J,UAAG,UAAU,CAAC;AACd,UAAG,iBAAiB,WAAW,IAAI,QAAQ;AAC3C,UAAG,WAAW,eAAkB,YAAY,IAAI,IAAI,iBAAiB,CAAC;AACtE,UAAG,UAAU,CAAC;AACd,UAAG,iBAAiB,WAAW,IAAI,QAAQ;AAC3C,UAAG,WAAW,eAAkB,YAAY,IAAI,IAAI,iBAAiB,CAAC;AACtE,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,WAAW,IAAI,YAAY;AAC1C,UAAG,WAAW,gBAAgB,QAAQ,EAAE,oBAAoB,KAAK;AACjE,UAAG,UAAU,CAAC;AACd,UAAG,mBAAmB,KAAK,IAAI,cAAc,GAAG;AAChD,UAAG,UAAU,CAAC;AACd,UAAG,iBAAiB,WAAW,IAAI,UAAU;AAC7C,UAAG,UAAU,CAAC;AACd,UAAG,kBAAqB,YAAY,IAAI,IAAI,oBAAoB,CAAC;AACjE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,YAAY,CAAC,IAAI,YAAY,CAAC,IAAI,QAAQ;AACxD,UAAG,UAAU;AACb,UAAG,mBAAmB,KAAQ,YAAY,IAAI,IAAI,cAAc,GAAG,GAAG;AACtE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,CAAC,IAAI,mBAAmB;AAAA,QAChD;AAAA,MACF;AAAA,MACA,cAAc,CAAI,UAAa,cAAiB,cAAiB,UAAa,aAAgB,oBAAuB,MAAS,eAAkB,sBAAyB,8BAAiC,iBAAoB,sBAAyB,SAAY,QAAW,KAAU,2BAA8B,WAAc,aAAa;AAAA,MACjV,QAAQ,CAAC,mqLAAmqL;AAAA,IAC9qL,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,43HAA43H;AAAA,IACv4H,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,YAAY,QAAQ,aAAa;AAC/B,SAAK,SAAS;AACd,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,YAAY,OAAO;AACjB,WAAO,KAAK,YAAY,yBAAyB,EAAE,KAAK,IAAI,mBAAiB;AAC3E,UAAI,eAAe;AACjB,aAAK,OAAO,SAAS,CAAC,GAAG,CAAC;AAAA,MAC5B;AACA,aAAO,CAAC;AAAA,IACV,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mBAAmB,mBAAmB;AACzD,aAAO,KAAK,qBAAqB,aAAe,SAAc,MAAM,GAAM,SAAY,WAAW,CAAC;AAAA,IACpG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,YAAW;AAAA,MACpB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,cAAc,CAAC;AAAA,EACnB,MAAM;AAAA,EACN,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa,CAAC,UAAU;AAC1B,CAAC;AACD,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,aAAO,KAAK,qBAAqB,cAAa;AAAA,IAChD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,cAAc;AAAA,MAC7B,SAAS,CAAC,cAAmB,YAAY;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,cAAc,aAAa,SAAS,WAAW,CAAC;AAAA,IAC5D,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,aAAa,SAAS,WAAW,CAAC;AAAA,MAC1D,SAAS,CAAC;AAAA,MACV,cAAc,CAAC,cAAc;AAAA,IAC/B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}