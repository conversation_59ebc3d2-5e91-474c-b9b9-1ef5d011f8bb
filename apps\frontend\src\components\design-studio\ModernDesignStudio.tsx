import { $, component$, QRL, useSignal, useTask$ } from '@qwik.dev/core';
import type { Decal } from '~/generated/graphql-shop';
import { getAllDecals } from '~/providers/shop/decals/decals';
import DesignCanvas from './DesignCanvas';
import type { DesignState, PlacedDecal, SavedDesign } from './types/design-types';
import { DESIGN_AREAS } from './types/design-types';

interface ModernDesignStudioProps {
	productVariant: any;
	onDesignComplete$: QRL<(design: SavedDesign) => void>;
	onClose$: QRL<() => void>;
}

export default component$<ModernDesignStudioProps>(
	({ productVariant, onDesignComplete$, onClose$ }) => {
		const designState = useSignal<DesignState>({
			selectedArea: 'front',
			decals: [],
			productVariant,
		});

		const selectedDecal = useSignal<PlacedDecal | null>(null);
		const availableDecals = useSignal<Decal[]>([]);
		const selectedCategory = useSignal<string>('all');
		const searchTerm = useSignal<string>('');
		const isLoading = useSignal<boolean>(true);
		const error = useSignal<string>('');

		// Load decals on component mount
		useTask$(async () => {
			try {
				isLoading.value = true;
				error.value = '';
				const decalsResult = await getAllDecals();
				availableDecals.value = decalsResult.items;
			} catch (err) {
				error.value = err instanceof Error ? err.message : 'Failed to load decals';
				console.error('Error loading decals:', err);
			} finally {
				isLoading.value = false;
			}
		});

		// Get unique categories
		const categories = availableDecals.value.reduce((cats: string[], decal) => {
			if (!cats.includes(decal.category)) {
				cats.push(decal.category);
			}
			return cats;
		}, []);

		// Filter decals based on category and search
		const filteredDecals = availableDecals.value.filter((decal) => {
			const matchesCategory =
				selectedCategory.value === 'all' || decal.category === selectedCategory.value;
			const matchesSearch =
				!searchTerm.value.trim() ||
				decal.name.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
				decal.description.toLowerCase().includes(searchTerm.value.toLowerCase());
			return matchesCategory && matchesSearch;
		});

		const handleDecalSelect = $((decal: Decal) => {
			const newDecal: PlacedDecal = {
				id: `decal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
				decalId: decal.id,
				area: designState.value.selectedArea,
				position: { x: 200, y: 250 },
				scale: 1.0,
				rotation: 0,
				zIndex: designState.value.decals.filter((d) => d.area === designState.value.selectedArea)
					.length,
			};

			designState.value = {
				...designState.value,
				decals: [...designState.value.decals, newDecal],
			};

			selectedDecal.value = newDecal;
		});

		const handleDecalUpdate = $((updatedDecal: PlacedDecal) => {
			designState.value = {
				...designState.value,
				decals: designState.value.decals.map((decal) =>
					decal.id === updatedDecal.id ? updatedDecal : decal
				),
			};

			if (selectedDecal.value?.id === updatedDecal.id) {
				selectedDecal.value = updatedDecal;
			}
		});

		const handleDecalDelete = $((decalId: string) => {
			designState.value = {
				...designState.value,
				decals: designState.value.decals.filter((decal) => decal.id !== decalId),
			};

			if (selectedDecal.value?.id === decalId) {
				selectedDecal.value = null;
			}
		});

		const handleSaveDesign = $(() => {
			const savedDesign: SavedDesign = {
				version: '1.0',
				productVariantId: productVariant.id,
				areas: {
					front: { decals: designState.value.decals.filter((d) => d.area === 'front') },
					back: { decals: designState.value.decals.filter((d) => d.area === 'back') },
					leftSleeve: { decals: designState.value.decals.filter((d) => d.area === 'leftSleeve') },
					rightSleeve: { decals: designState.value.decals.filter((d) => d.area === 'rightSleeve') },
				},
				metadata: {
					createdAt: new Date().toISOString(),
					totalDecals: designState.value.decals.length,
					previewUrls: [],
				},
			};

			onDesignComplete$(savedDesign);
		});

		const getTotalDecalsInArea = (area: string) => {
			return designState.value.decals.filter((d) => d.area === area).length;
		};

		if (error.value) {
			return (
				<div class="design-studio fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4">
					<div class="bg-gray-900 rounded-lg p-8 max-w-md w-full">
						<h2 class="text-2xl font-bold text-white mb-4">Error Loading Design Studio</h2>
						<p class="text-red-400 mb-6">{error.value}</p>
						<div class="flex gap-3">
							<button
								class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors"
								onClick$={async () => {
									try {
										isLoading.value = true;
										error.value = '';
										const decalsResult = await getAllDecals();
										availableDecals.value = decalsResult.items;
									} catch (err) {
										error.value = err instanceof Error ? err.message : 'Failed to load decals';
									} finally {
										isLoading.value = false;
									}
								}}
							>
								Retry
							</button>
							<button
								class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
								onClick$={onClose$}
							>
								Close
							</button>
						</div>
					</div>
				</div>
			);
		}

		return (
			<div class="design-studio fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4">
				<div class="design-studio-container bg-gray-900 rounded-lg max-w-7xl w-full max-h-full overflow-hidden flex flex-col">
					{/* Header */}
					<div class="flex justify-between items-center p-6 border-b border-gray-700">
						<div>
							<h2 class="text-2xl font-bold text-white">Modern Design Studio</h2>
							<p class="text-gray-400">Customize your {productVariant.name}</p>
						</div>
						<div class="flex gap-3">
							<button
								type="button"
								class="bg-primary hover:bg-secondary text-white px-6 py-2 rounded transition-colors"
								onClick$={handleSaveDesign}
							>
								Save Design
							</button>
							<button
								type="button"
								class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
								onClick$={onClose$}
							>
								Close
							</button>
						</div>
					</div>

					{/* Main Content */}
					<div class="flex-1 flex overflow-hidden">
						{/* Left Panel - Decal Library */}
						<div class="w-80 p-4 border-r border-gray-700 overflow-y-auto">
							<div class="decal-library bg-gray-800 p-4 rounded-lg">
								<h3 class="text-white font-bold mb-4 text-lg">Available Decals</h3>

								{/* Search and Filter Controls */}
								<div class="mb-4 space-y-3">
									<input
										type="text"
										placeholder="Search decals..."
										class="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-primary focus:outline-none"
										value={searchTerm.value}
										onInput$={(e) => {
											searchTerm.value = (e.target as HTMLInputElement).value;
										}}
										disabled={isLoading.value}
									/>

									<select
										class="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-primary focus:outline-none"
										value={selectedCategory.value}
										onChange$={(e) => {
											selectedCategory.value = (e.target as HTMLSelectElement).value;
										}}
										disabled={isLoading.value}
									>
										<option value="all">All Categories</option>
										{categories.map((category) => (
											<option key={category} value={category}>
												{category.charAt(0).toUpperCase() + category.slice(1)}
											</option>
										))}
									</select>
								</div>

								{/* Loading State */}
								{isLoading.value && (
									<div class="text-center text-gray-400 py-8">
										<div class="animate-spin inline-block w-8 h-8 border-4 border-current border-t-transparent rounded-full mb-4"></div>
										<p>Loading decals...</p>
									</div>
								)}

								{/* Decal Grid */}
								{!isLoading.value && (
									<div class="grid grid-cols-2 gap-3 max-h-96 overflow-y-auto">
										{filteredDecals.length === 0 ? (
											<div class="col-span-2 text-center text-gray-400 py-8">
												<p>No decals found</p>
											</div>
										) : (
											filteredDecals.map((decal) => (
												<div
													key={decal.id}
													class="decal-item bg-gray-700 rounded-lg p-3 cursor-pointer hover:bg-gray-600 transition-colors"
													onClick$={() => handleDecalSelect(decal)}
												>
													<div class="aspect-square bg-white rounded mb-2 flex items-center justify-center overflow-hidden">
														<img
															src={decal.asset.preview}
															alt={decal.name}
															class="max-w-full max-h-full object-contain"
															loading="lazy"
															width="80"
															height="80"
														/>
													</div>
													<div class="text-white text-sm font-medium truncate" title={decal.name}>
														{decal.name}
													</div>
													<div class="text-gray-400 text-xs truncate" title={decal.description}>
														{decal.description}
													</div>
													<div class="text-gray-500 text-xs mt-1">{decal.category}</div>
												</div>
											))
										)}
									</div>
								)}

								{/* Results Count */}
								{!isLoading.value && filteredDecals.length > 0 && (
									<div class="mt-3 text-center text-gray-400 text-sm">
										{filteredDecals.length} decal{filteredDecals.length !== 1 ? 's' : ''} available
									</div>
								)}
							</div>
						</div>

						{/* Center Panel - Design Canvas */}
						<div class="flex-1 p-4 overflow-y-auto">
							<DesignCanvas
								designState={designState}
								onDecalUpdate$={handleDecalUpdate}
								onDecalSelect$={(decal: PlacedDecal | null) => {
									selectedDecal.value = decal;
								}}
								selectedDecal={selectedDecal}
								availableDecals={availableDecals.value}
							/>
						</div>

						{/* Right Panel - Design Controls */}
						<div class="w-80 p-4 border-l border-gray-700 overflow-y-auto">
							{/* Selected Decal Controls */}
							{selectedDecal.value && (
								<div class="bg-gray-800 p-4 rounded-lg mb-4">
									<h4 class="text-white font-bold mb-3">Selected Decal</h4>
									<div class="space-y-3">
										<div>
											<label class="text-gray-300 text-sm block mb-1">Scale</label>
											<input
												type="range"
												min="0.5"
												max="2"
												step="0.1"
												value={selectedDecal.value.scale}
												class="w-full"
												onInput$={(e) => {
													if (selectedDecal.value) {
														const updatedDecal = {
															...selectedDecal.value,
															scale: parseFloat((e.target as HTMLInputElement).value),
														};
														handleDecalUpdate(updatedDecal);
													}
												}}
											/>
											<span class="text-gray-400 text-xs">
												{selectedDecal.value.scale.toFixed(1)}x
											</span>
										</div>

										<div>
											<label class="text-gray-300 text-sm block mb-1">Rotation</label>
											<input
												type="range"
												min="0"
												max="360"
												step="5"
												value={selectedDecal.value.rotation}
												class="w-full"
												onInput$={(e) => {
													if (selectedDecal.value) {
														const updatedDecal = {
															...selectedDecal.value,
															rotation: parseInt((e.target as HTMLInputElement).value),
														};
														handleDecalUpdate(updatedDecal);
													}
												}}
											/>
											<span class="text-gray-400 text-xs">{selectedDecal.value.rotation}°</span>
										</div>

										<button
											class="w-full bg-red-600 hover:bg-red-700 text-white py-2 rounded transition-colors"
											onClick$={() => handleDecalDelete(selectedDecal.value!.id)}
										>
											Delete Decal
										</button>
									</div>
								</div>
							)}

							{/* Design Summary */}
							<div class="bg-gray-800 p-4 rounded-lg">
								<h4 class="text-white font-bold mb-3">Design Summary</h4>
								<div class="space-y-2 text-sm">
									{Object.entries(DESIGN_AREAS).map(([area, label]) => (
										<div key={area} class="flex justify-between text-gray-300">
											<span>{label}:</span>
											<span>{getTotalDecalsInArea(area)} decals</span>
										</div>
									))}
									<div class="pt-2 border-t border-gray-700">
										<div class="flex justify-between text-white font-medium">
											<span>Total:</span>
											<span>{designState.value.decals.length} decals</span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
);
