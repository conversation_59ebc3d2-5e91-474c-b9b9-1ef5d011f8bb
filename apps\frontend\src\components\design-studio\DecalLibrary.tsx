import { component$, QRL, useComputed$, useSignal, useTask$ } from '@qwik.dev/core';
import type { Decal } from '~/generated/graphql-shop';
import { getAllDecals, getDecalCategories } from '~/providers/shop/decals/decals';

interface DecalLibraryProps {
	onDecalSelect$: QRL<(decal: Decal) => void>;
}

export default component$<DecalLibraryProps>(({ onDecalSelect$ }) => {
	const selectedCategory = useSignal<string>('all');
	const searchTerm = useSignal<string>('');
	const decals = useSignal<Decal[]>([]);
	const categories = useSignal<string[]>([]);
	const isLoading = useSignal<boolean>(true);
	const error = useSignal<string>('');

	// Load decals and categories on component mount
	useTask$(async () => {
		try {
			isLoading.value = true;
			error.value = '';

			const [decalsResult, categoriesResult] = await Promise.all([
				getAllDecals(),
				getDecalCategories(),
			]);

			decals.value = decalsResult.items;
			categories.value = categoriesResult;
		} catch (err) {
			error.value = err instanceof Error ? err.message : 'Failed to load decals';
			console.error('Error loading decals:', err);
		} finally {
			isLoading.value = false;
		}
	});

	// Compute filtered decals based on category and search
	const filteredDecals = useComputed$(() => {
		let filtered = decals.value;

		// Filter by category
		if (selectedCategory.value !== 'all') {
			filtered = filtered.filter((decal) => decal.category === selectedCategory.value);
		}

		// Apply search filter
		if (searchTerm.value.trim()) {
			const search = searchTerm.value.toLowerCase();
			filtered = filtered.filter(
				(decal) =>
					decal.name.toLowerCase().includes(search) ||
					decal.description.toLowerCase().includes(search) ||
					decal.category.toLowerCase().includes(search)
			);
		}

		return filtered;
	});

	if (error.value) {
		return (
			<div class="decal-library bg-gray-800 p-4 rounded-lg">
				<h3 class="text-white font-bold mb-4 text-lg">Available Decals</h3>
				<div class="text-center text-red-400 py-8">
					<p>Error loading decals: {error.value}</p>
					<button
						class="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
						onClick$={async () => {
							try {
								isLoading.value = true;
								error.value = '';
								const [decalsResult, categoriesResult] = await Promise.all([
									getAllDecals(),
									getDecalCategories(),
								]);
								decals.value = decalsResult.items;
								categories.value = categoriesResult;
							} catch (err) {
								error.value = err instanceof Error ? err.message : 'Failed to load decals';
							} finally {
								isLoading.value = false;
							}
						}}
					>
						Retry
					</button>
				</div>
			</div>
		);
	}

	return (
		<div class="decal-library bg-gray-800 p-4 rounded-lg">
			<h3 class="text-white font-bold mb-4 text-lg">Available Decals</h3>

			{/* Search and Filter Controls */}
			<div class="mb-4 space-y-3">
				{/* Search Input */}
				<div>
					<input
						type="text"
						placeholder="Search decals..."
						class="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-primary focus:outline-none"
						value={searchTerm.value}
						onInput$={(e) => {
							searchTerm.value = (e.target as HTMLInputElement).value;
						}}
						disabled={isLoading.value}
					/>
				</div>

				{/* Category Filter */}
				<div>
					<select
						class="w-full px-3 py-2 bg-gray-700 text-white rounded border border-gray-600 focus:border-primary focus:outline-none"
						value={selectedCategory.value}
						onChange$={(e) => {
							selectedCategory.value = (e.target as HTMLSelectElement).value;
						}}
						disabled={isLoading.value}
					>
						<option value="all">All Categories</option>
						{categories.value.map((category) => (
							<option key={category} value={category}>
								{category.charAt(0).toUpperCase() + category.slice(1)}
							</option>
						))}
					</select>
				</div>
			</div>

			{/* Loading State */}
			{isLoading.value && (
				<div class="text-center text-gray-400 py-8">
					<div class="animate-spin inline-block w-8 h-8 border-4 border-current border-t-transparent rounded-full mb-4"></div>
					<p>Loading decals...</p>
				</div>
			)}

			{/* Decal Grid */}
			{!isLoading.value && (
				<div class="grid grid-cols-2 gap-3 max-h-96 overflow-y-auto">
					{filteredDecals.value.length === 0 ? (
						<div class="col-span-2 text-center text-gray-400 py-8">
							<p>No decals found</p>
							{searchTerm.value && <p class="text-sm mt-1">Try adjusting your search or filter</p>}
						</div>
					) : (
						filteredDecals.value.map((decal) => (
							<div
								key={decal.id}
								class="decal-item bg-gray-700 rounded-lg p-3 cursor-pointer hover:bg-gray-600 transition-colors"
								onClick$={() => onDecalSelect$(decal)}
							>
								<div class="aspect-square bg-white rounded mb-2 flex items-center justify-center overflow-hidden">
									<img
										src={decal.asset.preview}
										alt={decal.name}
										class="max-w-full max-h-full object-contain"
										loading="lazy"
										width="80"
										height="80"
									/>
								</div>
								<div class="text-white text-sm font-medium truncate" title={decal.name}>
									{decal.name}
								</div>
								<div class="text-gray-400 text-xs truncate" title={decal.description}>
									{decal.description}
								</div>
								<div class="text-gray-500 text-xs mt-1">{decal.category}</div>
							</div>
						))
					)}
				</div>
			)}

			{/* Results Count */}
			{!isLoading.value && filteredDecals.value.length > 0 && (
				<div class="mt-3 text-center text-gray-400 text-sm">
					{filteredDecals.value.length} decal{filteredDecals.value.length !== 1 ? 's' : ''}{' '}
					available
				</div>
			)}
		</div>
	);
});
