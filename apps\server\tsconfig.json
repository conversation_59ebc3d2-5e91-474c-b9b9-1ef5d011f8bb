{"compilerOptions": {"module": "nodenext", "moduleResolution": "nodenext", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "target": "es2019", "strict": true, "sourceMap": false, "skipLibCheck": true, "outDir": "./dist", "baseUrl": "./"}, "exclude": ["node_modules", "migration.ts", "src/plugins/**/ui/*", "src/plugins/**/dashboard/*", "admin-ui", "vite.*.*ts"], "ts-node": {"files": true}}