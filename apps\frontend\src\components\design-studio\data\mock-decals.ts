import type { Decal } from '../types/design-types';

/**
 * Static mock decal data for the Design Studio
 * This replaces the API calls and provides a consistent set of decals for testing
 */
export const MOCK_DECALS: Decal[] = [
	{
		id: 'decal_star_001',
		name: 'Cool Star',
		description: 'A stylish star design perfect for any t-shirt',
		asset: {
			id: 'asset_star_001',
			preview: '/images/decals/star.svg',
		},
		category: 'shapes',
		maxWidth: 100,
		maxHeight: 100,
		minScale: 0.5,
		maxScale: 2.0,
	},
	{
		id: 'decal_lightning_001',
		name: 'Lightning Bolt',
		description: 'Electric lightning design with dynamic energy',
		asset: {
			id: 'asset_lightning_001',
			preview: '/images/decals/lightning.svg',
		},
		category: 'symbols',
		maxWidth: 80,
		maxHeight: 120,
		minScale: 0.5,
		maxScale: 2.0,
	},
	{
		id: 'decal_heart_001',
		name: 'Heart',
		description: 'Classic heart shape for romantic designs',
		asset: {
			id: 'asset_heart_001',
			preview: '/images/decals/heart.svg',
		},
		category: 'shapes',
		maxWidth: 90,
		maxHeight: 90,
		minScale: 0.5,
		maxScale: 2.0,
	},
	{
		id: 'decal_skull_001',
		name: 'Skull',
		description: 'Edgy skull design for bold statements',
		asset: {
			id: 'asset_skull_001',
			preview: '/images/decals/skull.svg',
		},
		category: 'symbols',
		maxWidth: 100,
		maxHeight: 110,
		minScale: 0.5,
		maxScale: 2.0,
	},
	{
		id: 'decal_circle_001',
		name: 'Circle',
		description: 'Simple circle shape for minimalist designs',
		asset: {
			id: 'asset_circle_001',
			preview: '/images/decals/circle.svg',
		},
		category: 'shapes',
		maxWidth: 80,
		maxHeight: 80,
		minScale: 0.3,
		maxScale: 2.5,
	},
	{
		id: 'decal_triangle_001',
		name: 'Triangle',
		description: 'Geometric triangle for modern looks',
		asset: {
			id: 'asset_triangle_001',
			preview: '/images/decals/triangle.svg',
		},
		category: 'shapes',
		maxWidth: 90,
		maxHeight: 90,
		minScale: 0.4,
		maxScale: 2.0,
	},
	{
		id: 'decal_music_001',
		name: 'Music Note',
		description: 'Musical note for music lovers',
		asset: {
			id: 'asset_music_001',
			preview: '/images/decals/music-note.svg',
		},
		category: 'symbols',
		maxWidth: 70,
		maxHeight: 100,
		minScale: 0.5,
		maxScale: 2.0,
	},
	{
		id: 'decal_flower_001',
		name: 'Flower',
		description: 'Beautiful flower design for nature themes',
		asset: {
			id: 'asset_flower_001',
			preview: '/images/decals/flower.svg',
		},
		category: 'nature',
		maxWidth: 95,
		maxHeight: 95,
		minScale: 0.5,
		maxScale: 2.0,
	},
];

/**
 * Get all available decals
 */
export const getAllDecals = (): Decal[] => {
	return MOCK_DECALS;
};

/**
 * Get decals filtered by category
 */
export const getDecalsByCategory = (category: string): Decal[] => {
	return MOCK_DECALS.filter((decal) => decal.category === category);
};

/**
 * Get all unique categories
 */
export const getDecalCategories = (): string[] => {
	const categories = MOCK_DECALS.map((decal) => decal.category);
	return [...new Set(categories)];
};

/**
 * Get a specific decal by ID
 */
export const getDecalById = (id: string): Decal | undefined => {
	return MOCK_DECALS.find((decal) => decal.id === id);
};
