import {
  A11yModule,
  ActiveDescendant<PERSON>eyManager,
  AriaDescriber,
  CDK_DESCRIBEDBY_HOST_ATTRIBUTE,
  CDK_DESCRIBEDBY_ID_PREFIX,
  CdkAriaLive,
  CdkMonitorFocus,
  CdkTrapFocus,
  ConfigurableFocusTrap,
  ConfigurableFocusTrapFactory,
  EventListenerFocusTrapInertStrategy,
  FOCUS_MONITOR_DEFAULT_OPTIONS,
  FOCUS_TRAP_INERT_STRATEGY,
  FocusKeyManager,
  FocusMonitor,
  FocusMonitorDetectionMode,
  FocusTrap,
  FocusTrapFactory,
  HighContrastMode,
  HighContrastModeDetector,
  INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,
  INPUT_MODALITY_DETECTOR_OPTIONS,
  InputModalityDetector,
  InteractivityChecker,
  IsFocusableConfig,
  LIVE_ANNOUNCER_DEFAULT_OPTIONS,
  LIVE_ANNOUNCER_ELEMENT_TOKEN,
  LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,
  List<PERSON>eyManager,
  LiveAnnouncer,
  MESSAGES_CONTAINER_ID,
  NOOP_TREE_KEY_MANAGER_FACTORY,
  NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER,
  NoopTreeKeyManager,
  TREE_KEY_MANAGER,
  TREE_KEY_MANAGER_FACTORY,
  TREE_KEY_MANAGER_FACTORY_PROVIDER,
  TreeKeyManager,
  addAriaReferencedId,
  getAriaReferenceIds,
  removeAriaReferencedId
} from "./chunk-AH4V4Y5P.js";
import {
  isFakeMousedownFromScreenReader,
  isFakeTouchstartFromScreenReader
} from "./chunk-D2IIRJOA.js";
import "./chunk-KPGQWKW7.js";
import {
  _IdGenerator
} from "./chunk-6KLBFRQL.js";
import "./chunk-4PUVEEMI.js";
import "./chunk-LUYNUHYL.js";
import "./chunk-TXDUYLVM.js";
export {
  A11yModule,
  ActiveDescendantKeyManager,
  AriaDescriber,
  CDK_DESCRIBEDBY_HOST_ATTRIBUTE,
  CDK_DESCRIBEDBY_ID_PREFIX,
  CdkAriaLive,
  CdkMonitorFocus,
  CdkTrapFocus,
  ConfigurableFocusTrap,
  ConfigurableFocusTrapFactory,
  EventListenerFocusTrapInertStrategy,
  FOCUS_MONITOR_DEFAULT_OPTIONS,
  FOCUS_TRAP_INERT_STRATEGY,
  FocusKeyManager,
  FocusMonitor,
  FocusMonitorDetectionMode,
  FocusTrap,
  FocusTrapFactory,
  HighContrastMode,
  HighContrastModeDetector,
  INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,
  INPUT_MODALITY_DETECTOR_OPTIONS,
  InputModalityDetector,
  InteractivityChecker,
  IsFocusableConfig,
  LIVE_ANNOUNCER_DEFAULT_OPTIONS,
  LIVE_ANNOUNCER_ELEMENT_TOKEN,
  LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,
  ListKeyManager,
  LiveAnnouncer,
  MESSAGES_CONTAINER_ID,
  NOOP_TREE_KEY_MANAGER_FACTORY,
  NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER,
  NoopTreeKeyManager,
  TREE_KEY_MANAGER,
  TREE_KEY_MANAGER_FACTORY,
  TREE_KEY_MANAGER_FACTORY_PROVIDER,
  TreeKeyManager,
  _IdGenerator,
  addAriaReferencedId,
  getAriaReferenceIds,
  isFakeMousedownFromScreenReader,
  isFakeTouchstartFromScreenReader,
  removeAriaReferencedId
};
