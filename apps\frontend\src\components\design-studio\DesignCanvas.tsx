import { $, component$, QRL, Signal, useSignal, useVisibleTask$ } from '@qwik.dev/core';
import type { DesignArea, DesignState, PlacedDecal } from './types/design-types';
import { DESIGN_AREAS } from './types/design-types';

interface DesignCanvasProps {
	designState: Signal<DesignState>;
	onDecalUpdate$: QRL<(decal: PlacedDecal) => void>;
	onDecalSelect$: QRL<(decal: PlacedDecal | null) => void>;
	selectedDecal: Signal<PlacedDecal | null>;
	availableDecals?: any[];
}

export default component$<DesignCanvasProps>(
	({ designState, onDecalUpdate$, onDecalSelect$, selectedDecal, availableDecals = [] }) => {
		const canvasRef = useSignal<HTMLCanvasElement>();
		const isDragging = useSignal(false);
		const dragOffset = useSignal({ x: 0, y: 0 });
		const decalCache = useSignal<Record<string, any>>({});

		// Canvas dimensions
		const CANVAS_WIDTH = 400;
		const CANVAS_HEIGHT = 500;

		// Printable areas for different t-shirt sections
		const PRINTABLE_AREAS: Record<
			DesignArea,
			{ x: number; y: number; width: number; height: number }
		> = {
			front: { x: 100, y: 150, width: 200, height: 250 },
			back: { x: 100, y: 150, width: 200, height: 250 },
			leftSleeve: { x: 50, y: 100, width: 80, height: 120 },
			rightSleeve: { x: 270, y: 100, width: 80, height: 120 },
		};

		const renderTShirtTemplate = $((ctx: CanvasRenderingContext2D, area: DesignArea) => {
			// Clear canvas
			ctx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

			// Set canvas background
			ctx.fillStyle = '#f8f9fa';
			ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

			// Draw t-shirt outline based on selected area
			ctx.strokeStyle = '#666';
			ctx.lineWidth = 2;
			ctx.fillStyle = '#ffffff';

			if (area === 'front' || area === 'back') {
				// Main body
				ctx.fillRect(80, 120, 240, 300);
				ctx.strokeRect(80, 120, 240, 300);
				// Sleeves
				ctx.fillRect(40, 140, 40, 100);
				ctx.strokeRect(40, 140, 40, 100);
				ctx.fillRect(320, 140, 40, 100);
				ctx.strokeRect(320, 140, 40, 100);
				// Neck
				ctx.fillRect(160, 120, 80, 30);
				ctx.strokeRect(160, 120, 80, 30);
			} else if (area === 'leftSleeve') {
				// Left sleeve focus
				ctx.fillRect(40, 100, 80, 120);
				ctx.strokeRect(40, 100, 80, 120);
			} else if (area === 'rightSleeve') {
				// Right sleeve focus
				ctx.fillRect(280, 100, 80, 120);
				ctx.strokeRect(280, 100, 80, 120);
			}

			// Draw printable area
			const printArea = PRINTABLE_AREAS[area];
			ctx.strokeStyle = '#00ff00';
			ctx.lineWidth = 2;
			ctx.setLineDash([5, 5]);
			ctx.strokeRect(printArea.x, printArea.y, printArea.width, printArea.height);
			ctx.setLineDash([]);

			// Add area label
			ctx.fillStyle = '#333';
			ctx.font = '14px Arial';
			ctx.textAlign = 'center';
			ctx.fillText(DESIGN_AREAS[area], printArea.x + printArea.width / 2, printArea.y - 10);
		});

		const renderDecal = $((ctx: CanvasRenderingContext2D, decal: PlacedDecal, decalData?: any) => {
			// Calculate decal size based on scale
			const size = 50 * decal.scale;
			const halfSize = size / 2;

			// Save context for transformations
			ctx.save();

			// Apply transformations
			ctx.translate(decal.position.x, decal.position.y);
			ctx.rotate((decal.rotation * Math.PI) / 180);

			// Draw decal placeholder (in a real implementation, this would load and draw the actual image)
			ctx.fillStyle = selectedDecal.value?.id === decal.id ? '#ff6b6b' : '#4ecdc4';
			ctx.fillRect(-halfSize, -halfSize, size, size);

			// Add decal name if available
			if (decalData?.name) {
				ctx.fillStyle = '#fff';
				ctx.font = '10px Arial';
				ctx.textAlign = 'center';
				ctx.fillText(decalData.name, 0, 0);
			}

			// Restore context
			ctx.restore();

			// Draw selection border if selected
			if (selectedDecal.value?.id === decal.id) {
				ctx.strokeStyle = '#ff6b6b';
				ctx.lineWidth = 2;
				ctx.strokeRect(
					decal.position.x - halfSize - 2,
					decal.position.y - halfSize - 2,
					size + 4,
					size + 4
				);

				// Draw resize handles
				const handleSize = 8;
				ctx.fillStyle = '#ff6b6b';
				ctx.fillRect(
					decal.position.x + halfSize - handleSize / 2,
					decal.position.y + halfSize - handleSize / 2,
					handleSize,
					handleSize
				);
			}
		});

		const renderDesign = $(() => {
			const canvas = canvasRef.value;
			const ctx = canvas?.getContext('2d');
			if (!ctx) return;

			// Render t-shirt template
			renderTShirtTemplate(ctx, designState.value.selectedArea);

			// Render placed decals for current area
			const decalsInArea = designState.value.decals
				.filter((decal) => decal.area === designState.value.selectedArea)
				.sort((a, b) => a.zIndex - b.zIndex);

			// Render each decal with cached data if available
			decalsInArea.forEach((decal) => {
				const decalData = decalCache.value[decal.decalId];
				renderDecal(ctx, decal, decalData);
			});
		});

		const handleMouseDown = $((e: MouseEvent) => {
			const canvas = canvasRef.value;
			if (!canvas) return;

			const rect = canvas.getBoundingClientRect();
			const pos = {
				x: e.clientX - rect.left,
				y: e.clientY - rect.top,
			};

			// Find clicked decal inline to avoid serialization issues
			const decalsInArea = designState.value.decals
				.filter((decal) => decal.area === designState.value.selectedArea)
				.sort((a, b) => b.zIndex - a.zIndex);

			let clickedDecal: PlacedDecal | null = null;
			for (const decal of decalsInArea) {
				const size = 50 * decal.scale;
				const halfSize = size / 2;

				if (
					pos.x >= decal.position.x - halfSize &&
					pos.x <= decal.position.x + halfSize &&
					pos.y >= decal.position.y - halfSize &&
					pos.y <= decal.position.y + halfSize
				) {
					clickedDecal = decal;
					break;
				}
			}

			if (clickedDecal) {
				onDecalSelect$(clickedDecal);
				isDragging.value = true;
				dragOffset.value = {
					x: pos.x - clickedDecal.position.x,
					y: pos.y - clickedDecal.position.y,
				};
			} else {
				onDecalSelect$(null);
			}
		});

		const handleMouseMove = $((e: MouseEvent) => {
			if (!isDragging.value || !selectedDecal.value) return;

			const canvas = canvasRef.value;
			if (!canvas) return;

			const rect = canvas.getBoundingClientRect();
			const pos = {
				x: e.clientX - rect.left,
				y: e.clientY - rect.top,
			};

			const newPosition = {
				x: pos.x - dragOffset.value.x,
				y: pos.y - dragOffset.value.y,
			};

			// Keep decal within printable area
			const printArea = PRINTABLE_AREAS[designState.value.selectedArea];
			const size = 50 * selectedDecal.value.scale;
			const halfSize = size / 2;

			newPosition.x = Math.max(
				printArea.x + halfSize,
				Math.min(printArea.x + printArea.width - halfSize, newPosition.x)
			);
			newPosition.y = Math.max(
				printArea.y + halfSize,
				Math.min(printArea.y + printArea.height - halfSize, newPosition.y)
			);

			const updatedDecal = {
				...selectedDecal.value,
				position: newPosition,
			};

			onDecalUpdate$(updatedDecal);
		});

		const handleMouseUp = $(() => {
			isDragging.value = false;
		});

		const handleAreaChange = $((area: DesignArea) => {
			designState.value = {
				...designState.value,
				selectedArea: area,
			};
			// Clear selection when switching areas
			onDecalSelect$(null);
		});

		// Update decal cache when available decals change
		useVisibleTask$(({ track }) => {
			track(() => availableDecals);
			// Populate decal cache
			const cache: Record<string, any> = {};
			availableDecals.forEach((decal) => {
				cache[decal.id] = decal;
			});
			decalCache.value = cache;
		});

		// Re-render when design state changes
		useVisibleTask$(({ track }) => {
			track(() => designState.value);
			track(() => selectedDecal.value);
			renderDesign();
		});

		return (
			<div class="design-canvas bg-gray-900 p-4 rounded-lg">
				<div class="mb-4">
					<h3 class="text-white font-bold text-lg mb-2">Design Canvas</h3>

					{/* Area Selection Buttons */}
					<div class="flex gap-2 mb-4">
						{(Object.keys(DESIGN_AREAS) as DesignArea[]).map((area) => (
							<button
								key={area}
								type="button"
								class={`px-3 py-1 rounded text-sm transition-colors ${
									designState.value.selectedArea === area
										? 'bg-primary text-white'
										: 'bg-gray-700 text-gray-300 hover:bg-gray-600'
								}`}
								onClick$={() => handleAreaChange(area)}
							>
								{DESIGN_AREAS[area]}
							</button>
						))}
					</div>
				</div>

				{/* Canvas */}
				<div class="flex justify-center">
					<canvas
						ref={canvasRef}
						width={CANVAS_WIDTH}
						height={CANVAS_HEIGHT}
						class="border border-gray-600 rounded cursor-crosshair"
						onMouseDown$={handleMouseDown}
						onMouseMove$={handleMouseMove}
						onMouseUp$={handleMouseUp}
						onMouseLeave$={handleMouseUp}
					/>
				</div>

				{/* Instructions */}
				<div class="mt-4 text-sm text-gray-400">
					<p>• Click and drag decals to reposition them</p>
					<p>• Green dashed area shows printable region</p>
					<p>• Use controls panel to adjust scale and rotation</p>
				</div>
			</div>
		);
	}
);
