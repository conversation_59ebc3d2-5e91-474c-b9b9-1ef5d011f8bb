{"version": 3, "sources": ["../../../../../../../node_modules/@ng-select/ng-select/fesm2022/ng-select-ng-select.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Input, Injectable, input, EventEmitter, booleanAttribute, ElementRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Inject, Output, ViewChild, InjectionToken, numberAttribute, forwardRef, TemplateRef, Attribute, HostBinding, ContentChild, ContentChildren, HostListener, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { animationFrameScheduler, asapScheduler, Subject, fromEvent, merge } from 'rxjs';\nimport { takeUntil, auditTime, startWith, tap, debounceTime, filter, map } from 'rxjs/operators';\nimport { DOCUMENT, NgTemplateOutlet, NgClass } from '@angular/common';\nconst _c0 = [\"content\"];\nconst _c1 = [\"scroll\"];\nconst _c2 = [\"padding\"];\nconst _c3 = [\"*\"];\nconst _c4 = a0 => ({\n  searchTerm: a0\n});\nfunction NgDropdownPanelComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelementContainer(1, 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r0.filterValue));\n  }\n}\nfunction NgDropdownPanelComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelementContainer(1, 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.footerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r0.filterValue));\n  }\n}\nconst _c5 = [\"searchInput\"];\nconst _c6 = [\"clearButton\"];\nconst _c7 = (a0, a1, a2) => ({\n  item: a0,\n  clear: a1,\n  label: a2\n});\nconst _c8 = (a0, a1) => ({\n  items: a0,\n  clear: a1\n});\nconst _c9 = (a0, a1, a2, a3) => ({\n  item: a0,\n  item$: a1,\n  index: a2,\n  searchTerm: a3\n});\nfunction NgSelectComponent_Conditional_2_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.placeholder);\n  }\n}\nfunction NgSelectComponent_Conditional_2_ng_template_2_Template(rf, ctx) {}\nfunction NgSelectComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NgSelectComponent_Conditional_2_ng_template_0_Template, 2, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(2, NgSelectComponent_Conditional_2_ng_template_2_Template, 0, 0, \"ng-template\", 19);\n  }\n  if (rf & 2) {\n    const defaultPlaceholderTemplate_r3 = i0.ɵɵreference(1);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.placeholderTemplate || defaultPlaceholderTemplate_r3);\n  }\n}\nfunction NgSelectComponent_Conditional_3_For_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵlistener(\"click\", function NgSelectComponent_Conditional_3_For_1_ng_template_1_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const item_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.unselect(item_r5));\n    });\n    i0.ɵɵtext(1, \"\\xD7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"span\", 24);\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngItemLabel\", item_r5.label)(\"escape\", ctx_r1.escapeHTML);\n  }\n}\nfunction NgSelectComponent_Conditional_3_For_1_ng_template_3_Template(rf, ctx) {}\nfunction NgSelectComponent_Conditional_3_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, NgSelectComponent_Conditional_3_For_1_ng_template_1_Template, 3, 2, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(3, NgSelectComponent_Conditional_3_For_1_ng_template_3_Template, 0, 0, \"ng-template\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const defaultLabelTemplate_r6 = i0.ɵɵreference(2);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"ng-value-disabled\", item_r5.disabled);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.labelTemplate || defaultLabelTemplate_r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(4, _c7, item_r5.value, ctx_r1.clearItem, item_r5.label));\n  }\n}\nfunction NgSelectComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, NgSelectComponent_Conditional_3_For_1_Template, 4, 8, \"div\", 21, i0.ɵɵcomponentInstance().trackByOption, true);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r1.selectedItems);\n  }\n}\nfunction NgSelectComponent_Conditional_4_ng_template_0_Template(rf, ctx) {}\nfunction NgSelectComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NgSelectComponent_Conditional_4_ng_template_0_Template, 0, 0, \"ng-template\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.multiLabelTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c8, ctx_r1.selectedValues, ctx_r1.clearItem));\n  }\n}\nfunction NgSelectComponent_Conditional_8_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 25);\n  }\n}\nfunction NgSelectComponent_Conditional_8_ng_template_2_Template(rf, ctx) {}\nfunction NgSelectComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NgSelectComponent_Conditional_8_ng_template_0_Template, 1, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(2, NgSelectComponent_Conditional_8_ng_template_2_Template, 0, 0, \"ng-template\", 19);\n  }\n  if (rf & 2) {\n    const defaultLoadingSpinnerTemplate_r8 = i0.ɵɵreference(1);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.loadingSpinnerTemplate || defaultLoadingSpinnerTemplate_r8);\n  }\n}\nfunction NgSelectComponent_Conditional_9_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.clearButtonTemplate);\n  }\n}\nfunction NgSelectComponent_Conditional_9_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26, 4)(2, \"span\", 27);\n    i0.ɵɵtext(3, \"\\xD7\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r1.clearAllText);\n    i0.ɵɵattribute(\"tabindex\", ctx_r1.tabFocusOnClearButton() ? 0 : -1);\n  }\n}\nfunction NgSelectComponent_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NgSelectComponent_Conditional_9_Conditional_0_Template, 1, 1, \"ng-container\", 19)(1, NgSelectComponent_Conditional_9_Conditional_1_Template, 4, 2, \"span\", 26);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r1.clearButtonTemplate ? 0 : 1);\n  }\n}\nfunction NgSelectComponent_Conditional_12_For_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 32);\n  }\n  if (rf & 2) {\n    const item_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngItemLabel\", item_r11.label)(\"escape\", ctx_r1.escapeHTML);\n  }\n}\nfunction NgSelectComponent_Conditional_12_For_3_ng_template_3_Template(rf, ctx) {}\nfunction NgSelectComponent_Conditional_12_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function NgSelectComponent_Conditional_12_For_3_Template_div_click_0_listener() {\n      const item_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleItem(item_r11));\n    })(\"mouseover\", function NgSelectComponent_Conditional_12_For_3_Template_div_mouseover_0_listener() {\n      const item_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onItemHover(item_r11));\n    });\n    i0.ɵɵtemplate(1, NgSelectComponent_Conditional_12_For_3_ng_template_1_Template, 1, 2, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor)(3, NgSelectComponent_Conditional_12_For_3_ng_template_3_Template, 0, 0, \"ng-template\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const defaultOptionTemplate_r12 = i0.ɵɵreference(2);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"ng-option-disabled\", item_r11.disabled)(\"ng-option-selected\", item_r11.selected)(\"ng-optgroup\", item_r11.children)(\"ng-option\", !item_r11.children)(\"ng-option-child\", !!item_r11.parent)(\"ng-option-marked\", item_r11 === ctx_r1.itemsList.markedItem);\n    i0.ɵɵattribute(\"role\", item_r11.children ? \"group\" : \"option\")(\"aria-selected\", item_r11.selected)(\"id\", item_r11 == null ? null : item_r11.htmlId)(\"aria-setsize\", ctx_r1.itemsList.filteredItems.length)(\"aria-posinset\", item_r11.index + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", item_r11.children ? ctx_r1.optgroupTemplate || defaultOptionTemplate_r12 : ctx_r1.optionTemplate || defaultOptionTemplate_r12)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction4(19, _c9, item_r11.value, item_r11, item_r11.index, ctx_r1.searchTerm));\n  }\n}\nfunction NgSelectComponent_Conditional_12_Conditional_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\")(1, \"span\", 34);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.addTagText);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\\"\", ctx_r1.searchTerm, \"\\\"\");\n  }\n}\nfunction NgSelectComponent_Conditional_12_Conditional_4_ng_template_3_Template(rf, ctx) {}\nfunction NgSelectComponent_Conditional_12_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵlistener(\"mouseover\", function NgSelectComponent_Conditional_12_Conditional_4_Template_div_mouseover_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.itemsList.unmarkItem());\n    })(\"click\", function NgSelectComponent_Conditional_12_Conditional_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectTag());\n    });\n    i0.ɵɵtemplate(1, NgSelectComponent_Conditional_12_Conditional_4_ng_template_1_Template, 4, 2, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor)(3, NgSelectComponent_Conditional_12_Conditional_4_ng_template_3_Template, 0, 0, \"ng-template\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const defaultTagTemplate_r14 = i0.ɵɵreference(2);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"ng-option-marked\", !ctx_r1.itemsList.markedItem);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.tagTemplate || defaultTagTemplate_r14)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c4, ctx_r1.searchTerm));\n  }\n}\nfunction NgSelectComponent_Conditional_12_Conditional_5_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.notFoundText);\n  }\n}\nfunction NgSelectComponent_Conditional_12_Conditional_5_ng_template_2_Template(rf, ctx) {}\nfunction NgSelectComponent_Conditional_12_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NgSelectComponent_Conditional_12_Conditional_5_ng_template_0_Template, 2, 1, \"ng-template\", null, 7, i0.ɵɵtemplateRefExtractor)(2, NgSelectComponent_Conditional_12_Conditional_5_ng_template_2_Template, 0, 0, \"ng-template\", 12);\n  }\n  if (rf & 2) {\n    const defaultNotFoundTemplate_r15 = i0.ɵɵreference(1);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.notFoundTemplate || defaultNotFoundTemplate_r15)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r1.searchTerm));\n  }\n}\nfunction NgSelectComponent_Conditional_12_Conditional_6_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.typeToSearchText);\n  }\n}\nfunction NgSelectComponent_Conditional_12_Conditional_6_ng_template_2_Template(rf, ctx) {}\nfunction NgSelectComponent_Conditional_12_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NgSelectComponent_Conditional_12_Conditional_6_ng_template_0_Template, 2, 1, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor)(2, NgSelectComponent_Conditional_12_Conditional_6_ng_template_2_Template, 0, 0, \"ng-template\", 19);\n  }\n  if (rf & 2) {\n    const defaultTypeToSearchTemplate_r16 = i0.ɵɵreference(1);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.typeToSearchTemplate || defaultTypeToSearchTemplate_r16);\n  }\n}\nfunction NgSelectComponent_Conditional_12_Conditional_7_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.loadingText);\n  }\n}\nfunction NgSelectComponent_Conditional_12_Conditional_7_ng_template_2_Template(rf, ctx) {}\nfunction NgSelectComponent_Conditional_12_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NgSelectComponent_Conditional_12_Conditional_7_ng_template_0_Template, 2, 1, \"ng-template\", null, 9, i0.ɵɵtemplateRefExtractor)(2, NgSelectComponent_Conditional_12_Conditional_7_ng_template_2_Template, 0, 0, \"ng-template\", 12);\n  }\n  if (rf & 2) {\n    const defaultLoadingTextTemplate_r17 = i0.ɵɵreference(1);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.loadingTextTemplate || defaultLoadingTextTemplate_r17)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r1.searchTerm));\n  }\n}\nfunction NgSelectComponent_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ng-dropdown-panel\", 28);\n    i0.ɵɵlistener(\"update\", function NgSelectComponent_Conditional_12_Template_ng_dropdown_panel_update_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.viewPortItems = $event);\n    })(\"scroll\", function NgSelectComponent_Conditional_12_Template_ng_dropdown_panel_scroll_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.scroll.emit($event));\n    })(\"scrollToEnd\", function NgSelectComponent_Conditional_12_Template_ng_dropdown_panel_scrollToEnd_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.scrollToEnd.emit($event));\n    })(\"outsideClick\", function NgSelectComponent_Conditional_12_Template_ng_dropdown_panel_outsideClick_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.close());\n    });\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵrepeaterCreate(2, NgSelectComponent_Conditional_12_For_3_Template, 4, 24, \"div\", 29, i0.ɵɵcomponentInstance().trackByOption, true);\n    i0.ɵɵtemplate(4, NgSelectComponent_Conditional_12_Conditional_4_Template, 4, 6, \"div\", 30);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(5, NgSelectComponent_Conditional_12_Conditional_5_Template, 3, 4)(6, NgSelectComponent_Conditional_12_Conditional_6_Template, 3, 1)(7, NgSelectComponent_Conditional_12_Conditional_7_Template, 3, 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"ng-select-multiple\", ctx_r1.multiple);\n    i0.ɵɵproperty(\"virtualScroll\", ctx_r1.virtualScroll)(\"bufferAmount\", ctx_r1.bufferAmount)(\"appendTo\", ctx_r1.appendTo)(\"position\", ctx_r1.dropdownPosition)(\"headerTemplate\", ctx_r1.headerTemplate)(\"footerTemplate\", ctx_r1.footerTemplate)(\"filterValue\", ctx_r1.searchTerm)(\"items\", ctx_r1.itemsList.filteredItems)(\"markedItem\", ctx_r1.itemsList.markedItem)(\"ngClass\", ctx_r1.appendTo ? ctx_r1.ngClass ? ctx_r1.ngClass : ctx_r1.classes : null)(\"id\", ctx_r1.dropdownId)(\"ariaLabelDropdown\", ctx_r1.ariaLabelDropdown);\n    i0.ɵɵadvance(2);\n    i0.ɵɵrepeater(ctx_r1.viewPortItems);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r1.showAddTag ? 4 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.showNoItemsFound() ? 5 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.showTypeToSearch() ? 6 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.loading && ctx_r1.itemsList.filteredItems.length === 0 ? 7 : -1);\n  }\n}\nfunction NgSelectComponent_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.notFoundText, \" \");\n  }\n}\nconst unescapedHTMLExp = /[&<>\"']/g;\nconst hasUnescapedHTMLExp = RegExp(unescapedHTMLExp.source);\nconst htmlEscapes = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;'\n};\nfunction escapeHTML(value) {\n  return value && hasUnescapedHTMLExp.test(value) ? value.replace(unescapedHTMLExp, chr => htmlEscapes[chr]) : value;\n}\nfunction isDefined(value) {\n  return value !== undefined && value !== null;\n}\nfunction isObject(value) {\n  return typeof value === 'object' && isDefined(value);\n}\nfunction isPromise(value) {\n  return value instanceof Promise;\n}\nfunction isFunction(value) {\n  return value instanceof Function;\n}\nclass NgItemLabelDirective {\n  constructor(element) {\n    this.element = element;\n    this.escape = true;\n  }\n  ngOnChanges(changes) {\n    this.element.nativeElement.innerHTML = this.escape ? escapeHTML(this.ngItemLabel) : this.ngItemLabel;\n  }\n  static {\n    this.ɵfac = function NgItemLabelDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgItemLabelDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgItemLabelDirective,\n      selectors: [[\"\", \"ngItemLabel\", \"\"]],\n      inputs: {\n        ngItemLabel: \"ngItemLabel\",\n        escape: \"escape\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgItemLabelDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngItemLabel]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    ngItemLabel: [{\n      type: Input\n    }],\n    escape: [{\n      type: Input\n    }]\n  });\n})();\nclass NgOptionTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgOptionTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgOptionTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgOptionTemplateDirective,\n      selectors: [[\"\", \"ng-option-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgOptionTemplateDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: '[ng-option-tmp]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass NgOptgroupTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgOptgroupTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgOptgroupTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgOptgroupTemplateDirective,\n      selectors: [[\"\", \"ng-optgroup-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgOptgroupTemplateDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: '[ng-optgroup-tmp]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass NgLabelTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgLabelTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgLabelTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgLabelTemplateDirective,\n      selectors: [[\"\", \"ng-label-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgLabelTemplateDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: '[ng-label-tmp]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass NgMultiLabelTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgMultiLabelTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgMultiLabelTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgMultiLabelTemplateDirective,\n      selectors: [[\"\", \"ng-multi-label-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgMultiLabelTemplateDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: '[ng-multi-label-tmp]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass NgHeaderTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgHeaderTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgHeaderTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgHeaderTemplateDirective,\n      selectors: [[\"\", \"ng-header-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgHeaderTemplateDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: '[ng-header-tmp]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass NgFooterTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgFooterTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgFooterTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgFooterTemplateDirective,\n      selectors: [[\"\", \"ng-footer-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgFooterTemplateDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: '[ng-footer-tmp]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass NgNotFoundTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgNotFoundTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgNotFoundTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgNotFoundTemplateDirective,\n      selectors: [[\"\", \"ng-notfound-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgNotFoundTemplateDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: '[ng-notfound-tmp]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass NgPlaceholderTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgPlaceholderTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgPlaceholderTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgPlaceholderTemplateDirective,\n      selectors: [[\"\", \"ng-placeholder-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgPlaceholderTemplateDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: '[ng-placeholder-tmp]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass NgTypeToSearchTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgTypeToSearchTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgTypeToSearchTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgTypeToSearchTemplateDirective,\n      selectors: [[\"\", \"ng-typetosearch-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgTypeToSearchTemplateDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: '[ng-typetosearch-tmp]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass NgLoadingTextTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgLoadingTextTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgLoadingTextTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgLoadingTextTemplateDirective,\n      selectors: [[\"\", \"ng-loadingtext-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgLoadingTextTemplateDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: '[ng-loadingtext-tmp]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass NgTagTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgTagTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgTagTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgTagTemplateDirective,\n      selectors: [[\"\", \"ng-tag-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgTagTemplateDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: '[ng-tag-tmp]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nclass NgLoadingSpinnerTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgLoadingSpinnerTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgLoadingSpinnerTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgLoadingSpinnerTemplateDirective,\n      selectors: [[\"\", \"ng-loadingspinner-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgLoadingSpinnerTemplateDirective, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/directive-selector\n      selector: '[ng-loadingspinner-tmp]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgClearButtonTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function NgClearButtonTemplateDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgClearButtonTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgClearButtonTemplateDirective,\n      selectors: [[\"\", \"ng-clearbutton-tmp\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgClearButtonTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-clearbutton-tmp]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\nfunction newId() {\n  // First character is an 'a', it's good practice to tag id to begin with a letter\n  return 'axxxxxxxxxxx'.replace(/[x]/g, () => {\n    // eslint-disable-next-line no-bitwise\n    const val = Math.random() * 16 | 0;\n    return val.toString(16);\n  });\n}\nconst diacritics = {\n  '\\u24B6': 'A',\n  '\\uFF21': 'A',\n  '\\u00C0': 'A',\n  '\\u00C1': 'A',\n  '\\u00C2': 'A',\n  '\\u1EA6': 'A',\n  '\\u1EA4': 'A',\n  '\\u1EAA': 'A',\n  '\\u1EA8': 'A',\n  '\\u00C3': 'A',\n  '\\u0100': 'A',\n  '\\u0102': 'A',\n  '\\u1EB0': 'A',\n  '\\u1EAE': 'A',\n  '\\u1EB4': 'A',\n  '\\u1EB2': 'A',\n  '\\u0226': 'A',\n  '\\u01E0': 'A',\n  '\\u00C4': 'A',\n  '\\u01DE': 'A',\n  '\\u1EA2': 'A',\n  '\\u00C5': 'A',\n  '\\u01FA': 'A',\n  '\\u01CD': 'A',\n  '\\u0200': 'A',\n  '\\u0202': 'A',\n  '\\u1EA0': 'A',\n  '\\u1EAC': 'A',\n  '\\u1EB6': 'A',\n  '\\u1E00': 'A',\n  '\\u0104': 'A',\n  '\\u023A': 'A',\n  '\\u2C6F': 'A',\n  '\\uA732': 'AA',\n  '\\u00C6': 'AE',\n  '\\u01FC': 'AE',\n  '\\u01E2': 'AE',\n  '\\uA734': 'AO',\n  '\\uA736': 'AU',\n  '\\uA738': 'AV',\n  '\\uA73A': 'AV',\n  '\\uA73C': 'AY',\n  '\\u24B7': 'B',\n  '\\uFF22': 'B',\n  '\\u1E02': 'B',\n  '\\u1E04': 'B',\n  '\\u1E06': 'B',\n  '\\u0243': 'B',\n  '\\u0182': 'B',\n  '\\u0181': 'B',\n  '\\u24B8': 'C',\n  '\\uFF23': 'C',\n  '\\u0106': 'C',\n  '\\u0108': 'C',\n  '\\u010A': 'C',\n  '\\u010C': 'C',\n  '\\u00C7': 'C',\n  '\\u1E08': 'C',\n  '\\u0187': 'C',\n  '\\u023B': 'C',\n  '\\uA73E': 'C',\n  '\\u24B9': 'D',\n  '\\uFF24': 'D',\n  '\\u1E0A': 'D',\n  '\\u010E': 'D',\n  '\\u1E0C': 'D',\n  '\\u1E10': 'D',\n  '\\u1E12': 'D',\n  '\\u1E0E': 'D',\n  '\\u0110': 'D',\n  '\\u018B': 'D',\n  '\\u018A': 'D',\n  '\\u0189': 'D',\n  '\\uA779': 'D',\n  '\\u01F1': 'DZ',\n  '\\u01C4': 'DZ',\n  '\\u01F2': 'Dz',\n  '\\u01C5': 'Dz',\n  '\\u24BA': 'E',\n  '\\uFF25': 'E',\n  '\\u00C8': 'E',\n  '\\u00C9': 'E',\n  '\\u00CA': 'E',\n  '\\u1EC0': 'E',\n  '\\u1EBE': 'E',\n  '\\u1EC4': 'E',\n  '\\u1EC2': 'E',\n  '\\u1EBC': 'E',\n  '\\u0112': 'E',\n  '\\u1E14': 'E',\n  '\\u1E16': 'E',\n  '\\u0114': 'E',\n  '\\u0116': 'E',\n  '\\u00CB': 'E',\n  '\\u1EBA': 'E',\n  '\\u011A': 'E',\n  '\\u0204': 'E',\n  '\\u0206': 'E',\n  '\\u1EB8': 'E',\n  '\\u1EC6': 'E',\n  '\\u0228': 'E',\n  '\\u1E1C': 'E',\n  '\\u0118': 'E',\n  '\\u1E18': 'E',\n  '\\u1E1A': 'E',\n  '\\u0190': 'E',\n  '\\u018E': 'E',\n  '\\u24BB': 'F',\n  '\\uFF26': 'F',\n  '\\u1E1E': 'F',\n  '\\u0191': 'F',\n  '\\uA77B': 'F',\n  '\\u24BC': 'G',\n  '\\uFF27': 'G',\n  '\\u01F4': 'G',\n  '\\u011C': 'G',\n  '\\u1E20': 'G',\n  '\\u011E': 'G',\n  '\\u0120': 'G',\n  '\\u01E6': 'G',\n  '\\u0122': 'G',\n  '\\u01E4': 'G',\n  '\\u0193': 'G',\n  '\\uA7A0': 'G',\n  '\\uA77D': 'G',\n  '\\uA77E': 'G',\n  '\\u24BD': 'H',\n  '\\uFF28': 'H',\n  '\\u0124': 'H',\n  '\\u1E22': 'H',\n  '\\u1E26': 'H',\n  '\\u021E': 'H',\n  '\\u1E24': 'H',\n  '\\u1E28': 'H',\n  '\\u1E2A': 'H',\n  '\\u0126': 'H',\n  '\\u2C67': 'H',\n  '\\u2C75': 'H',\n  '\\uA78D': 'H',\n  '\\u24BE': 'I',\n  '\\uFF29': 'I',\n  '\\u00CC': 'I',\n  '\\u00CD': 'I',\n  '\\u00CE': 'I',\n  '\\u0128': 'I',\n  '\\u012A': 'I',\n  '\\u012C': 'I',\n  '\\u0130': 'I',\n  '\\u00CF': 'I',\n  '\\u1E2E': 'I',\n  '\\u1EC8': 'I',\n  '\\u01CF': 'I',\n  '\\u0208': 'I',\n  '\\u020A': 'I',\n  '\\u1ECA': 'I',\n  '\\u012E': 'I',\n  '\\u1E2C': 'I',\n  '\\u0197': 'I',\n  '\\u24BF': 'J',\n  '\\uFF2A': 'J',\n  '\\u0134': 'J',\n  '\\u0248': 'J',\n  '\\u24C0': 'K',\n  '\\uFF2B': 'K',\n  '\\u1E30': 'K',\n  '\\u01E8': 'K',\n  '\\u1E32': 'K',\n  '\\u0136': 'K',\n  '\\u1E34': 'K',\n  '\\u0198': 'K',\n  '\\u2C69': 'K',\n  '\\uA740': 'K',\n  '\\uA742': 'K',\n  '\\uA744': 'K',\n  '\\uA7A2': 'K',\n  '\\u24C1': 'L',\n  '\\uFF2C': 'L',\n  '\\u013F': 'L',\n  '\\u0139': 'L',\n  '\\u013D': 'L',\n  '\\u1E36': 'L',\n  '\\u1E38': 'L',\n  '\\u013B': 'L',\n  '\\u1E3C': 'L',\n  '\\u1E3A': 'L',\n  '\\u0141': 'L',\n  '\\u023D': 'L',\n  '\\u2C62': 'L',\n  '\\u2C60': 'L',\n  '\\uA748': 'L',\n  '\\uA746': 'L',\n  '\\uA780': 'L',\n  '\\u01C7': 'LJ',\n  '\\u01C8': 'Lj',\n  '\\u24C2': 'M',\n  '\\uFF2D': 'M',\n  '\\u1E3E': 'M',\n  '\\u1E40': 'M',\n  '\\u1E42': 'M',\n  '\\u2C6E': 'M',\n  '\\u019C': 'M',\n  '\\u24C3': 'N',\n  '\\uFF2E': 'N',\n  '\\u01F8': 'N',\n  '\\u0143': 'N',\n  '\\u00D1': 'N',\n  '\\u1E44': 'N',\n  '\\u0147': 'N',\n  '\\u1E46': 'N',\n  '\\u0145': 'N',\n  '\\u1E4A': 'N',\n  '\\u1E48': 'N',\n  '\\u0220': 'N',\n  '\\u019D': 'N',\n  '\\uA790': 'N',\n  '\\uA7A4': 'N',\n  '\\u01CA': 'NJ',\n  '\\u01CB': 'Nj',\n  '\\u24C4': 'O',\n  '\\uFF2F': 'O',\n  '\\u00D2': 'O',\n  '\\u00D3': 'O',\n  '\\u00D4': 'O',\n  '\\u1ED2': 'O',\n  '\\u1ED0': 'O',\n  '\\u1ED6': 'O',\n  '\\u1ED4': 'O',\n  '\\u00D5': 'O',\n  '\\u1E4C': 'O',\n  '\\u022C': 'O',\n  '\\u1E4E': 'O',\n  '\\u014C': 'O',\n  '\\u1E50': 'O',\n  '\\u1E52': 'O',\n  '\\u014E': 'O',\n  '\\u022E': 'O',\n  '\\u0230': 'O',\n  '\\u00D6': 'O',\n  '\\u022A': 'O',\n  '\\u1ECE': 'O',\n  '\\u0150': 'O',\n  '\\u01D1': 'O',\n  '\\u020C': 'O',\n  '\\u020E': 'O',\n  '\\u01A0': 'O',\n  '\\u1EDC': 'O',\n  '\\u1EDA': 'O',\n  '\\u1EE0': 'O',\n  '\\u1EDE': 'O',\n  '\\u1EE2': 'O',\n  '\\u1ECC': 'O',\n  '\\u1ED8': 'O',\n  '\\u01EA': 'O',\n  '\\u01EC': 'O',\n  '\\u00D8': 'O',\n  '\\u01FE': 'O',\n  '\\u0186': 'O',\n  '\\u019F': 'O',\n  '\\uA74A': 'O',\n  '\\uA74C': 'O',\n  '\\u01A2': 'OI',\n  '\\uA74E': 'OO',\n  '\\u0222': 'OU',\n  '\\u24C5': 'P',\n  '\\uFF30': 'P',\n  '\\u1E54': 'P',\n  '\\u1E56': 'P',\n  '\\u01A4': 'P',\n  '\\u2C63': 'P',\n  '\\uA750': 'P',\n  '\\uA752': 'P',\n  '\\uA754': 'P',\n  '\\u24C6': 'Q',\n  '\\uFF31': 'Q',\n  '\\uA756': 'Q',\n  '\\uA758': 'Q',\n  '\\u024A': 'Q',\n  '\\u24C7': 'R',\n  '\\uFF32': 'R',\n  '\\u0154': 'R',\n  '\\u1E58': 'R',\n  '\\u0158': 'R',\n  '\\u0210': 'R',\n  '\\u0212': 'R',\n  '\\u1E5A': 'R',\n  '\\u1E5C': 'R',\n  '\\u0156': 'R',\n  '\\u1E5E': 'R',\n  '\\u024C': 'R',\n  '\\u2C64': 'R',\n  '\\uA75A': 'R',\n  '\\uA7A6': 'R',\n  '\\uA782': 'R',\n  '\\u24C8': 'S',\n  '\\uFF33': 'S',\n  '\\u1E9E': 'S',\n  '\\u015A': 'S',\n  '\\u1E64': 'S',\n  '\\u015C': 'S',\n  '\\u1E60': 'S',\n  '\\u0160': 'S',\n  '\\u1E66': 'S',\n  '\\u1E62': 'S',\n  '\\u1E68': 'S',\n  '\\u0218': 'S',\n  '\\u015E': 'S',\n  '\\u2C7E': 'S',\n  '\\uA7A8': 'S',\n  '\\uA784': 'S',\n  '\\u24C9': 'T',\n  '\\uFF34': 'T',\n  '\\u1E6A': 'T',\n  '\\u0164': 'T',\n  '\\u1E6C': 'T',\n  '\\u021A': 'T',\n  '\\u0162': 'T',\n  '\\u1E70': 'T',\n  '\\u1E6E': 'T',\n  '\\u0166': 'T',\n  '\\u01AC': 'T',\n  '\\u01AE': 'T',\n  '\\u023E': 'T',\n  '\\uA786': 'T',\n  '\\uA728': 'TZ',\n  '\\u24CA': 'U',\n  '\\uFF35': 'U',\n  '\\u00D9': 'U',\n  '\\u00DA': 'U',\n  '\\u00DB': 'U',\n  '\\u0168': 'U',\n  '\\u1E78': 'U',\n  '\\u016A': 'U',\n  '\\u1E7A': 'U',\n  '\\u016C': 'U',\n  '\\u00DC': 'U',\n  '\\u01DB': 'U',\n  '\\u01D7': 'U',\n  '\\u01D5': 'U',\n  '\\u01D9': 'U',\n  '\\u1EE6': 'U',\n  '\\u016E': 'U',\n  '\\u0170': 'U',\n  '\\u01D3': 'U',\n  '\\u0214': 'U',\n  '\\u0216': 'U',\n  '\\u01AF': 'U',\n  '\\u1EEA': 'U',\n  '\\u1EE8': 'U',\n  '\\u1EEE': 'U',\n  '\\u1EEC': 'U',\n  '\\u1EF0': 'U',\n  '\\u1EE4': 'U',\n  '\\u1E72': 'U',\n  '\\u0172': 'U',\n  '\\u1E76': 'U',\n  '\\u1E74': 'U',\n  '\\u0244': 'U',\n  '\\u24CB': 'V',\n  '\\uFF36': 'V',\n  '\\u1E7C': 'V',\n  '\\u1E7E': 'V',\n  '\\u01B2': 'V',\n  '\\uA75E': 'V',\n  '\\u0245': 'V',\n  '\\uA760': 'VY',\n  '\\u24CC': 'W',\n  '\\uFF37': 'W',\n  '\\u1E80': 'W',\n  '\\u1E82': 'W',\n  '\\u0174': 'W',\n  '\\u1E86': 'W',\n  '\\u1E84': 'W',\n  '\\u1E88': 'W',\n  '\\u2C72': 'W',\n  '\\u24CD': 'X',\n  '\\uFF38': 'X',\n  '\\u1E8A': 'X',\n  '\\u1E8C': 'X',\n  '\\u24CE': 'Y',\n  '\\uFF39': 'Y',\n  '\\u1EF2': 'Y',\n  '\\u00DD': 'Y',\n  '\\u0176': 'Y',\n  '\\u1EF8': 'Y',\n  '\\u0232': 'Y',\n  '\\u1E8E': 'Y',\n  '\\u0178': 'Y',\n  '\\u1EF6': 'Y',\n  '\\u1EF4': 'Y',\n  '\\u01B3': 'Y',\n  '\\u024E': 'Y',\n  '\\u1EFE': 'Y',\n  '\\u24CF': 'Z',\n  '\\uFF3A': 'Z',\n  '\\u0179': 'Z',\n  '\\u1E90': 'Z',\n  '\\u017B': 'Z',\n  '\\u017D': 'Z',\n  '\\u1E92': 'Z',\n  '\\u1E94': 'Z',\n  '\\u01B5': 'Z',\n  '\\u0224': 'Z',\n  '\\u2C7F': 'Z',\n  '\\u2C6B': 'Z',\n  '\\uA762': 'Z',\n  '\\u24D0': 'a',\n  '\\uFF41': 'a',\n  '\\u1E9A': 'a',\n  '\\u00E0': 'a',\n  '\\u00E1': 'a',\n  '\\u00E2': 'a',\n  '\\u1EA7': 'a',\n  '\\u1EA5': 'a',\n  '\\u1EAB': 'a',\n  '\\u1EA9': 'a',\n  '\\u00E3': 'a',\n  '\\u0101': 'a',\n  '\\u0103': 'a',\n  '\\u1EB1': 'a',\n  '\\u1EAF': 'a',\n  '\\u1EB5': 'a',\n  '\\u1EB3': 'a',\n  '\\u0227': 'a',\n  '\\u01E1': 'a',\n  '\\u00E4': 'a',\n  '\\u01DF': 'a',\n  '\\u1EA3': 'a',\n  '\\u00E5': 'a',\n  '\\u01FB': 'a',\n  '\\u01CE': 'a',\n  '\\u0201': 'a',\n  '\\u0203': 'a',\n  '\\u1EA1': 'a',\n  '\\u1EAD': 'a',\n  '\\u1EB7': 'a',\n  '\\u1E01': 'a',\n  '\\u0105': 'a',\n  '\\u2C65': 'a',\n  '\\u0250': 'a',\n  '\\uA733': 'aa',\n  '\\u00E6': 'ae',\n  '\\u01FD': 'ae',\n  '\\u01E3': 'ae',\n  '\\uA735': 'ao',\n  '\\uA737': 'au',\n  '\\uA739': 'av',\n  '\\uA73B': 'av',\n  '\\uA73D': 'ay',\n  '\\u24D1': 'b',\n  '\\uFF42': 'b',\n  '\\u1E03': 'b',\n  '\\u1E05': 'b',\n  '\\u1E07': 'b',\n  '\\u0180': 'b',\n  '\\u0183': 'b',\n  '\\u0253': 'b',\n  '\\u24D2': 'c',\n  '\\uFF43': 'c',\n  '\\u0107': 'c',\n  '\\u0109': 'c',\n  '\\u010B': 'c',\n  '\\u010D': 'c',\n  '\\u00E7': 'c',\n  '\\u1E09': 'c',\n  '\\u0188': 'c',\n  '\\u023C': 'c',\n  '\\uA73F': 'c',\n  '\\u2184': 'c',\n  '\\u24D3': 'd',\n  '\\uFF44': 'd',\n  '\\u1E0B': 'd',\n  '\\u010F': 'd',\n  '\\u1E0D': 'd',\n  '\\u1E11': 'd',\n  '\\u1E13': 'd',\n  '\\u1E0F': 'd',\n  '\\u0111': 'd',\n  '\\u018C': 'd',\n  '\\u0256': 'd',\n  '\\u0257': 'd',\n  '\\uA77A': 'd',\n  '\\u01F3': 'dz',\n  '\\u01C6': 'dz',\n  '\\u24D4': 'e',\n  '\\uFF45': 'e',\n  '\\u00E8': 'e',\n  '\\u00E9': 'e',\n  '\\u00EA': 'e',\n  '\\u1EC1': 'e',\n  '\\u1EBF': 'e',\n  '\\u1EC5': 'e',\n  '\\u1EC3': 'e',\n  '\\u1EBD': 'e',\n  '\\u0113': 'e',\n  '\\u1E15': 'e',\n  '\\u1E17': 'e',\n  '\\u0115': 'e',\n  '\\u0117': 'e',\n  '\\u00EB': 'e',\n  '\\u1EBB': 'e',\n  '\\u011B': 'e',\n  '\\u0205': 'e',\n  '\\u0207': 'e',\n  '\\u1EB9': 'e',\n  '\\u1EC7': 'e',\n  '\\u0229': 'e',\n  '\\u1E1D': 'e',\n  '\\u0119': 'e',\n  '\\u1E19': 'e',\n  '\\u1E1B': 'e',\n  '\\u0247': 'e',\n  '\\u025B': 'e',\n  '\\u01DD': 'e',\n  '\\u24D5': 'f',\n  '\\uFF46': 'f',\n  '\\u1E1F': 'f',\n  '\\u0192': 'f',\n  '\\uA77C': 'f',\n  '\\u24D6': 'g',\n  '\\uFF47': 'g',\n  '\\u01F5': 'g',\n  '\\u011D': 'g',\n  '\\u1E21': 'g',\n  '\\u011F': 'g',\n  '\\u0121': 'g',\n  '\\u01E7': 'g',\n  '\\u0123': 'g',\n  '\\u01E5': 'g',\n  '\\u0260': 'g',\n  '\\uA7A1': 'g',\n  '\\u1D79': 'g',\n  '\\uA77F': 'g',\n  '\\u24D7': 'h',\n  '\\uFF48': 'h',\n  '\\u0125': 'h',\n  '\\u1E23': 'h',\n  '\\u1E27': 'h',\n  '\\u021F': 'h',\n  '\\u1E25': 'h',\n  '\\u1E29': 'h',\n  '\\u1E2B': 'h',\n  '\\u1E96': 'h',\n  '\\u0127': 'h',\n  '\\u2C68': 'h',\n  '\\u2C76': 'h',\n  '\\u0265': 'h',\n  '\\u0195': 'hv',\n  '\\u24D8': 'i',\n  '\\uFF49': 'i',\n  '\\u00EC': 'i',\n  '\\u00ED': 'i',\n  '\\u00EE': 'i',\n  '\\u0129': 'i',\n  '\\u012B': 'i',\n  '\\u012D': 'i',\n  '\\u00EF': 'i',\n  '\\u1E2F': 'i',\n  '\\u1EC9': 'i',\n  '\\u01D0': 'i',\n  '\\u0209': 'i',\n  '\\u020B': 'i',\n  '\\u1ECB': 'i',\n  '\\u012F': 'i',\n  '\\u1E2D': 'i',\n  '\\u0268': 'i',\n  '\\u0131': 'i',\n  '\\u24D9': 'j',\n  '\\uFF4A': 'j',\n  '\\u0135': 'j',\n  '\\u01F0': 'j',\n  '\\u0249': 'j',\n  '\\u24DA': 'k',\n  '\\uFF4B': 'k',\n  '\\u1E31': 'k',\n  '\\u01E9': 'k',\n  '\\u1E33': 'k',\n  '\\u0137': 'k',\n  '\\u1E35': 'k',\n  '\\u0199': 'k',\n  '\\u2C6A': 'k',\n  '\\uA741': 'k',\n  '\\uA743': 'k',\n  '\\uA745': 'k',\n  '\\uA7A3': 'k',\n  '\\u24DB': 'l',\n  '\\uFF4C': 'l',\n  '\\u0140': 'l',\n  '\\u013A': 'l',\n  '\\u013E': 'l',\n  '\\u1E37': 'l',\n  '\\u1E39': 'l',\n  '\\u013C': 'l',\n  '\\u1E3D': 'l',\n  '\\u1E3B': 'l',\n  '\\u017F': 'l',\n  '\\u0142': 'l',\n  '\\u019A': 'l',\n  '\\u026B': 'l',\n  '\\u2C61': 'l',\n  '\\uA749': 'l',\n  '\\uA781': 'l',\n  '\\uA747': 'l',\n  '\\u01C9': 'lj',\n  '\\u24DC': 'm',\n  '\\uFF4D': 'm',\n  '\\u1E3F': 'm',\n  '\\u1E41': 'm',\n  '\\u1E43': 'm',\n  '\\u0271': 'm',\n  '\\u026F': 'm',\n  '\\u24DD': 'n',\n  '\\uFF4E': 'n',\n  '\\u01F9': 'n',\n  '\\u0144': 'n',\n  '\\u00F1': 'n',\n  '\\u1E45': 'n',\n  '\\u0148': 'n',\n  '\\u1E47': 'n',\n  '\\u0146': 'n',\n  '\\u1E4B': 'n',\n  '\\u1E49': 'n',\n  '\\u019E': 'n',\n  '\\u0272': 'n',\n  '\\u0149': 'n',\n  '\\uA791': 'n',\n  '\\uA7A5': 'n',\n  '\\u01CC': 'nj',\n  '\\u24DE': 'o',\n  '\\uFF4F': 'o',\n  '\\u00F2': 'o',\n  '\\u00F3': 'o',\n  '\\u00F4': 'o',\n  '\\u1ED3': 'o',\n  '\\u1ED1': 'o',\n  '\\u1ED7': 'o',\n  '\\u1ED5': 'o',\n  '\\u00F5': 'o',\n  '\\u1E4D': 'o',\n  '\\u022D': 'o',\n  '\\u1E4F': 'o',\n  '\\u014D': 'o',\n  '\\u1E51': 'o',\n  '\\u1E53': 'o',\n  '\\u014F': 'o',\n  '\\u022F': 'o',\n  '\\u0231': 'o',\n  '\\u00F6': 'o',\n  '\\u022B': 'o',\n  '\\u1ECF': 'o',\n  '\\u0151': 'o',\n  '\\u01D2': 'o',\n  '\\u020D': 'o',\n  '\\u020F': 'o',\n  '\\u01A1': 'o',\n  '\\u1EDD': 'o',\n  '\\u1EDB': 'o',\n  '\\u1EE1': 'o',\n  '\\u1EDF': 'o',\n  '\\u1EE3': 'o',\n  '\\u1ECD': 'o',\n  '\\u1ED9': 'o',\n  '\\u01EB': 'o',\n  '\\u01ED': 'o',\n  '\\u00F8': 'o',\n  '\\u01FF': 'o',\n  '\\u0254': 'o',\n  '\\uA74B': 'o',\n  '\\uA74D': 'o',\n  '\\u0275': 'o',\n  '\\u01A3': 'oi',\n  '\\u0223': 'ou',\n  '\\uA74F': 'oo',\n  '\\u24DF': 'p',\n  '\\uFF50': 'p',\n  '\\u1E55': 'p',\n  '\\u1E57': 'p',\n  '\\u01A5': 'p',\n  '\\u1D7D': 'p',\n  '\\uA751': 'p',\n  '\\uA753': 'p',\n  '\\uA755': 'p',\n  '\\u24E0': 'q',\n  '\\uFF51': 'q',\n  '\\u024B': 'q',\n  '\\uA757': 'q',\n  '\\uA759': 'q',\n  '\\u24E1': 'r',\n  '\\uFF52': 'r',\n  '\\u0155': 'r',\n  '\\u1E59': 'r',\n  '\\u0159': 'r',\n  '\\u0211': 'r',\n  '\\u0213': 'r',\n  '\\u1E5B': 'r',\n  '\\u1E5D': 'r',\n  '\\u0157': 'r',\n  '\\u1E5F': 'r',\n  '\\u024D': 'r',\n  '\\u027D': 'r',\n  '\\uA75B': 'r',\n  '\\uA7A7': 'r',\n  '\\uA783': 'r',\n  '\\u24E2': 's',\n  '\\uFF53': 's',\n  '\\u00DF': 's',\n  '\\u015B': 's',\n  '\\u1E65': 's',\n  '\\u015D': 's',\n  '\\u1E61': 's',\n  '\\u0161': 's',\n  '\\u1E67': 's',\n  '\\u1E63': 's',\n  '\\u1E69': 's',\n  '\\u0219': 's',\n  '\\u015F': 's',\n  '\\u023F': 's',\n  '\\uA7A9': 's',\n  '\\uA785': 's',\n  '\\u1E9B': 's',\n  '\\u24E3': 't',\n  '\\uFF54': 't',\n  '\\u1E6B': 't',\n  '\\u1E97': 't',\n  '\\u0165': 't',\n  '\\u1E6D': 't',\n  '\\u021B': 't',\n  '\\u0163': 't',\n  '\\u1E71': 't',\n  '\\u1E6F': 't',\n  '\\u0167': 't',\n  '\\u01AD': 't',\n  '\\u0288': 't',\n  '\\u2C66': 't',\n  '\\uA787': 't',\n  '\\uA729': 'tz',\n  '\\u24E4': 'u',\n  '\\uFF55': 'u',\n  '\\u00F9': 'u',\n  '\\u00FA': 'u',\n  '\\u00FB': 'u',\n  '\\u0169': 'u',\n  '\\u1E79': 'u',\n  '\\u016B': 'u',\n  '\\u1E7B': 'u',\n  '\\u016D': 'u',\n  '\\u00FC': 'u',\n  '\\u01DC': 'u',\n  '\\u01D8': 'u',\n  '\\u01D6': 'u',\n  '\\u01DA': 'u',\n  '\\u1EE7': 'u',\n  '\\u016F': 'u',\n  '\\u0171': 'u',\n  '\\u01D4': 'u',\n  '\\u0215': 'u',\n  '\\u0217': 'u',\n  '\\u01B0': 'u',\n  '\\u1EEB': 'u',\n  '\\u1EE9': 'u',\n  '\\u1EEF': 'u',\n  '\\u1EED': 'u',\n  '\\u1EF1': 'u',\n  '\\u1EE5': 'u',\n  '\\u1E73': 'u',\n  '\\u0173': 'u',\n  '\\u1E77': 'u',\n  '\\u1E75': 'u',\n  '\\u0289': 'u',\n  '\\u24E5': 'v',\n  '\\uFF56': 'v',\n  '\\u1E7D': 'v',\n  '\\u1E7F': 'v',\n  '\\u028B': 'v',\n  '\\uA75F': 'v',\n  '\\u028C': 'v',\n  '\\uA761': 'vy',\n  '\\u24E6': 'w',\n  '\\uFF57': 'w',\n  '\\u1E81': 'w',\n  '\\u1E83': 'w',\n  '\\u0175': 'w',\n  '\\u1E87': 'w',\n  '\\u1E85': 'w',\n  '\\u1E98': 'w',\n  '\\u1E89': 'w',\n  '\\u2C73': 'w',\n  '\\u24E7': 'x',\n  '\\uFF58': 'x',\n  '\\u1E8B': 'x',\n  '\\u1E8D': 'x',\n  '\\u24E8': 'y',\n  '\\uFF59': 'y',\n  '\\u1EF3': 'y',\n  '\\u00FD': 'y',\n  '\\u0177': 'y',\n  '\\u1EF9': 'y',\n  '\\u0233': 'y',\n  '\\u1E8F': 'y',\n  '\\u00FF': 'y',\n  '\\u1EF7': 'y',\n  '\\u1E99': 'y',\n  '\\u1EF5': 'y',\n  '\\u01B4': 'y',\n  '\\u024F': 'y',\n  '\\u1EFF': 'y',\n  '\\u24E9': 'z',\n  '\\uFF5A': 'z',\n  '\\u017A': 'z',\n  '\\u1E91': 'z',\n  '\\u017C': 'z',\n  '\\u017E': 'z',\n  '\\u1E93': 'z',\n  '\\u1E95': 'z',\n  '\\u01B6': 'z',\n  '\\u0225': 'z',\n  '\\u0240': 'z',\n  '\\u2C6C': 'z',\n  '\\uA763': 'z',\n  '\\u0386': '\\u0391',\n  '\\u0388': '\\u0395',\n  '\\u0389': '\\u0397',\n  '\\u038A': '\\u0399',\n  '\\u03AA': '\\u0399',\n  '\\u038C': '\\u039F',\n  '\\u038E': '\\u03A5',\n  '\\u03AB': '\\u03A5',\n  '\\u038F': '\\u03A9',\n  '\\u03AC': '\\u03B1',\n  '\\u03AD': '\\u03B5',\n  '\\u03AE': '\\u03B7',\n  '\\u03AF': '\\u03B9',\n  '\\u03CA': '\\u03B9',\n  '\\u0390': '\\u03B9',\n  '\\u03CC': '\\u03BF',\n  '\\u03CD': '\\u03C5',\n  '\\u03CB': '\\u03C5',\n  '\\u03B0': '\\u03C5',\n  '\\u03C9': '\\u03C9',\n  '\\u03C2': '\\u03C3'\n};\nfunction stripSpecialChars(text) {\n  const match = a => diacritics[a] || a;\n  return text.replace(/[^\\u0000-\\u007E]/g, match);\n}\nclass ItemsList {\n  constructor(_ngSelect, _selectionModel) {\n    this._ngSelect = _ngSelect;\n    this._selectionModel = _selectionModel;\n    this._items = [];\n    this._filteredItems = [];\n    this._markedIndex = -1;\n  }\n  get items() {\n    return this._items;\n  }\n  get filteredItems() {\n    return this._filteredItems;\n  }\n  get markedIndex() {\n    return this._markedIndex;\n  }\n  get selectedItems() {\n    return this._selectionModel.value;\n  }\n  get markedItem() {\n    return this._filteredItems[this._markedIndex];\n  }\n  get noItemsToSelect() {\n    return this._ngSelect.hideSelected && this._items.length === this.selectedItems.length;\n  }\n  get maxItemsSelected() {\n    return this._ngSelect.multiple && this._ngSelect.maxSelectedItems <= this.selectedItems.length;\n  }\n  get lastSelectedItem() {\n    let i = this.selectedItems.length - 1;\n    for (; i >= 0; i--) {\n      const item = this.selectedItems[i];\n      if (!item.disabled) {\n        return item;\n      }\n    }\n    return null;\n  }\n  setItems(items) {\n    this._items = items.map((item, index) => this.mapItem(item, index));\n    if (this._ngSelect.groupBy) {\n      this._groups = this._groupBy(this._items, this._ngSelect.groupBy);\n      this._items = this._flatten(this._groups);\n    } else {\n      this._groups = new Map();\n      this._groups.set(undefined, this._items);\n    }\n    this._filteredItems = [...this._items];\n  }\n  select(item) {\n    if (item.selected || this.maxItemsSelected) {\n      return;\n    }\n    const multiple = this._ngSelect.multiple;\n    if (!multiple) {\n      this.clearSelected();\n    }\n    this._selectionModel.select(item, multiple, this._ngSelect.selectableGroupAsModel);\n    if (this._ngSelect.hideSelected) {\n      this._hideSelected(item);\n    }\n  }\n  unselect(item) {\n    if (!item.selected) {\n      return;\n    }\n    this._selectionModel.unselect(item, this._ngSelect.multiple);\n    if (this._ngSelect.hideSelected && isDefined(item.index) && this._ngSelect.multiple) {\n      this._showSelected(item);\n    }\n  }\n  findItem(value) {\n    let findBy;\n    if (this._ngSelect.compareWith) {\n      findBy = item => this._ngSelect.compareWith(item.value, value);\n    } else if (this._ngSelect.bindValue) {\n      findBy = item => !item.children && this.resolveNested(item.value, this._ngSelect.bindValue) === value;\n    } else {\n      findBy = item => item.value === value || !item.children && item.label && item.label === this.resolveNested(value, this._ngSelect.bindLabel);\n    }\n    return this._items.find(item => findBy(item));\n  }\n  addItem(item) {\n    const option = this.mapItem(item, this._items.length);\n    this._items.push(option);\n    this._filteredItems.push(option);\n    return option;\n  }\n  clearSelected(keepDisabled = false) {\n    this._selectionModel.clear(keepDisabled);\n    this._items.forEach(item => {\n      item.selected = keepDisabled && item.selected && item.disabled;\n      item.marked = false;\n    });\n    if (this._ngSelect.hideSelected) {\n      this.resetFilteredItems();\n    }\n  }\n  findByLabel(term) {\n    term = stripSpecialChars(term).toLocaleLowerCase();\n    return this.filteredItems.find(item => {\n      const label = stripSpecialChars(item.label).toLocaleLowerCase();\n      return label.substr(0, term.length) === term;\n    });\n  }\n  filter(term) {\n    if (!term) {\n      this.resetFilteredItems();\n      return;\n    }\n    this._filteredItems = [];\n    term = this._ngSelect.searchFn ? term : stripSpecialChars(term).toLocaleLowerCase();\n    const match = this._ngSelect.searchFn || this._defaultSearchFn;\n    const hideSelected = this._ngSelect.hideSelected;\n    for (const key of Array.from(this._groups.keys())) {\n      const matchedItems = [];\n      for (const item of this._groups.get(key)) {\n        if (hideSelected && (item.parent && item.parent.selected || item.selected)) {\n          continue;\n        }\n        const searchItem = this._ngSelect.searchFn ? item.value : item;\n        if (match(term, searchItem)) {\n          matchedItems.push(item);\n        }\n      }\n      if (matchedItems.length > 0) {\n        const [last] = matchedItems.slice(-1);\n        if (last.parent) {\n          const head = this._items.find(x => x === last.parent);\n          this._filteredItems.push(head);\n        }\n        this._filteredItems.push(...matchedItems);\n      }\n    }\n  }\n  resetFilteredItems() {\n    if (this._filteredItems.length === this._items.length) {\n      return;\n    }\n    if (this._ngSelect.hideSelected && this.selectedItems.length > 0) {\n      this._filteredItems = this._items.filter(x => !x.selected);\n    } else {\n      this._filteredItems = this._items;\n    }\n  }\n  unmarkItem() {\n    this._markedIndex = -1;\n  }\n  markNextItem() {\n    this._stepToItem(+1);\n  }\n  markPreviousItem() {\n    this._stepToItem(-1);\n  }\n  markItem(item) {\n    this._markedIndex = this._filteredItems.indexOf(item);\n  }\n  markSelectedOrDefault(markDefault) {\n    if (this._filteredItems.length === 0) {\n      return;\n    }\n    const lastMarkedIndex = this._getLastMarkedIndex();\n    if (lastMarkedIndex > -1) {\n      this._markedIndex = lastMarkedIndex;\n    } else {\n      this._markedIndex = markDefault ? this.filteredItems.findIndex(x => !x.disabled) : -1;\n    }\n  }\n  resolveNested(option, key) {\n    if (!isObject(option)) {\n      return option;\n    }\n    if (key.indexOf('.') === -1) {\n      return option[key];\n    } else {\n      const keys = key.split('.');\n      let value = option;\n      for (let i = 0, len = keys.length; i < len; ++i) {\n        if (value == null) {\n          return null;\n        }\n        value = value[keys[i]];\n      }\n      return value;\n    }\n  }\n  mapItem(item, index) {\n    const label = isDefined(item.$ngOptionLabel) ? item.$ngOptionLabel : this.resolveNested(item, this._ngSelect.bindLabel);\n    const value = isDefined(item.$ngOptionValue) ? item.$ngOptionValue : item;\n    return {\n      index,\n      label: isDefined(label) ? label.toString() : '',\n      value,\n      disabled: item.disabled,\n      htmlId: `${this._ngSelect.dropdownId}-${index}`\n    };\n  }\n  mapSelectedItems() {\n    const multiple = this._ngSelect.multiple;\n    for (const selected of this.selectedItems) {\n      const value = this._ngSelect.bindValue ? this.resolveNested(selected.value, this._ngSelect.bindValue) : selected.value;\n      const item = isDefined(value) ? this.findItem(value) : null;\n      this._selectionModel.unselect(selected, multiple);\n      this._selectionModel.select(item || selected, multiple, this._ngSelect.selectableGroupAsModel);\n    }\n    if (this._ngSelect.hideSelected) {\n      this._filteredItems = this.filteredItems.filter(x => this.selectedItems.indexOf(x) === -1);\n    }\n  }\n  _showSelected(item) {\n    this._filteredItems.push(item);\n    if (item.parent) {\n      const parent = item.parent;\n      const parentExists = this._filteredItems.find(x => x === parent);\n      if (!parentExists) {\n        this._filteredItems.push(parent);\n      }\n    } else if (item.children) {\n      for (const child of item.children) {\n        child.selected = false;\n        this._filteredItems.push(child);\n      }\n    }\n    this._filteredItems = [...this._filteredItems.sort((a, b) => a.index - b.index)];\n  }\n  _hideSelected(item) {\n    this._filteredItems = this._filteredItems.filter(x => x !== item);\n    if (item.parent) {\n      const children = item.parent.children;\n      if (children.every(x => x.selected)) {\n        this._filteredItems = this._filteredItems.filter(x => x !== item.parent);\n      }\n    } else if (item.children) {\n      this._filteredItems = this.filteredItems.filter(x => x.parent !== item);\n    }\n  }\n  _defaultSearchFn(search, opt) {\n    const label = stripSpecialChars(opt.label).toLocaleLowerCase();\n    return label.indexOf(search) > -1;\n  }\n  _getNextItemIndex(steps) {\n    if (steps > 0) {\n      return this._markedIndex >= this._filteredItems.length - 1 ? 0 : this._markedIndex + 1;\n    }\n    return this._markedIndex <= 0 ? this._filteredItems.length - 1 : this._markedIndex - 1;\n  }\n  _stepToItem(steps) {\n    if (this._filteredItems.length === 0 || this._filteredItems.every(x => x.disabled)) {\n      return;\n    }\n    this._markedIndex = this._getNextItemIndex(steps);\n    if (this.markedItem.disabled) {\n      this._stepToItem(steps);\n    }\n  }\n  _getLastMarkedIndex() {\n    if (this._ngSelect.hideSelected) {\n      return -1;\n    }\n    if (this._markedIndex > -1 && this.markedItem === undefined) {\n      return -1;\n    }\n    const selectedIndex = this._filteredItems.indexOf(this.lastSelectedItem);\n    if (this.lastSelectedItem && selectedIndex < 0) {\n      return -1;\n    }\n    return Math.max(this.markedIndex, selectedIndex);\n  }\n  _groupBy(items, prop) {\n    const groups = new Map();\n    if (items.length === 0) {\n      return groups;\n    }\n    // Check if items are already grouped by given key.\n    if (Array.isArray(items[0].value[prop])) {\n      for (const item of items) {\n        const children = (item.value[prop] || []).map((x, index) => this.mapItem(x, index));\n        groups.set(item, children);\n      }\n      return groups;\n    }\n    const isFnKey = isFunction(this._ngSelect.groupBy);\n    const keyFn = item => {\n      const key = isFnKey ? prop(item.value) : item.value[prop];\n      return isDefined(key) ? key : undefined;\n    };\n    // Group items by key.\n    for (const item of items) {\n      const key = keyFn(item);\n      const group = groups.get(key);\n      if (group) {\n        group.push(item);\n      } else {\n        groups.set(key, [item]);\n      }\n    }\n    return groups;\n  }\n  _flatten(groups) {\n    const isGroupByFn = isFunction(this._ngSelect.groupBy);\n    const items = [];\n    for (const key of Array.from(groups.keys())) {\n      let i = items.length;\n      if (key === undefined) {\n        const withoutGroup = groups.get(undefined) || [];\n        items.push(...withoutGroup.map(x => {\n          x.index = i++;\n          return x;\n        }));\n        continue;\n      }\n      const isObjectKey = isObject(key);\n      const parent = {\n        label: isObjectKey ? '' : String(key),\n        children: undefined,\n        parent: null,\n        index: i++,\n        disabled: !this._ngSelect.selectableGroup,\n        htmlId: newId()\n      };\n      const groupKey = isGroupByFn ? this._ngSelect.bindLabel : this._ngSelect.groupBy;\n      const groupValue = this._ngSelect.groupValue || (() => {\n        if (isObjectKey) {\n          return key.value;\n        }\n        return {\n          [groupKey]: key\n        };\n      });\n      const children = groups.get(key).map(x => {\n        x.parent = parent;\n        x.children = undefined;\n        x.index = i++;\n        return x;\n      });\n      parent.children = children;\n      parent.value = groupValue(key, children.map(x => x.value));\n      items.push(parent);\n      items.push(...children);\n    }\n    return items;\n  }\n}\nclass NgDropdownPanelService {\n  constructor() {\n    this._dimensions = {\n      itemHeight: 0,\n      panelHeight: 0,\n      itemsPerViewport: 0\n    };\n  }\n  get dimensions() {\n    return this._dimensions;\n  }\n  calculateItems(scrollPos, itemsLength, buffer) {\n    const d = this._dimensions;\n    const scrollHeight = d.itemHeight * itemsLength;\n    const scrollTop = Math.max(0, scrollPos);\n    const indexByScrollTop = scrollTop / scrollHeight * itemsLength;\n    let end = Math.min(itemsLength, Math.ceil(indexByScrollTop) + (d.itemsPerViewport + 1));\n    const maxStartEnd = end;\n    const maxStart = Math.max(0, maxStartEnd - d.itemsPerViewport);\n    let start = Math.min(maxStart, Math.floor(indexByScrollTop));\n    let topPadding = d.itemHeight * Math.ceil(start) - d.itemHeight * Math.min(start, buffer);\n    topPadding = !isNaN(topPadding) ? topPadding : 0;\n    start = !isNaN(start) ? start : -1;\n    end = !isNaN(end) ? end : -1;\n    start -= buffer;\n    start = Math.max(0, start);\n    end += buffer;\n    end = Math.min(itemsLength, end);\n    return {\n      topPadding,\n      scrollHeight,\n      start,\n      end\n    };\n  }\n  setDimensions(itemHeight, panelHeight) {\n    const itemsPerViewport = Math.max(1, Math.floor(panelHeight / itemHeight));\n    this._dimensions = {\n      itemHeight,\n      panelHeight,\n      itemsPerViewport\n    };\n  }\n  getScrollTo(itemTop, itemHeight, lastScroll) {\n    const {\n      panelHeight\n    } = this.dimensions;\n    const itemBottom = itemTop + itemHeight;\n    const top = lastScroll;\n    const bottom = top + panelHeight;\n    if (panelHeight >= itemBottom && lastScroll === itemTop) {\n      return null;\n    }\n    if (itemBottom > bottom) {\n      return top + itemBottom - bottom;\n    } else if (itemTop <= top) {\n      return itemTop;\n    }\n    return null;\n  }\n  static {\n    this.ɵfac = function NgDropdownPanelService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgDropdownPanelService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NgDropdownPanelService,\n      factory: NgDropdownPanelService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgDropdownPanelService, [{\n    type: Injectable\n  }], null, null);\n})();\nconst CSS_POSITIONS = ['top', 'right', 'bottom', 'left'];\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\nclass NgDropdownPanelComponent {\n  constructor(_renderer, _zone, _panelService, _elementRef, _document) {\n    this._renderer = _renderer;\n    this._zone = _zone;\n    this._panelService = _panelService;\n    this._document = _document;\n    this.items = [];\n    this.position = 'auto';\n    this.virtualScroll = false;\n    this.filterValue = null;\n    this.ariaLabelDropdown = input(null);\n    this.update = new EventEmitter();\n    this.scroll = new EventEmitter();\n    this.scrollToEnd = new EventEmitter();\n    this.outsideClick = new EventEmitter();\n    this._destroy$ = new Subject();\n    this._scrollToEndFired = false;\n    this._updateScrollHeight = false;\n    this._lastScrollPosition = 0;\n    this._dropdown = _elementRef.nativeElement;\n  }\n  get currentPosition() {\n    return this._currentPosition;\n  }\n  get itemsLength() {\n    return this._itemsLength;\n  }\n  set itemsLength(value) {\n    if (value !== this._itemsLength) {\n      this._itemsLength = value;\n      this._onItemsLengthChanged();\n    }\n  }\n  get _startOffset() {\n    if (this.markedItem) {\n      const {\n        itemHeight,\n        panelHeight\n      } = this._panelService.dimensions;\n      const offset = this.markedItem.index * itemHeight;\n      return panelHeight > offset ? 0 : offset;\n    }\n    return 0;\n  }\n  ngOnInit() {\n    this._select = this._dropdown.parentElement;\n    this._virtualPadding = this.paddingElementRef.nativeElement;\n    this._scrollablePanel = this.scrollElementRef.nativeElement;\n    this._contentPanel = this.contentElementRef.nativeElement;\n    this._handleScroll();\n    this._handleOutsideClick();\n    this._appendDropdown();\n    this._setupMousedownListener();\n  }\n  ngOnChanges(changes) {\n    if (changes.items) {\n      const change = changes.items;\n      this._onItemsChange(change.currentValue, change.firstChange);\n    }\n  }\n  ngOnDestroy() {\n    this._destroy$.next();\n    this._destroy$.complete();\n    this._destroy$.unsubscribe();\n    if (this.appendTo) {\n      this._renderer.removeChild(this._dropdown.parentNode, this._dropdown);\n    }\n  }\n  scrollTo(option, startFromOption = false) {\n    if (!option) {\n      return;\n    }\n    const index = this.items.indexOf(option);\n    if (index < 0 || index >= this.itemsLength) {\n      return;\n    }\n    let scrollTo;\n    if (this.virtualScroll) {\n      const itemHeight = this._panelService.dimensions.itemHeight;\n      scrollTo = this._panelService.getScrollTo(index * itemHeight, itemHeight, this._lastScrollPosition);\n    } else {\n      const item = this._dropdown.querySelector(`#${option.htmlId}`);\n      const lastScroll = startFromOption ? item.offsetTop : this._lastScrollPosition;\n      scrollTo = this._panelService.getScrollTo(item.offsetTop, item.clientHeight, lastScroll);\n    }\n    if (isDefined(scrollTo)) {\n      this._scrollablePanel.scrollTop = scrollTo;\n    }\n  }\n  scrollToTag() {\n    const panel = this._scrollablePanel;\n    panel.scrollTop = panel.scrollHeight - panel.clientHeight;\n  }\n  adjustPosition() {\n    this._updateYPosition();\n  }\n  _handleDropdownPosition() {\n    this._currentPosition = this._calculateCurrentPosition(this._dropdown);\n    if (CSS_POSITIONS.includes(this._currentPosition)) {\n      this._updateDropdownClass(this._currentPosition);\n    } else {\n      this._updateDropdownClass('bottom');\n    }\n    if (this.appendTo) {\n      this._updateYPosition();\n    }\n    this._dropdown.style.opacity = '1';\n  }\n  _updateDropdownClass(currentPosition) {\n    CSS_POSITIONS.forEach(position => {\n      const REMOVE_CSS_CLASS = `ng-select-${position}`;\n      this._renderer.removeClass(this._dropdown, REMOVE_CSS_CLASS);\n      this._renderer.removeClass(this._select, REMOVE_CSS_CLASS);\n    });\n    const ADD_CSS_CLASS = `ng-select-${currentPosition}`;\n    this._renderer.addClass(this._dropdown, ADD_CSS_CLASS);\n    this._renderer.addClass(this._select, ADD_CSS_CLASS);\n  }\n  _handleScroll() {\n    this._zone.runOutsideAngular(() => {\n      fromEvent(this.scrollElementRef.nativeElement, 'scroll').pipe(takeUntil(this._destroy$), auditTime(0, SCROLL_SCHEDULER)).subscribe(e => {\n        const path = e.path || e.composedPath && e.composedPath();\n        if (!path || path.length === 0 && !e.target) {\n          return;\n        }\n        const scrollTop = !path || path.length === 0 ? e.target.scrollTop : path[0].scrollTop;\n        this._onContentScrolled(scrollTop);\n      });\n    });\n  }\n  _handleOutsideClick() {\n    if (!this._document) {\n      return;\n    }\n    this._zone.runOutsideAngular(() => {\n      merge(fromEvent(this._document, 'touchstart', {\n        capture: true\n      }), fromEvent(this._document, 'click', {\n        capture: true\n      })).pipe(takeUntil(this._destroy$)).subscribe($event => this._checkToClose($event));\n    });\n  }\n  _checkToClose($event) {\n    if (this._select.contains($event.target) || this._dropdown.contains($event.target)) {\n      return;\n    }\n    const path = $event.path || $event.composedPath && $event.composedPath();\n    if ($event.target && $event.target.shadowRoot && path && path[0] && this._select.contains(path[0])) {\n      return;\n    }\n    this._zone.run(() => this.outsideClick.emit());\n  }\n  _onItemsChange(items, firstChange) {\n    this.items = items || [];\n    this._scrollToEndFired = false;\n    this.itemsLength = items.length;\n    if (this.virtualScroll) {\n      this._updateItemsRange(firstChange);\n    } else {\n      this._setVirtualHeight();\n      this._updateItems(firstChange);\n    }\n  }\n  _updateItems(firstChange) {\n    this.update.emit(this.items);\n    if (firstChange === false) {\n      return;\n    }\n    this._zone.runOutsideAngular(() => {\n      Promise.resolve().then(() => {\n        const panelHeight = this._scrollablePanel.clientHeight;\n        this._panelService.setDimensions(0, panelHeight);\n        this._handleDropdownPosition();\n        this.scrollTo(this.markedItem, firstChange);\n      });\n    });\n  }\n  _updateItemsRange(firstChange) {\n    this._zone.runOutsideAngular(() => {\n      this._measureDimensions().then(() => {\n        if (firstChange) {\n          this._renderItemsRange(this._startOffset);\n          this._handleDropdownPosition();\n        } else {\n          this._renderItemsRange();\n        }\n      });\n    });\n  }\n  _onContentScrolled(scrollTop) {\n    if (this.virtualScroll) {\n      this._renderItemsRange(scrollTop);\n    }\n    this._lastScrollPosition = scrollTop;\n    this._fireScrollToEnd(scrollTop);\n  }\n  _updateVirtualHeight(height) {\n    if (this._updateScrollHeight) {\n      this._virtualPadding.style.height = `${height}px`;\n      this._updateScrollHeight = false;\n    }\n  }\n  _setVirtualHeight() {\n    if (!this._virtualPadding) {\n      return;\n    }\n    this._virtualPadding.style.height = `0px`;\n  }\n  _onItemsLengthChanged() {\n    this._updateScrollHeight = true;\n  }\n  _renderItemsRange(scrollTop = null) {\n    if (scrollTop && this._lastScrollPosition === scrollTop) {\n      return;\n    }\n    scrollTop = scrollTop || this._scrollablePanel.scrollTop;\n    const range = this._panelService.calculateItems(scrollTop, this.itemsLength, this.bufferAmount);\n    this._updateVirtualHeight(range.scrollHeight);\n    this._contentPanel.style.transform = `translateY(${range.topPadding}px)`;\n    this._zone.run(() => {\n      this.update.emit(this.items.slice(range.start, range.end));\n      this.scroll.emit({\n        start: range.start,\n        end: range.end\n      });\n    });\n    if (isDefined(scrollTop) && this._lastScrollPosition === 0) {\n      this._scrollablePanel.scrollTop = scrollTop;\n      this._lastScrollPosition = scrollTop;\n    }\n  }\n  _measureDimensions() {\n    if (this._panelService.dimensions.itemHeight > 0 || this.itemsLength === 0) {\n      return Promise.resolve(this._panelService.dimensions);\n    }\n    const [first] = this.items;\n    this.update.emit([first]);\n    return Promise.resolve().then(() => {\n      const option = this._dropdown.querySelector(`#${first.htmlId}`);\n      const optionHeight = option.clientHeight;\n      this._virtualPadding.style.height = `${optionHeight * this.itemsLength}px`;\n      const panelHeight = this._scrollablePanel.clientHeight;\n      this._panelService.setDimensions(optionHeight, panelHeight);\n      return this._panelService.dimensions;\n    });\n  }\n  _fireScrollToEnd(scrollTop) {\n    if (this._scrollToEndFired || scrollTop === 0) {\n      return;\n    }\n    const padding = this.virtualScroll ? this._virtualPadding : this._contentPanel;\n    if (scrollTop + this._dropdown.clientHeight >= padding.clientHeight - 1) {\n      this._zone.run(() => this.scrollToEnd.emit());\n      this._scrollToEndFired = true;\n    }\n  }\n  _calculateCurrentPosition(dropdownEl) {\n    if (this.position !== 'auto') {\n      return this.position;\n    }\n    const selectRect = this._select.getBoundingClientRect();\n    const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;\n    const offsetTop = selectRect.top + window.pageYOffset;\n    const height = selectRect.height;\n    const dropdownHeight = dropdownEl.getBoundingClientRect().height;\n    if (offsetTop + height + dropdownHeight > scrollTop + document.documentElement.clientHeight) {\n      return 'top';\n    } else {\n      return 'bottom';\n    }\n  }\n  _appendDropdown() {\n    if (!this.appendTo) {\n      return;\n    }\n    this._parent = this._dropdown.shadowRoot ? this._dropdown.shadowRoot.querySelector(this.appendTo) : document.querySelector(this.appendTo);\n    if (!this._parent) {\n      throw new Error(`appendTo selector ${this.appendTo} did not found any parent element`);\n    }\n    this._updateXPosition();\n    this._parent.appendChild(this._dropdown);\n  }\n  _updateXPosition() {\n    const select = this._select.getBoundingClientRect();\n    const parent = this._parent.getBoundingClientRect();\n    const offsetLeft = select.left - parent.left;\n    this._dropdown.style.left = offsetLeft + 'px';\n    this._dropdown.style.width = select.width + 'px';\n    this._dropdown.style.minWidth = select.width + 'px';\n  }\n  _updateYPosition() {\n    const select = this._select.getBoundingClientRect();\n    const parent = this._parent.getBoundingClientRect();\n    const delta = select.height;\n    if (this._currentPosition === 'top') {\n      const offsetBottom = parent.bottom - select.bottom;\n      this._dropdown.style.bottom = offsetBottom + delta + 'px';\n      this._dropdown.style.top = 'auto';\n    } else if (this._currentPosition === 'bottom') {\n      const offsetTop = select.top - parent.top;\n      this._dropdown.style.top = offsetTop + delta + 'px';\n      this._dropdown.style.bottom = 'auto';\n    }\n  }\n  _setupMousedownListener() {\n    this._zone.runOutsideAngular(() => {\n      fromEvent(this._dropdown, 'mousedown').pipe(takeUntil(this._destroy$)).subscribe(event => {\n        const target = event.target;\n        if (target.tagName === 'INPUT') {\n          return;\n        }\n        event.preventDefault();\n      });\n    });\n  }\n  static {\n    this.ɵfac = function NgDropdownPanelComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgDropdownPanelComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(NgDropdownPanelService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NgDropdownPanelComponent,\n      selectors: [[\"ng-dropdown-panel\"]],\n      viewQuery: function NgDropdownPanelComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7, ElementRef);\n          i0.ɵɵviewQuery(_c1, 7, ElementRef);\n          i0.ɵɵviewQuery(_c2, 7, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentElementRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollElementRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paddingElementRef = _t.first);\n        }\n      },\n      inputs: {\n        items: \"items\",\n        markedItem: \"markedItem\",\n        position: \"position\",\n        appendTo: \"appendTo\",\n        bufferAmount: \"bufferAmount\",\n        virtualScroll: [2, \"virtualScroll\", \"virtualScroll\", booleanAttribute],\n        headerTemplate: \"headerTemplate\",\n        footerTemplate: \"footerTemplate\",\n        filterValue: \"filterValue\",\n        ariaLabelDropdown: [1, \"ariaLabelDropdown\"]\n      },\n      outputs: {\n        update: \"update\",\n        scroll: \"scroll\",\n        scrollToEnd: \"scrollToEnd\",\n        outsideClick: \"outsideClick\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c3,\n      decls: 9,\n      vars: 7,\n      consts: [[\"scroll\", \"\"], [\"padding\", \"\"], [\"content\", \"\"], [1, \"ng-dropdown-header\"], [\"role\", \"listbox\", 1, \"ng-dropdown-panel-items\", \"scroll-host\"], [1, \"ng-dropdown-footer\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function NgDropdownPanelComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NgDropdownPanelComponent_Conditional_0_Template, 2, 4, \"div\", 3);\n          i0.ɵɵelementStart(1, \"div\", 4, 0);\n          i0.ɵɵelement(3, \"div\", null, 1);\n          i0.ɵɵelementStart(5, \"div\", null, 2);\n          i0.ɵɵprojection(7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(8, NgDropdownPanelComponent_Conditional_8_Template, 2, 4, \"div\", 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.headerTemplate ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", ctx.ariaLabelDropdown());\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"total-padding\", ctx.virtualScroll);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"scrollable-content\", ctx.virtualScroll && ctx.items.length);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(ctx.footerTemplate ? 8 : -1);\n        }\n      },\n      dependencies: [NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgDropdownPanelComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'ng-dropdown-panel',\n      template: `\n\t\t@if (headerTemplate) {\n\t\t\t<div class=\"ng-dropdown-header\">\n\t\t\t\t<ng-container [ngTemplateOutlet]=\"headerTemplate\" [ngTemplateOutletContext]=\"{ searchTerm: filterValue }\" />\n\t\t\t</div>\n\t\t}\n\t\t<div #scroll role=\"listbox\" class=\"ng-dropdown-panel-items scroll-host\" [attr.aria-label]=\"ariaLabelDropdown()\">\n\t\t\t<div #padding [class.total-padding]=\"virtualScroll\"></div>\n\t\t\t<div #content [class.scrollable-content]=\"virtualScroll && items.length\">\n\t\t\t\t<ng-content />\n\t\t\t</div>\n\t\t</div>\n\t\t@if (footerTemplate) {\n\t\t\t<div class=\"ng-dropdown-footer\">\n\t\t\t\t<ng-container [ngTemplateOutlet]=\"footerTemplate\" [ngTemplateOutletContext]=\"{ searchTerm: filterValue }\" />\n\t\t\t</div>\n\t\t}\n\t`,\n      imports: [NgTemplateOutlet]\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.NgZone\n  }, {\n    type: NgDropdownPanelService\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], {\n    items: [{\n      type: Input\n    }],\n    markedItem: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    bufferAmount: [{\n      type: Input\n    }],\n    virtualScroll: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    headerTemplate: [{\n      type: Input\n    }],\n    footerTemplate: [{\n      type: Input\n    }],\n    filterValue: [{\n      type: Input\n    }],\n    update: [{\n      type: Output\n    }],\n    scroll: [{\n      type: Output\n    }],\n    scrollToEnd: [{\n      type: Output\n    }],\n    outsideClick: [{\n      type: Output\n    }],\n    contentElementRef: [{\n      type: ViewChild,\n      args: ['content', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    scrollElementRef: [{\n      type: ViewChild,\n      args: ['scroll', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    paddingElementRef: [{\n      type: ViewChild,\n      args: ['padding', {\n        read: ElementRef,\n        static: true\n      }]\n    }]\n  });\n})();\nclass NgOptionComponent {\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n    this.disabled = false;\n    this.stateChange$ = new Subject();\n  }\n  get label() {\n    return (this.elementRef.nativeElement.textContent || '').trim();\n  }\n  ngOnChanges(changes) {\n    if (changes.disabled) {\n      this.stateChange$.next({\n        value: this.value,\n        disabled: this.disabled\n      });\n    }\n  }\n  ngAfterViewChecked() {\n    if (this.label !== this._previousLabel) {\n      this._previousLabel = this.label;\n      this.stateChange$.next({\n        value: this.value,\n        disabled: this.disabled,\n        label: this.elementRef.nativeElement.innerHTML\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.stateChange$.complete();\n  }\n  static {\n    this.ɵfac = function NgOptionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgOptionComponent)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NgOptionComponent,\n      selectors: [[\"ng-option\"]],\n      inputs: {\n        value: \"value\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c3,\n      decls: 1,\n      vars: 0,\n      template: function NgOptionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgOptionComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ng-option',\n      standalone: true,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `<ng-content />`\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    value: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nvar KeyCode;\n(function (KeyCode) {\n  KeyCode[\"Tab\"] = \"Tab\";\n  KeyCode[\"Enter\"] = \"Enter\";\n  KeyCode[\"Esc\"] = \"Escape\";\n  KeyCode[\"Space\"] = \" \";\n  KeyCode[\"ArrowUp\"] = \"ArrowUp\";\n  KeyCode[\"ArrowDown\"] = \"ArrowDown\";\n  KeyCode[\"Backspace\"] = \"Backspace\";\n})(KeyCode || (KeyCode = {}));\nfunction DefaultSelectionModelFactory() {\n  return new DefaultSelectionModel();\n}\nclass DefaultSelectionModel {\n  constructor() {\n    this._selected = [];\n  }\n  get value() {\n    return this._selected;\n  }\n  select(item, multiple, groupAsModel) {\n    item.selected = true;\n    if (!item.children || !multiple && groupAsModel) {\n      this._selected.push(item);\n    }\n    if (multiple) {\n      if (item.parent) {\n        const childrenCount = item.parent.children.length;\n        const selectedCount = item.parent.children.filter(x => x.selected).length;\n        item.parent.selected = childrenCount === selectedCount;\n      } else if (item.children) {\n        this._setChildrenSelectedState(item.children, true);\n        this._removeChildren(item);\n        if (groupAsModel && this._activeChildren(item)) {\n          this._selected = [...this._selected.filter(x => x.parent !== item), item];\n        } else {\n          this._selected = [...this._selected, ...item.children.filter(x => !x.disabled)];\n        }\n      }\n    }\n  }\n  unselect(item, multiple) {\n    this._selected = this._selected.filter(x => x !== item);\n    item.selected = false;\n    if (multiple) {\n      if (item.parent && item.parent.selected) {\n        const children = item.parent.children;\n        this._removeParent(item.parent);\n        this._removeChildren(item.parent);\n        this._selected.push(...children.filter(x => x !== item && !x.disabled));\n        item.parent.selected = false;\n      } else if (item.children) {\n        this._setChildrenSelectedState(item.children, false);\n        this._removeChildren(item);\n      }\n    }\n  }\n  clear(keepDisabled) {\n    this._selected = keepDisabled ? this._selected.filter(x => x.disabled) : [];\n  }\n  _setChildrenSelectedState(children, selected) {\n    for (const child of children) {\n      if (child.disabled) {\n        continue;\n      }\n      child.selected = selected;\n    }\n  }\n  _removeChildren(parent) {\n    this._selected = [...this._selected.filter(x => x.parent !== parent), ...parent.children.filter(x => x.parent === parent && x.disabled && x.selected)];\n  }\n  _removeParent(parent) {\n    this._selected = this._selected.filter(x => x !== parent);\n  }\n  _activeChildren(item) {\n    return item.children.every(x => !x.disabled || x.selected);\n  }\n}\nclass NgSelectConfig {\n  constructor() {\n    this.fixedPlaceholder = false;\n    this.notFoundText = 'No items found';\n    this.typeToSearchText = 'Type to search';\n    this.addTagText = 'Add item';\n    this.loadingText = 'Loading...';\n    this.clearAllText = 'Clear all';\n    this.disableVirtualScroll = true;\n    this.openOnEnter = true;\n    this.appearance = 'underline';\n  }\n  static {\n    this.ɵfac = function NgSelectConfig_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgSelectConfig)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NgSelectConfig,\n      factory: NgSelectConfig.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgSelectConfig, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass ConsoleService {\n  warn(message) {\n    console.warn(message);\n  }\n  static {\n    this.ɵfac = function ConsoleService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ConsoleService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ConsoleService,\n      factory: ConsoleService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConsoleService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst SELECTION_MODEL_FACTORY = new InjectionToken('ng-select-selection-model');\nclass NgSelectComponent {\n  constructor(classes, autoFocus, config, newSelectionModel, _elementRef, _cd, _console) {\n    this.classes = classes;\n    this.autoFocus = autoFocus;\n    this.config = config;\n    this._cd = _cd;\n    this._console = _console;\n    this.ariaLabelDropdown = 'Options List';\n    this.markFirst = true;\n    this.fixedPlaceholder = false;\n    this.preventToggleOnRightClick = false;\n    this.dropdownPosition = 'auto';\n    this.loading = false;\n    this.closeOnSelect = true;\n    this.hideSelected = false;\n    this.selectOnTab = false;\n    this.bufferAmount = 4;\n    this.selectableGroup = false;\n    this.selectableGroupAsModel = true;\n    this.searchFn = null;\n    this.trackByFn = null;\n    this.clearOnBackspace = true;\n    this.labelForId = null;\n    this.inputAttrs = {};\n    this.tabFocusOnClearButton = input(true, {\n      transform: booleanAttribute\n    });\n    this.readonly = false;\n    this.searchWhileComposing = true;\n    this.minTermLength = 0;\n    this.editableSearchTerm = false;\n    this.ngClass = null;\n    this.multiple = false;\n    this.addTag = false;\n    this.searchable = true;\n    this.clearable = true;\n    this.isOpen = false;\n    // output events\n    this.blurEvent = new EventEmitter();\n    this.focusEvent = new EventEmitter();\n    this.changeEvent = new EventEmitter();\n    this.openEvent = new EventEmitter();\n    this.closeEvent = new EventEmitter();\n    this.searchEvent = new EventEmitter();\n    this.clearEvent = new EventEmitter();\n    this.addEvent = new EventEmitter();\n    this.removeEvent = new EventEmitter();\n    this.scroll = new EventEmitter();\n    this.scrollToEnd = new EventEmitter();\n    this.useDefaultClass = true;\n    this.viewPortItems = [];\n    this.searchTerm = null;\n    this.dropdownId = newId();\n    this.escapeHTML = true;\n    this._defaultLabel = 'label';\n    this._pressedKeys = [];\n    this._isComposing = false;\n    this._destroy$ = new Subject();\n    this._keyPress$ = new Subject();\n    this._items = [];\n    this.keyDownFn = _ => true;\n    this.clearItem = item => {\n      const option = this.selectedItems.find(x => x.value === item);\n      this.unselect(option);\n    };\n    this.trackByOption = (_, item) => {\n      if (this.trackByFn) {\n        return this.trackByFn(item.value);\n      }\n      return item;\n    };\n    this._onChange = _ => {};\n    this._onTouched = () => {};\n    this._mergeGlobalConfig(config);\n    this.itemsList = new ItemsList(this, newSelectionModel ? newSelectionModel() : DefaultSelectionModelFactory());\n    this.element = _elementRef.nativeElement;\n  }\n  get filtered() {\n    return !!this.searchTerm && this.searchable || this._isComposing;\n  }\n  get single() {\n    return !this.multiple;\n  }\n  get items() {\n    return this._items;\n  }\n  set items(value) {\n    this._itemsAreUsed = true;\n    this._items = value ?? [];\n  }\n  get disabled() {\n    return this.readonly || this._disabled;\n  }\n  get compareWith() {\n    return this._compareWith;\n  }\n  set compareWith(fn) {\n    if (fn !== undefined && fn !== null && !isFunction(fn)) {\n      throw Error('`compareWith` must be a function.');\n    }\n    this._compareWith = fn;\n  }\n  get clearSearchOnAdd() {\n    if (isDefined(this._clearSearchOnAdd)) {\n      return this._clearSearchOnAdd;\n    } else if (isDefined(this.config.clearSearchOnAdd)) {\n      return this.config.clearSearchOnAdd;\n    }\n    return this.closeOnSelect;\n  }\n  set clearSearchOnAdd(value) {\n    this._clearSearchOnAdd = value;\n  }\n  get deselectOnClick() {\n    if (isDefined(this._deselectOnClick)) {\n      return this._deselectOnClick;\n    } else if (isDefined(this.config.deselectOnClick)) {\n      return this.config.deselectOnClick;\n    }\n    return this.multiple;\n  }\n  set deselectOnClick(value) {\n    this._deselectOnClick = value;\n  }\n  get selectedItems() {\n    return this.itemsList.selectedItems;\n  }\n  get selectedValues() {\n    return this.selectedItems.map(x => x.value);\n  }\n  get hasValue() {\n    return this.selectedItems.length > 0;\n  }\n  get currentPanelPosition() {\n    if (this.dropdownPanel) {\n      return this.dropdownPanel.currentPosition;\n    }\n    return undefined;\n  }\n  get showAddTag() {\n    if (!this._validTerm) {\n      return false;\n    }\n    const term = this.searchTerm.toLowerCase().trim();\n    return this.addTag && !this.itemsList.filteredItems.some(x => x.label.toLowerCase() === term) && (!this.hideSelected && this.isOpen || !this.selectedItems.some(x => x.label.toLowerCase() === term)) && !this.loading;\n  }\n  get _editableSearchTerm() {\n    return this.editableSearchTerm && !this.multiple;\n  }\n  get _isTypeahead() {\n    return this.typeahead && this.typeahead.observers.length > 0;\n  }\n  get _validTerm() {\n    const term = this.searchTerm?.trim();\n    return term && term.length >= this.minTermLength;\n  }\n  ngOnInit() {\n    this._handleKeyPresses();\n    this._setInputAttributes();\n  }\n  ngOnChanges(changes) {\n    if (changes.multiple) {\n      this.itemsList.clearSelected();\n    }\n    if (changes.items) {\n      this._setItems(changes.items.currentValue || []);\n    }\n    if (changes.isOpen) {\n      this._manualOpen = isDefined(changes.isOpen.currentValue);\n    }\n    if (changes.groupBy) {\n      if (!changes.items) {\n        this._setItems([...this.items]);\n      }\n    }\n    if (changes.inputAttrs) {\n      this._setInputAttributes();\n    }\n  }\n  ngAfterViewInit() {\n    if (!this._itemsAreUsed) {\n      this.escapeHTML = false;\n      this._setItemsFromNgOptions();\n    }\n    if (isDefined(this.autoFocus)) {\n      this.focus();\n    }\n  }\n  ngOnDestroy() {\n    this._destroy$.next();\n    this._destroy$.complete();\n  }\n  handleKeyDown($event) {\n    const keyName = $event.key;\n    if (Object.values(KeyCode).includes(keyName)) {\n      if (this.keyDownFn($event) === false) {\n        return;\n      }\n      this.handleKeyCode($event);\n    } else if (keyName && keyName.length === 1) {\n      this._keyPress$.next(keyName.toLocaleLowerCase());\n    }\n  }\n  handleKeyCode($event) {\n    const target = $event.target;\n    if (this.clearButton && this.clearButton.nativeElement === target) {\n      this.handleKeyCodeClear($event);\n    } else {\n      this.handleKeyCodeInput($event);\n    }\n  }\n  handleKeyCodeInput($event) {\n    switch ($event.key) {\n      case KeyCode.ArrowDown:\n        this._handleArrowDown($event);\n        break;\n      case KeyCode.ArrowUp:\n        this._handleArrowUp($event);\n        break;\n      case KeyCode.Space:\n        this._handleSpace($event);\n        break;\n      case KeyCode.Enter:\n        this._handleEnter($event);\n        break;\n      case KeyCode.Tab:\n        this._handleTab($event);\n        break;\n      case KeyCode.Esc:\n        this.close();\n        $event.preventDefault();\n        break;\n      case KeyCode.Backspace:\n        this._handleBackspace();\n        break;\n    }\n  }\n  handleKeyCodeClear($event) {\n    switch ($event.key) {\n      case KeyCode.Enter:\n        this.handleClearClick();\n        $event.preventDefault();\n        break;\n    }\n  }\n  handleMousedown($event) {\n    if (this.preventToggleOnRightClick && $event.button === 2) {\n      return false;\n    }\n    const target = $event.target;\n    if (target.tagName !== 'INPUT') {\n      $event.preventDefault();\n    }\n    if (target.classList.contains('ng-clear-wrapper')) {\n      this.handleClearClick();\n      return;\n    }\n    if (target.classList.contains('ng-arrow-wrapper')) {\n      this.handleArrowClick();\n      return;\n    }\n    if (target.classList.contains('ng-value-icon')) {\n      return;\n    }\n    if (!this.focused) {\n      this.focus();\n    }\n    if (this.searchable) {\n      this.open();\n    } else {\n      this.toggle();\n    }\n  }\n  handleArrowClick() {\n    if (this.isOpen) {\n      this.close();\n    } else {\n      this.open();\n    }\n  }\n  handleClearClick() {\n    if (this.hasValue) {\n      this.itemsList.clearSelected(true);\n      this._updateNgModel();\n    }\n    this._clearSearch();\n    this.focus();\n    this.clearEvent.emit();\n    this._onSelectionChanged();\n  }\n  clearModel() {\n    if (!this.clearable) {\n      return;\n    }\n    this.itemsList.clearSelected();\n    this._updateNgModel();\n  }\n  writeValue(value) {\n    this.itemsList.clearSelected();\n    this._handleWriteValue(value);\n    this._cd.markForCheck();\n  }\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  setDisabledState(state) {\n    this._disabled = state;\n    this._cd.markForCheck();\n  }\n  toggle() {\n    if (!this.isOpen) {\n      this.open();\n    } else {\n      this.close();\n    }\n  }\n  open() {\n    if (this.disabled || this.isOpen || this._manualOpen) {\n      return;\n    }\n    if (!this._isTypeahead && !this.addTag && this.itemsList.noItemsToSelect) {\n      return;\n    }\n    this.isOpen = true;\n    this.itemsList.markSelectedOrDefault(this.markFirst);\n    this.openEvent.emit();\n    if (!this.searchTerm) {\n      this.focus();\n    }\n    this.detectChanges();\n  }\n  close() {\n    if (!this.isOpen || this._manualOpen) {\n      return;\n    }\n    this.isOpen = false;\n    this._isComposing = false;\n    if (!this._editableSearchTerm) {\n      this._clearSearch();\n    } else {\n      this.itemsList.resetFilteredItems();\n    }\n    this.itemsList.unmarkItem();\n    this._onTouched();\n    this.closeEvent.emit();\n    this._cd.markForCheck();\n  }\n  toggleItem(item) {\n    if (!item || item.disabled || this.disabled) {\n      return;\n    }\n    if (this.deselectOnClick && item.selected) {\n      this.unselect(item);\n    } else {\n      this.select(item);\n    }\n    if (this._editableSearchTerm) {\n      this._setSearchTermFromItems();\n    }\n  }\n  select(item) {\n    if (!item.selected) {\n      this.itemsList.select(item);\n      if (this.clearSearchOnAdd && !this._editableSearchTerm) {\n        this._clearSearch();\n      }\n      this._updateNgModel();\n      if (this.multiple) {\n        this.addEvent.emit(item.value);\n      }\n    }\n    if (this.closeOnSelect || this.itemsList.noItemsToSelect) {\n      this.close();\n    }\n    this._onSelectionChanged();\n  }\n  focus() {\n    this.searchInput.nativeElement.focus();\n  }\n  blur() {\n    this.searchInput.nativeElement.blur();\n  }\n  unselect(item) {\n    if (!item) {\n      return;\n    }\n    this.itemsList.unselect(item);\n    this.focus();\n    this._updateNgModel();\n    this.removeEvent.emit(item.value);\n    this._onSelectionChanged();\n  }\n  selectTag() {\n    let tag;\n    if (isFunction(this.addTag)) {\n      tag = this.addTag(this.searchTerm);\n    } else {\n      tag = this._primitive ? this.searchTerm : {\n        [this.bindLabel]: this.searchTerm\n      };\n    }\n    const handleTag = item => this._isTypeahead || !this.isOpen ? this.itemsList.mapItem(item, null) : this.itemsList.addItem(item);\n    if (isPromise(tag)) {\n      tag.then(item => this.select(handleTag(item))).catch(() => {});\n    } else if (tag) {\n      this.select(handleTag(tag));\n    }\n  }\n  showClear() {\n    return this.clearable && (this.hasValue || this.searchTerm) && !this.disabled;\n  }\n  focusOnClear() {\n    this.blur();\n    if (this.clearButton) {\n      this.clearButton.nativeElement.focus();\n    }\n  }\n  showNoItemsFound() {\n    const empty = this.itemsList.filteredItems.length === 0;\n    return (empty && !this._isTypeahead && !this.loading || empty && this._isTypeahead && this._validTerm && !this.loading) && !this.showAddTag;\n  }\n  showTypeToSearch() {\n    const empty = this.itemsList.filteredItems.length === 0;\n    return empty && this._isTypeahead && !this._validTerm && !this.loading;\n  }\n  onCompositionStart() {\n    this._isComposing = true;\n  }\n  onCompositionEnd(term) {\n    this._isComposing = false;\n    if (this.searchWhileComposing) {\n      return;\n    }\n    this.filter(term);\n  }\n  filter(term) {\n    if (this._isComposing && !this.searchWhileComposing) {\n      return;\n    }\n    this.searchTerm = term;\n    if (this._isTypeahead && (this._validTerm || this.minTermLength === 0)) {\n      this.typeahead.next(term);\n    }\n    if (!this._isTypeahead) {\n      this.itemsList.filter(this.searchTerm);\n      if (this.isOpen) {\n        this.itemsList.markSelectedOrDefault(this.markFirst);\n      }\n    }\n    this.searchEvent.emit({\n      term,\n      items: this.itemsList.filteredItems.map(x => x.value)\n    });\n    this.open();\n  }\n  onInputFocus($event) {\n    if (this.focused) {\n      return;\n    }\n    if (this._editableSearchTerm) {\n      this._setSearchTermFromItems();\n    }\n    this.element.classList.add('ng-select-focused');\n    this.focusEvent.emit($event);\n    this.focused = true;\n  }\n  onInputBlur($event) {\n    this.element.classList.remove('ng-select-focused');\n    this.blurEvent.emit($event);\n    if (!this.isOpen && !this.disabled) {\n      this._onTouched();\n    }\n    if (this._editableSearchTerm) {\n      this._setSearchTermFromItems();\n    }\n    this.focused = false;\n  }\n  onItemHover(item) {\n    if (item.disabled) {\n      return;\n    }\n    this.itemsList.markItem(item);\n  }\n  detectChanges() {\n    if (!this._cd.destroyed) {\n      this._cd.detectChanges();\n    }\n  }\n  _setSearchTermFromItems() {\n    const selected = this.selectedItems?.[0];\n    this.searchTerm = selected?.label ?? null;\n  }\n  _setItems(items) {\n    const firstItem = items[0];\n    this.bindLabel = this.bindLabel || this._defaultLabel;\n    this._primitive = isDefined(firstItem) ? !isObject(firstItem) : this._primitive || this.bindLabel === this._defaultLabel;\n    this.itemsList.setItems(items);\n    if (items.length > 0 && this.hasValue) {\n      this.itemsList.mapSelectedItems();\n    }\n    if (this.isOpen && isDefined(this.searchTerm) && !this._isTypeahead) {\n      this.itemsList.filter(this.searchTerm);\n    }\n    if (this._isTypeahead || this.isOpen) {\n      this.itemsList.markSelectedOrDefault(this.markFirst);\n    }\n  }\n  _setItemsFromNgOptions() {\n    const mapNgOptions = options => {\n      this.items = options.map(option => ({\n        $ngOptionValue: option.value,\n        $ngOptionLabel: option.elementRef.nativeElement.innerHTML,\n        disabled: option.disabled\n      }));\n      this.itemsList.setItems(this.items);\n      if (this.hasValue) {\n        this.itemsList.mapSelectedItems();\n      }\n      this.detectChanges();\n    };\n    const handleOptionChange = () => {\n      const changedOrDestroyed = merge(this.ngOptions.changes, this._destroy$);\n      merge(...this.ngOptions.map(option => option.stateChange$)).pipe(takeUntil(changedOrDestroyed)).subscribe(option => {\n        const item = this.itemsList.findItem(option.value);\n        item.disabled = option.disabled;\n        item.label = option.label || item.label;\n        this._cd.detectChanges();\n      });\n    };\n    this.ngOptions.changes.pipe(startWith(this.ngOptions), takeUntil(this._destroy$)).subscribe(options => {\n      this.bindLabel = this._defaultLabel;\n      mapNgOptions(options);\n      handleOptionChange();\n    });\n  }\n  _isValidWriteValue(value) {\n    if (!isDefined(value) || this.multiple && value === '' || Array.isArray(value) && value.length === 0) {\n      return false;\n    }\n    const validateBinding = item => {\n      if (!isDefined(this.compareWith) && isObject(item) && this.bindValue) {\n        this._console.warn(`Setting object(${JSON.stringify(item)}) as your model with bindValue is not allowed unless [compareWith] is used.`);\n        return false;\n      }\n      return true;\n    };\n    if (this.multiple) {\n      if (!Array.isArray(value)) {\n        this._console.warn('Multiple select ngModel should be array.');\n        return false;\n      }\n      return value.every(item => validateBinding(item));\n    } else {\n      return validateBinding(value);\n    }\n  }\n  _handleWriteValue(ngModel) {\n    if (!this._isValidWriteValue(ngModel)) {\n      return;\n    }\n    const select = val => {\n      let item = this.itemsList.findItem(val);\n      if (item) {\n        this.itemsList.select(item);\n      } else {\n        const isValObject = isObject(val);\n        const isPrimitive = !isValObject && !this.bindValue;\n        if (isValObject || isPrimitive) {\n          this.itemsList.select(this.itemsList.mapItem(val, null));\n        } else if (this.bindValue) {\n          item = {\n            [this.bindLabel]: null,\n            [this.bindValue]: val\n          };\n          this.itemsList.select(this.itemsList.mapItem(item, null));\n        }\n      }\n    };\n    if (this.multiple) {\n      ngModel.forEach(item => select(item));\n    } else {\n      select(ngModel);\n    }\n  }\n  _handleKeyPresses() {\n    if (this.searchable) {\n      return;\n    }\n    this._keyPress$.pipe(takeUntil(this._destroy$), tap(letter => this._pressedKeys.push(letter)), debounceTime(200), filter(() => this._pressedKeys.length > 0), map(() => this._pressedKeys.join(''))).subscribe(term => {\n      const item = this.itemsList.findByLabel(term);\n      if (item) {\n        if (this.isOpen) {\n          this.itemsList.markItem(item);\n          this._scrollToMarked();\n          this._cd.markForCheck();\n        } else {\n          this.select(item);\n        }\n      }\n      this._pressedKeys = [];\n    });\n  }\n  _setInputAttributes() {\n    const input = this.searchInput.nativeElement;\n    const attributes = {\n      type: 'text',\n      autocorrect: 'off',\n      autocapitalize: 'off',\n      autocomplete: 'off',\n      'aria-controls': this.dropdownId,\n      ...this.inputAttrs\n    };\n    for (const key of Object.keys(attributes)) {\n      input.setAttribute(key, attributes[key]);\n    }\n  }\n  _updateNgModel() {\n    const model = [];\n    for (const item of this.selectedItems) {\n      if (this.bindValue) {\n        let value = null;\n        if (item.children) {\n          const groupKey = this.groupValue ? this.bindValue : this.groupBy;\n          value = item.value[groupKey || this.groupBy];\n        } else {\n          value = this.itemsList.resolveNested(item.value, this.bindValue);\n        }\n        model.push(value);\n      } else {\n        model.push(item.value);\n      }\n    }\n    const selected = this.selectedItems.map(x => x.value);\n    if (this.multiple) {\n      this._onChange(model);\n      this.changeEvent.emit(selected);\n    } else {\n      this._onChange(isDefined(model[0]) ? model[0] : null);\n      this.changeEvent.emit(selected[0]);\n    }\n    this._cd.markForCheck();\n  }\n  _clearSearch() {\n    if (!this.searchTerm) {\n      return;\n    }\n    this._changeSearch(null);\n    this.itemsList.resetFilteredItems();\n  }\n  _changeSearch(searchTerm) {\n    this.searchTerm = searchTerm;\n    if (this._isTypeahead) {\n      this.typeahead.next(searchTerm);\n    }\n  }\n  _scrollToMarked() {\n    if (!this.isOpen || !this.dropdownPanel) {\n      return;\n    }\n    this.dropdownPanel.scrollTo(this.itemsList.markedItem);\n  }\n  _scrollToTag() {\n    if (!this.isOpen || !this.dropdownPanel) {\n      return;\n    }\n    this.dropdownPanel.scrollToTag();\n  }\n  _onSelectionChanged() {\n    if (this.isOpen && this.deselectOnClick && this.appendTo) {\n      // Make sure items are rendered.\n      this._cd.detectChanges();\n      this.dropdownPanel.adjustPosition();\n    }\n  }\n  _handleTab($event) {\n    if (this.isOpen === false) {\n      if (this.showClear() && !$event.shiftKey && this.tabFocusOnClearButton()) {\n        this.focusOnClear();\n        $event.preventDefault();\n      } else if (!this.addTag) {\n        return;\n      }\n    }\n    if (this.selectOnTab) {\n      if (this.itemsList.markedItem) {\n        this.toggleItem(this.itemsList.markedItem);\n        $event.preventDefault();\n      } else if (this.showAddTag) {\n        this.selectTag();\n        $event.preventDefault();\n      } else {\n        this.close();\n      }\n    } else {\n      this.close();\n    }\n  }\n  _handleEnter($event) {\n    if (this.isOpen || this._manualOpen) {\n      if (this.itemsList.markedItem) {\n        this.toggleItem(this.itemsList.markedItem);\n      } else if (this.showAddTag) {\n        this.selectTag();\n      }\n    } else if (this.openOnEnter) {\n      this.open();\n    } else {\n      return;\n    }\n    $event.preventDefault();\n  }\n  _handleSpace($event) {\n    if (this.isOpen || this._manualOpen) {\n      return;\n    }\n    this.open();\n    $event.preventDefault();\n  }\n  _handleArrowDown($event) {\n    if (this._nextItemIsTag(+1)) {\n      this.itemsList.unmarkItem();\n      this._scrollToTag();\n    } else {\n      this.itemsList.markNextItem();\n      this._scrollToMarked();\n    }\n    this.open();\n    $event.preventDefault();\n  }\n  _handleArrowUp($event) {\n    if (!this.isOpen) {\n      return;\n    }\n    if (this._nextItemIsTag(-1)) {\n      this.itemsList.unmarkItem();\n      this._scrollToTag();\n    } else {\n      this.itemsList.markPreviousItem();\n      this._scrollToMarked();\n    }\n    $event.preventDefault();\n  }\n  _nextItemIsTag(nextStep) {\n    const nextIndex = this.itemsList.markedIndex + nextStep;\n    return this.addTag && this.searchTerm && this.itemsList.markedItem && (nextIndex < 0 || nextIndex === this.itemsList.filteredItems.length);\n  }\n  _handleBackspace() {\n    if (this.searchTerm || !this.clearable || !this.clearOnBackspace || !this.hasValue) {\n      return;\n    }\n    if (this.multiple) {\n      this.unselect(this.itemsList.lastSelectedItem);\n    } else {\n      this.clearModel();\n    }\n  }\n  _mergeGlobalConfig(config) {\n    this.placeholder = this.placeholder || config.placeholder;\n    this.fixedPlaceholder = this.fixedPlaceholder || config.fixedPlaceholder;\n    this.notFoundText = this.notFoundText || config.notFoundText;\n    this.typeToSearchText = this.typeToSearchText || config.typeToSearchText;\n    this.addTagText = this.addTagText || config.addTagText;\n    this.loadingText = this.loadingText || config.loadingText;\n    this.clearAllText = this.clearAllText || config.clearAllText;\n    this.virtualScroll = this.getVirtualScroll(config);\n    this.openOnEnter = isDefined(this.openOnEnter) ? this.openOnEnter : config.openOnEnter;\n    this.appendTo = this.appendTo || config.appendTo;\n    this.bindValue = this.bindValue || config.bindValue;\n    this.bindLabel = this.bindLabel || config.bindLabel;\n    this.appearance = this.appearance || config.appearance;\n  }\n  /**\n   * Gets virtual scroll value from input or from config\n   *\n   *  @param config NgSelectConfig object\n   *\n   *  @returns `true` if virtual scroll is enabled, `false` otherwise\n   */\n  getVirtualScroll(config) {\n    return isDefined(this.virtualScroll) ? this.virtualScroll : this.isVirtualScrollDisabled(config);\n  }\n  /**\n   * Gets disableVirtualScroll value from input or from config\n   *\n   *  @param config NgSelectConfig object\n   *\n   *  @returns `true` if disableVirtualScroll is enabled, `false` otherwise\n   */\n  isVirtualScrollDisabled(config) {\n    return isDefined(config.disableVirtualScroll) ? !config.disableVirtualScroll : false;\n  }\n  static {\n    this.ɵfac = function NgSelectComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgSelectComponent)(i0.ɵɵinjectAttribute('class'), i0.ɵɵinjectAttribute('autofocus'), i0.ɵɵdirectiveInject(NgSelectConfig), i0.ɵɵdirectiveInject(SELECTION_MODEL_FACTORY, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(ConsoleService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NgSelectComponent,\n      selectors: [[\"ng-select\"]],\n      contentQueries: function NgSelectComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NgOptionTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgOptgroupTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgLabelTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgMultiLabelTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgHeaderTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgFooterTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgNotFoundTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgPlaceholderTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgTypeToSearchTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgLoadingTextTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgTagTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgLoadingSpinnerTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgClearButtonTemplateDirective, 5, TemplateRef);\n          i0.ɵɵcontentQuery(dirIndex, NgOptionComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optgroupTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.labelTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.multiLabelTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.notFoundTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.placeholderTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.typeToSearchTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.loadingTextTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tagTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.loadingSpinnerTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.clearButtonTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ngOptions = _t);\n        }\n      },\n      viewQuery: function NgSelectComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(NgDropdownPanelComponent, 5);\n          i0.ɵɵviewQuery(_c5, 7);\n          i0.ɵɵviewQuery(_c6, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dropdownPanel = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInput = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.clearButton = _t.first);\n        }\n      },\n      hostVars: 20,\n      hostBindings: function NgSelectComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function NgSelectComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeyDown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ng-select-typeahead\", ctx.typeahead)(\"ng-select-multiple\", ctx.multiple)(\"ng-select-taggable\", ctx.addTag)(\"ng-select-searchable\", ctx.searchable)(\"ng-select-clearable\", ctx.clearable)(\"ng-select-opened\", ctx.isOpen)(\"ng-select\", ctx.useDefaultClass)(\"ng-select-filtered\", ctx.filtered)(\"ng-select-single\", ctx.single)(\"ng-select-disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        ariaLabelDropdown: \"ariaLabelDropdown\",\n        bindLabel: \"bindLabel\",\n        bindValue: \"bindValue\",\n        ariaLabel: \"ariaLabel\",\n        markFirst: [2, \"markFirst\", \"markFirst\", booleanAttribute],\n        placeholder: \"placeholder\",\n        fixedPlaceholder: \"fixedPlaceholder\",\n        notFoundText: \"notFoundText\",\n        typeToSearchText: \"typeToSearchText\",\n        preventToggleOnRightClick: \"preventToggleOnRightClick\",\n        addTagText: \"addTagText\",\n        loadingText: \"loadingText\",\n        clearAllText: \"clearAllText\",\n        appearance: \"appearance\",\n        dropdownPosition: \"dropdownPosition\",\n        appendTo: \"appendTo\",\n        loading: [2, \"loading\", \"loading\", booleanAttribute],\n        closeOnSelect: [2, \"closeOnSelect\", \"closeOnSelect\", booleanAttribute],\n        hideSelected: [2, \"hideSelected\", \"hideSelected\", booleanAttribute],\n        selectOnTab: [2, \"selectOnTab\", \"selectOnTab\", booleanAttribute],\n        openOnEnter: [2, \"openOnEnter\", \"openOnEnter\", booleanAttribute],\n        maxSelectedItems: [2, \"maxSelectedItems\", \"maxSelectedItems\", numberAttribute],\n        groupBy: \"groupBy\",\n        groupValue: \"groupValue\",\n        bufferAmount: [2, \"bufferAmount\", \"bufferAmount\", numberAttribute],\n        virtualScroll: [2, \"virtualScroll\", \"virtualScroll\", booleanAttribute],\n        selectableGroup: [2, \"selectableGroup\", \"selectableGroup\", booleanAttribute],\n        selectableGroupAsModel: [2, \"selectableGroupAsModel\", \"selectableGroupAsModel\", booleanAttribute],\n        searchFn: \"searchFn\",\n        trackByFn: \"trackByFn\",\n        clearOnBackspace: [2, \"clearOnBackspace\", \"clearOnBackspace\", booleanAttribute],\n        labelForId: \"labelForId\",\n        inputAttrs: \"inputAttrs\",\n        tabIndex: [2, \"tabIndex\", \"tabIndex\", numberAttribute],\n        tabFocusOnClearButton: [1, \"tabFocusOnClearButton\"],\n        readonly: [2, \"readonly\", \"readonly\", booleanAttribute],\n        searchWhileComposing: [2, \"searchWhileComposing\", \"searchWhileComposing\", booleanAttribute],\n        minTermLength: [2, \"minTermLength\", \"minTermLength\", numberAttribute],\n        editableSearchTerm: [2, \"editableSearchTerm\", \"editableSearchTerm\", booleanAttribute],\n        ngClass: \"ngClass\",\n        typeahead: \"typeahead\",\n        multiple: [2, \"multiple\", \"multiple\", booleanAttribute],\n        addTag: \"addTag\",\n        searchable: [2, \"searchable\", \"searchable\", booleanAttribute],\n        clearable: [2, \"clearable\", \"clearable\", booleanAttribute],\n        isOpen: \"isOpen\",\n        items: \"items\",\n        compareWith: \"compareWith\",\n        clearSearchOnAdd: \"clearSearchOnAdd\",\n        deselectOnClick: \"deselectOnClick\",\n        keyDownFn: \"keyDownFn\"\n      },\n      outputs: {\n        blurEvent: \"blur\",\n        focusEvent: \"focus\",\n        changeEvent: \"change\",\n        openEvent: \"open\",\n        closeEvent: \"close\",\n        searchEvent: \"search\",\n        clearEvent: \"clear\",\n        addEvent: \"add\",\n        removeEvent: \"remove\",\n        scroll: \"scroll\",\n        scrollToEnd: \"scrollToEnd\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NgSelectComponent),\n        multi: true\n      }, NgDropdownPanelService]), i0.ɵɵNgOnChangesFeature],\n      decls: 15,\n      vars: 20,\n      consts: [[\"searchInput\", \"\"], [\"defaultPlaceholderTemplate\", \"\"], [\"defaultLabelTemplate\", \"\"], [\"defaultLoadingSpinnerTemplate\", \"\"], [\"clearButton\", \"\"], [\"defaultOptionTemplate\", \"\"], [\"defaultTagTemplate\", \"\"], [\"defaultNotFoundTemplate\", \"\"], [\"defaultTypeToSearchTemplate\", \"\"], [\"defaultLoadingTextTemplate\", \"\"], [1, \"ng-select-container\", 3, \"mousedown\"], [1, \"ng-value-container\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"ng-input\"], [\"aria-autocomplete\", \"list\", \"role\", \"combobox\", 3, \"blur\", \"change\", \"compositionend\", \"compositionstart\", \"focus\", \"input\", \"disabled\", \"readOnly\", \"value\"], [1, \"ng-arrow-wrapper\"], [1, \"ng-arrow\"], [1, \"ng-dropdown-panel\", 3, \"virtualScroll\", \"bufferAmount\", \"appendTo\", \"position\", \"headerTemplate\", \"footerTemplate\", \"filterValue\", \"items\", \"markedItem\", \"ng-select-multiple\", \"ngClass\", \"id\", \"ariaLabelDropdown\"], [\"aria-atomic\", \"true\", \"aria-live\", \"polite\", \"role\", \"status\", 1, \"ng-visually-hidden\"], [3, \"ngTemplateOutlet\"], [1, \"ng-placeholder\"], [1, \"ng-value\", 3, \"ng-value-disabled\"], [1, \"ng-value\"], [\"aria-hidden\", \"true\", 1, \"ng-value-icon\", \"left\", 3, \"click\"], [1, \"ng-value-label\", 3, \"ngItemLabel\", \"escape\"], [1, \"ng-spinner-loader\"], [\"role\", \"button\", \"tabindex\", \"0\", 1, \"ng-clear-wrapper\", 3, \"title\"], [\"aria-hidden\", \"true\", 1, \"ng-clear\"], [1, \"ng-dropdown-panel\", 3, \"update\", \"scroll\", \"scrollToEnd\", \"outsideClick\", \"virtualScroll\", \"bufferAmount\", \"appendTo\", \"position\", \"headerTemplate\", \"footerTemplate\", \"filterValue\", \"items\", \"markedItem\", \"ngClass\", \"id\", \"ariaLabelDropdown\"], [1, \"ng-option\", 3, \"ng-option-disabled\", \"ng-option-selected\", \"ng-optgroup\", \"ng-option\", \"ng-option-child\", \"ng-option-marked\"], [\"role\", \"option\", 1, \"ng-option\", 3, \"ng-option-marked\"], [1, \"ng-option\", 3, \"click\", \"mouseover\"], [1, \"ng-option-label\", 3, \"ngItemLabel\", \"escape\"], [\"role\", \"option\", 1, \"ng-option\", 3, \"mouseover\", \"click\"], [1, \"ng-tag-label\"], [1, \"ng-option\", \"ng-option-disabled\"]],\n      template: function NgSelectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 10);\n          i0.ɵɵlistener(\"mousedown\", function NgSelectComponent_Template_div_mousedown_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleMousedown($event));\n          });\n          i0.ɵɵelementStart(1, \"div\", 11);\n          i0.ɵɵtemplate(2, NgSelectComponent_Conditional_2_Template, 3, 1)(3, NgSelectComponent_Conditional_3_Template, 2, 0)(4, NgSelectComponent_Conditional_4_Template, 1, 5, null, 12);\n          i0.ɵɵelementStart(5, \"div\", 13)(6, \"input\", 14, 0);\n          i0.ɵɵlistener(\"blur\", function NgSelectComponent_Template_input_blur_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputBlur($event));\n          })(\"change\", function NgSelectComponent_Template_input_change_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView($event.stopPropagation());\n          })(\"compositionend\", function NgSelectComponent_Template_input_compositionend_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const searchInput_r7 = i0.ɵɵreference(7);\n            return i0.ɵɵresetView(ctx.onCompositionEnd(searchInput_r7.value));\n          })(\"compositionstart\", function NgSelectComponent_Template_input_compositionstart_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCompositionStart());\n          })(\"focus\", function NgSelectComponent_Template_input_focus_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputFocus($event));\n          })(\"input\", function NgSelectComponent_Template_input_input_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const searchInput_r7 = i0.ɵɵreference(7);\n            return i0.ɵɵresetView(ctx.filter(searchInput_r7.value));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, NgSelectComponent_Conditional_8_Template, 3, 1)(9, NgSelectComponent_Conditional_9_Template, 2, 1);\n          i0.ɵɵelementStart(10, \"span\", 15);\n          i0.ɵɵelement(11, \"span\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, NgSelectComponent_Conditional_12_Template, 8, 18, \"ng-dropdown-panel\", 17);\n          i0.ɵɵelementStart(13, \"div\", 18);\n          i0.ɵɵtemplate(14, NgSelectComponent_Conditional_14_Template, 1, 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          let tmp_8_0;\n          i0.ɵɵclassProp(\"ng-appearance-outline\", ctx.appearance === \"outline\")(\"ng-has-value\", ctx.hasValue);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.selectedItems.length === 0 && !ctx.searchTerm || ctx.fixedPlaceholder ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional((!ctx.multiLabelTemplate || !ctx.multiple) && ctx.selectedItems.length > 0 ? 3 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.multiple && ctx.multiLabelTemplate && ctx.selectedValues.length > 0 ? 4 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled)(\"readOnly\", !ctx.searchable || ctx.itemsList.maxItemsSelected)(\"value\", (tmp_8_0 = ctx.searchTerm) !== null && tmp_8_0 !== undefined ? tmp_8_0 : \"\");\n          i0.ɵɵattribute(\"aria-activedescendant\", ctx.isOpen ? ctx.itemsList == null ? null : ctx.itemsList.markedItem == null ? null : ctx.itemsList.markedItem.htmlId : null)(\"aria-controls\", ctx.isOpen ? ctx.dropdownId : null)(\"aria-expanded\", ctx.isOpen)(\"aria-label\", ctx.ariaLabel)(\"id\", ctx.labelForId)(\"tabindex\", ctx.tabIndex);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.loading ? 8 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.showClear() ? 9 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(ctx.isOpen ? 12 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.isOpen && ctx.showNoItemsFound() ? 14 : -1);\n        }\n      },\n      dependencies: [NgTemplateOutlet, NgItemLabelDirective, NgDropdownPanelComponent, NgClass],\n      styles: [\"@charset \\\"UTF-8\\\";.ng-select{position:relative;display:block;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.ng-select div,.ng-select input,.ng-select span{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.ng-select [hidden]{display:none}.ng-select.ng-select-searchable .ng-select-container .ng-value-container .ng-input{opacity:1}.ng-select.ng-select-opened .ng-select-container{z-index:1001}.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-placeholder,.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-value{-webkit-user-select:none;user-select:none;cursor:default}.ng-select.ng-select-disabled .ng-arrow-wrapper{cursor:default}.ng-select.ng-select-filtered .ng-placeholder{display:none}.ng-select .ng-select-container{cursor:default;display:flex;outline:none;overflow:hidden;position:relative;width:100%}.ng-select .ng-select-container .ng-value-container{display:flex;flex:1}.ng-select .ng-select-container .ng-value-container .ng-input{opacity:0}.ng-select .ng-select-container .ng-value-container .ng-input>input{box-sizing:content-box;background:none transparent;border:0 none;box-shadow:none;outline:none;padding:0;cursor:default;width:100%}.ng-select .ng-select-container .ng-value-container .ng-input>input::-ms-clear{display:none}.ng-select .ng-select-container .ng-value-container .ng-input>input[readonly]{-webkit-user-select:unset;user-select:unset;width:0;padding:0}.ng-select.ng-select-single.ng-select-filtered .ng-select-container .ng-value-container .ng-value{visibility:hidden}.ng-select.ng-select-single .ng-select-container .ng-value-container,.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input{position:absolute;left:0;width:100%}.ng-select.ng-select-multiple.ng-select-disabled>.ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container{flex-wrap:wrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{position:absolute}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{white-space:nowrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon{cursor:pointer}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input{flex:1;z-index:2}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{z-index:1}.ng-select .ng-clear-wrapper{cursor:pointer;position:relative;width:17px;-webkit-user-select:none;user-select:none}.ng-select .ng-clear-wrapper .ng-clear{display:inline-block;font-size:18px;line-height:1;pointer-events:none}.ng-select .ng-spinner-loader{border-radius:50%;width:17px;height:17px;margin-right:5px;font-size:10px;position:relative;text-indent:-9999em;border-top:2px solid rgba(66,66,66,.2);border-right:2px solid rgba(66,66,66,.2);border-bottom:2px solid rgba(66,66,66,.2);border-left:2px solid #424242;transform:translateZ(0);animation:load8 .8s infinite linear}.ng-select .ng-spinner-loader:after{border-radius:50%;width:17px;height:17px}@-webkit-keyframes load8{0%{-webkit-transform:rotate(0deg);transform:rotate(0)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes load8{0%{-webkit-transform:rotate(0deg);transform:rotate(0)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}.ng-select .ng-arrow-wrapper{cursor:pointer;position:relative;text-align:center;-webkit-user-select:none;user-select:none}.ng-select .ng-arrow-wrapper .ng-arrow{pointer-events:none;display:inline-block;height:0;width:0;position:relative}.ng-dropdown-panel{box-sizing:border-box;position:absolute;opacity:0;width:100%;z-index:1050;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .ng-dropdown-panel-items{display:block;height:auto;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;max-height:240px;overflow-y:auto}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option{box-sizing:border-box;cursor:pointer;display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .ng-option-label:empty:before{content:\\\"\\\\200b\\\"}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .highlighted{font-weight:700;text-decoration:underline}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.disabled{cursor:default}.ng-dropdown-panel .scroll-host{overflow:hidden;overflow-y:auto;position:relative;display:block;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .scrollable-content{top:0;left:0;width:100%;height:100%;position:absolute}.ng-dropdown-panel .total-padding{width:1px;opacity:0}.ng-visually-hidden{position:absolute!important;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0 0 0 0);border:0;white-space:nowrap}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgSelectComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ng-select',\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NgSelectComponent),\n        multi: true\n      }, NgDropdownPanelService],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [NgTemplateOutlet, NgItemLabelDirective, NgDropdownPanelComponent, NgClass],\n      template: \"<div\\n\\t(mousedown)=\\\"handleMousedown($event)\\\"\\n\\t[class.ng-appearance-outline]=\\\"appearance === 'outline'\\\"\\n\\t[class.ng-has-value]=\\\"hasValue\\\"\\n\\tclass=\\\"ng-select-container\\\">\\n\\t<div class=\\\"ng-value-container\\\">\\n\\t\\t@if ((selectedItems.length === 0 && !searchTerm) || fixedPlaceholder) {\\n\\t\\t\\t<ng-template #defaultPlaceholderTemplate>\\n\\t\\t\\t\\t<div class=\\\"ng-placeholder\\\">{{ placeholder }}</div>\\n\\t\\t\\t</ng-template>\\n\\t\\t\\t<ng-template [ngTemplateOutlet]=\\\"placeholderTemplate || defaultPlaceholderTemplate\\\"> </ng-template>\\n\\t\\t}\\n\\n\\t\\t@if ((!multiLabelTemplate || !multiple) && selectedItems.length > 0) {\\n\\t\\t\\t@for (item of selectedItems; track trackByOption($index, item)) {\\n\\t\\t\\t\\t<div [class.ng-value-disabled]=\\\"item.disabled\\\" class=\\\"ng-value\\\">\\n\\t\\t\\t\\t\\t<ng-template #defaultLabelTemplate>\\n\\t\\t\\t\\t\\t\\t<span class=\\\"ng-value-icon left\\\" (click)=\\\"unselect(item)\\\" aria-hidden=\\\"true\\\">\\u00D7</span>\\n\\t\\t\\t\\t\\t\\t<span class=\\\"ng-value-label\\\" [ngItemLabel]=\\\"item.label\\\" [escape]=\\\"escapeHTML\\\"></span>\\n\\t\\t\\t\\t\\t</ng-template>\\n\\t\\t\\t\\t\\t<ng-template\\n\\t\\t\\t\\t\\t\\t[ngTemplateOutlet]=\\\"labelTemplate || defaultLabelTemplate\\\"\\n\\t\\t\\t\\t\\t\\t[ngTemplateOutletContext]=\\\"{ item: item.value, clear: clearItem, label: item.label }\\\">\\n\\t\\t\\t\\t\\t</ng-template>\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t}\\n\\t\\t}\\n\\n\\t\\t@if (multiple && multiLabelTemplate && selectedValues.length > 0) {\\n\\t\\t\\t<ng-template [ngTemplateOutlet]=\\\"multiLabelTemplate\\\" [ngTemplateOutletContext]=\\\"{ items: selectedValues, clear: clearItem }\\\">\\n\\t\\t\\t</ng-template>\\n\\t\\t}\\n\\n\\t\\t<div class=\\\"ng-input\\\">\\n\\t\\t\\t<input\\n\\t\\t\\t\\t#searchInput\\n\\t\\t\\t\\t(blur)=\\\"onInputBlur($event)\\\"\\n\\t\\t\\t\\t(change)=\\\"$event.stopPropagation()\\\"\\n\\t\\t\\t\\t(compositionend)=\\\"onCompositionEnd(searchInput.value)\\\"\\n\\t\\t\\t\\t(compositionstart)=\\\"onCompositionStart()\\\"\\n\\t\\t\\t\\t(focus)=\\\"onInputFocus($event)\\\"\\n\\t\\t\\t\\t(input)=\\\"filter(searchInput.value)\\\"\\n\\t\\t\\t\\t[attr.aria-activedescendant]=\\\"isOpen ? itemsList?.markedItem?.htmlId : null\\\"\\n\\t\\t\\t\\t[attr.aria-controls]=\\\"isOpen ? dropdownId : null\\\"\\n\\t\\t\\t\\t[attr.aria-expanded]=\\\"isOpen\\\"\\n\\t\\t\\t\\t[attr.aria-label]=\\\"ariaLabel\\\"\\n\\t\\t\\t\\t[attr.id]=\\\"labelForId\\\"\\n\\t\\t\\t\\t[attr.tabindex]=\\\"tabIndex\\\"\\n\\t\\t\\t\\t[disabled]=\\\"disabled\\\"\\n\\t\\t\\t\\t[readOnly]=\\\"!searchable || itemsList.maxItemsSelected\\\"\\n\\t\\t\\t\\t[value]=\\\"searchTerm ?? ''\\\"\\n\\t\\t\\t\\taria-autocomplete=\\\"list\\\"\\n\\t\\t\\t\\trole=\\\"combobox\\\" />\\n\\t\\t</div>\\n\\t</div>\\n\\n\\t@if (loading) {\\n\\t\\t<ng-template #defaultLoadingSpinnerTemplate>\\n\\t\\t\\t<div class=\\\"ng-spinner-loader\\\"></div>\\n\\t\\t</ng-template>\\n\\t\\t<ng-template [ngTemplateOutlet]=\\\"loadingSpinnerTemplate || defaultLoadingSpinnerTemplate\\\"></ng-template>\\n\\t}\\n\\n\\t@if (showClear()) {\\n\\t\\t@if (clearButtonTemplate) {\\n\\t\\t\\t<ng-container [ngTemplateOutlet]=\\\"clearButtonTemplate\\\"></ng-container>\\n\\t\\t} @else {\\n\\t\\t\\t<span\\n\\t\\t\\t\\tclass=\\\"ng-clear-wrapper\\\"\\n\\t\\t\\t\\trole=\\\"button\\\"\\n\\t\\t\\t\\ttabindex=\\\"0\\\"\\n\\t\\t\\t\\t[attr.tabindex]=\\\"tabFocusOnClearButton() ? 0 : -1\\\"\\n\\t\\t\\t\\ttitle=\\\"{{ clearAllText }}\\\"\\n\\t\\t\\t\\t#clearButton>\\n\\t\\t\\t\\t<span class=\\\"ng-clear\\\" aria-hidden=\\\"true\\\">\\u00D7</span>\\n\\t\\t\\t</span>\\n\\t\\t}\\n\\t}\\n\\n\\t<span class=\\\"ng-arrow-wrapper\\\">\\n\\t\\t<span class=\\\"ng-arrow\\\"></span>\\n\\t</span>\\n</div>\\n\\n@if (isOpen) {\\n\\t<ng-dropdown-panel\\n\\t\\tclass=\\\"ng-dropdown-panel\\\"\\n\\t\\t[virtualScroll]=\\\"virtualScroll\\\"\\n\\t\\t[bufferAmount]=\\\"bufferAmount\\\"\\n\\t\\t[appendTo]=\\\"appendTo\\\"\\n\\t\\t[position]=\\\"dropdownPosition\\\"\\n\\t\\t[headerTemplate]=\\\"headerTemplate\\\"\\n\\t\\t[footerTemplate]=\\\"footerTemplate\\\"\\n\\t\\t[filterValue]=\\\"searchTerm\\\"\\n\\t\\t[items]=\\\"itemsList.filteredItems\\\"\\n\\t\\t[markedItem]=\\\"itemsList.markedItem\\\"\\n\\t\\t(update)=\\\"viewPortItems = $event\\\"\\n\\t\\t(scroll)=\\\"scroll.emit($event)\\\"\\n\\t\\t(scrollToEnd)=\\\"scrollToEnd.emit($event)\\\"\\n\\t\\t(outsideClick)=\\\"close()\\\"\\n\\t\\t[class.ng-select-multiple]=\\\"multiple\\\"\\n\\t\\t[ngClass]=\\\"appendTo ? (ngClass ? ngClass : classes) : null\\\"\\n\\t\\t[id]=\\\"dropdownId\\\"\\n\\t\\t[ariaLabelDropdown]=\\\"ariaLabelDropdown\\\">\\n\\t\\t<ng-container>\\n\\t\\t\\t@for (item of viewPortItems; track trackByOption($index, item)) {\\n\\t\\t\\t\\t<div\\n\\t\\t\\t\\t\\tclass=\\\"ng-option\\\"\\n\\t\\t\\t\\t\\t[attr.role]=\\\"item.children ? 'group' : 'option'\\\"\\n\\t\\t\\t\\t\\t(click)=\\\"toggleItem(item)\\\"\\n\\t\\t\\t\\t\\t(mouseover)=\\\"onItemHover(item)\\\"\\n\\t\\t\\t\\t\\t[class.ng-option-disabled]=\\\"item.disabled\\\"\\n\\t\\t\\t\\t\\t[class.ng-option-selected]=\\\"item.selected\\\"\\n\\t\\t\\t\\t\\t[class.ng-optgroup]=\\\"item.children\\\"\\n\\t\\t\\t\\t\\t[class.ng-option]=\\\"!item.children\\\"\\n\\t\\t\\t\\t\\t[class.ng-option-child]=\\\"!!item.parent\\\"\\n\\t\\t\\t\\t\\t[class.ng-option-marked]=\\\"item === itemsList.markedItem\\\"\\n\\t\\t\\t\\t\\t[attr.aria-selected]=\\\"item.selected\\\"\\n\\t\\t\\t\\t\\t[attr.id]=\\\"item?.htmlId\\\"\\n\\t\\t\\t\\t\\t[attr.aria-setsize]=\\\"itemsList.filteredItems.length\\\"\\n\\t\\t\\t\\t\\t[attr.aria-posinset]=\\\"item.index + 1\\\">\\n\\t\\t\\t\\t\\t<ng-template #defaultOptionTemplate>\\n\\t\\t\\t\\t\\t\\t<span class=\\\"ng-option-label\\\" [ngItemLabel]=\\\"item.label\\\" [escape]=\\\"escapeHTML\\\"></span>\\n\\t\\t\\t\\t\\t</ng-template>\\n\\t\\t\\t\\t\\t<ng-template\\n\\t\\t\\t\\t\\t\\t[ngTemplateOutlet]=\\\"\\n\\t\\t\\t\\t\\t\\t\\titem.children ? optgroupTemplate || defaultOptionTemplate : optionTemplate || defaultOptionTemplate\\n\\t\\t\\t\\t\\t\\t\\\"\\n\\t\\t\\t\\t\\t\\t[ngTemplateOutletContext]=\\\"{ item: item.value, item$: item, index: item.index, searchTerm: searchTerm }\\\">\\n\\t\\t\\t\\t\\t</ng-template>\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t}\\n\\t\\t\\t@if (showAddTag) {\\n\\t\\t\\t\\t<div\\n\\t\\t\\t\\t\\tclass=\\\"ng-option\\\"\\n\\t\\t\\t\\t\\t[class.ng-option-marked]=\\\"!itemsList.markedItem\\\"\\n\\t\\t\\t\\t\\t(mouseover)=\\\"itemsList.unmarkItem()\\\"\\n\\t\\t\\t\\t\\trole=\\\"option\\\"\\n\\t\\t\\t\\t\\t(click)=\\\"selectTag()\\\">\\n\\t\\t\\t\\t\\t<ng-template #defaultTagTemplate>\\n\\t\\t\\t\\t\\t\\t<span\\n\\t\\t\\t\\t\\t\\t\\t><span class=\\\"ng-tag-label\\\">{{ addTagText }}</span\\n\\t\\t\\t\\t\\t\\t\\t>\\\"{{ searchTerm }}\\\"</span\\n\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t</ng-template>\\n\\t\\t\\t\\t\\t<ng-template\\n\\t\\t\\t\\t\\t\\t[ngTemplateOutlet]=\\\"tagTemplate || defaultTagTemplate\\\"\\n\\t\\t\\t\\t\\t\\t[ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n\\t\\t\\t\\t\\t</ng-template>\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t}\\n\\t\\t</ng-container>\\n\\t\\t@if (showNoItemsFound()) {\\n\\t\\t\\t<ng-template #defaultNotFoundTemplate>\\n\\t\\t\\t\\t<div class=\\\"ng-option ng-option-disabled\\\">{{ notFoundText }}</div>\\n\\t\\t\\t</ng-template>\\n\\t\\t\\t<ng-template\\n\\t\\t\\t\\t[ngTemplateOutlet]=\\\"notFoundTemplate || defaultNotFoundTemplate\\\"\\n\\t\\t\\t\\t[ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n\\t\\t\\t</ng-template>\\n\\t\\t}\\n\\t\\t@if (showTypeToSearch()) {\\n\\t\\t\\t<ng-template #defaultTypeToSearchTemplate>\\n\\t\\t\\t\\t<div class=\\\"ng-option ng-option-disabled\\\">{{ typeToSearchText }}</div>\\n\\t\\t\\t</ng-template>\\n\\t\\t\\t<ng-template [ngTemplateOutlet]=\\\"typeToSearchTemplate || defaultTypeToSearchTemplate\\\"></ng-template>\\n\\t\\t}\\n\\t\\t@if (loading && itemsList.filteredItems.length === 0) {\\n\\t\\t\\t<ng-template #defaultLoadingTextTemplate>\\n\\t\\t\\t\\t<div class=\\\"ng-option ng-option-disabled\\\">{{ loadingText }}</div>\\n\\t\\t\\t</ng-template>\\n\\t\\t\\t<ng-template\\n\\t\\t\\t\\t[ngTemplateOutlet]=\\\"loadingTextTemplate || defaultLoadingTextTemplate\\\"\\n\\t\\t\\t\\t[ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n\\t\\t\\t</ng-template>\\n\\t\\t}\\n\\t</ng-dropdown-panel>\\n}\\n\\n<!-- Always present aria-live region -->\\n<div aria-atomic=\\\"true\\\" aria-live=\\\"polite\\\" class=\\\"ng-visually-hidden\\\" role=\\\"status\\\">\\n\\t@if (isOpen && showNoItemsFound()) {\\n\\t\\t{{ notFoundText }}\\n\\t}\\n</div>\\n\",\n      styles: [\"@charset \\\"UTF-8\\\";.ng-select{position:relative;display:block;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.ng-select div,.ng-select input,.ng-select span{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.ng-select [hidden]{display:none}.ng-select.ng-select-searchable .ng-select-container .ng-value-container .ng-input{opacity:1}.ng-select.ng-select-opened .ng-select-container{z-index:1001}.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-placeholder,.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-value{-webkit-user-select:none;user-select:none;cursor:default}.ng-select.ng-select-disabled .ng-arrow-wrapper{cursor:default}.ng-select.ng-select-filtered .ng-placeholder{display:none}.ng-select .ng-select-container{cursor:default;display:flex;outline:none;overflow:hidden;position:relative;width:100%}.ng-select .ng-select-container .ng-value-container{display:flex;flex:1}.ng-select .ng-select-container .ng-value-container .ng-input{opacity:0}.ng-select .ng-select-container .ng-value-container .ng-input>input{box-sizing:content-box;background:none transparent;border:0 none;box-shadow:none;outline:none;padding:0;cursor:default;width:100%}.ng-select .ng-select-container .ng-value-container .ng-input>input::-ms-clear{display:none}.ng-select .ng-select-container .ng-value-container .ng-input>input[readonly]{-webkit-user-select:unset;user-select:unset;width:0;padding:0}.ng-select.ng-select-single.ng-select-filtered .ng-select-container .ng-value-container .ng-value{visibility:hidden}.ng-select.ng-select-single .ng-select-container .ng-value-container,.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input{position:absolute;left:0;width:100%}.ng-select.ng-select-multiple.ng-select-disabled>.ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container{flex-wrap:wrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{position:absolute}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{white-space:nowrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon{cursor:pointer}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input{flex:1;z-index:2}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{z-index:1}.ng-select .ng-clear-wrapper{cursor:pointer;position:relative;width:17px;-webkit-user-select:none;user-select:none}.ng-select .ng-clear-wrapper .ng-clear{display:inline-block;font-size:18px;line-height:1;pointer-events:none}.ng-select .ng-spinner-loader{border-radius:50%;width:17px;height:17px;margin-right:5px;font-size:10px;position:relative;text-indent:-9999em;border-top:2px solid rgba(66,66,66,.2);border-right:2px solid rgba(66,66,66,.2);border-bottom:2px solid rgba(66,66,66,.2);border-left:2px solid #424242;transform:translateZ(0);animation:load8 .8s infinite linear}.ng-select .ng-spinner-loader:after{border-radius:50%;width:17px;height:17px}@-webkit-keyframes load8{0%{-webkit-transform:rotate(0deg);transform:rotate(0)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes load8{0%{-webkit-transform:rotate(0deg);transform:rotate(0)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}.ng-select .ng-arrow-wrapper{cursor:pointer;position:relative;text-align:center;-webkit-user-select:none;user-select:none}.ng-select .ng-arrow-wrapper .ng-arrow{pointer-events:none;display:inline-block;height:0;width:0;position:relative}.ng-dropdown-panel{box-sizing:border-box;position:absolute;opacity:0;width:100%;z-index:1050;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .ng-dropdown-panel-items{display:block;height:auto;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;max-height:240px;overflow-y:auto}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option{box-sizing:border-box;cursor:pointer;display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .ng-option-label:empty:before{content:\\\"\\\\200b\\\"}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .highlighted{font-weight:700;text-decoration:underline}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.disabled{cursor:default}.ng-dropdown-panel .scroll-host{overflow:hidden;overflow-y:auto;position:relative;display:block;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .scrollable-content{top:0;left:0;width:100%;height:100%;position:absolute}.ng-dropdown-panel .total-padding{width:1px;opacity:0}.ng-visually-hidden{position:absolute!important;width:1px;height:1px;margin:-1px;padding:0;overflow:hidden;clip:rect(0 0 0 0);border:0;white-space:nowrap}\\n\"]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['class']\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['autofocus']\n    }]\n  }, {\n    type: NgSelectConfig\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [SELECTION_MODEL_FACTORY]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: ConsoleService\n  }], {\n    ariaLabelDropdown: [{\n      type: Input\n    }],\n    bindLabel: [{\n      type: Input\n    }],\n    bindValue: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    markFirst: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    fixedPlaceholder: [{\n      type: Input\n    }],\n    notFoundText: [{\n      type: Input\n    }],\n    typeToSearchText: [{\n      type: Input\n    }],\n    preventToggleOnRightClick: [{\n      type: Input\n    }],\n    addTagText: [{\n      type: Input\n    }],\n    loadingText: [{\n      type: Input\n    }],\n    clearAllText: [{\n      type: Input\n    }],\n    appearance: [{\n      type: Input\n    }],\n    dropdownPosition: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    loading: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    closeOnSelect: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    hideSelected: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectOnTab: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    openOnEnter: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    maxSelectedItems: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    groupBy: [{\n      type: Input\n    }],\n    groupValue: [{\n      type: Input\n    }],\n    bufferAmount: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    virtualScroll: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectableGroup: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectableGroupAsModel: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    searchFn: [{\n      type: Input\n    }],\n    trackByFn: [{\n      type: Input\n    }],\n    clearOnBackspace: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    labelForId: [{\n      type: Input\n    }],\n    inputAttrs: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    readonly: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    searchWhileComposing: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    minTermLength: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    editableSearchTerm: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    ngClass: [{\n      type: Input\n    }],\n    typeahead: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-typeahead']\n    }],\n    multiple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-multiple']\n    }],\n    addTag: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-taggable']\n    }],\n    searchable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-searchable']\n    }],\n    clearable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-clearable']\n    }],\n    isOpen: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-opened']\n    }],\n    blurEvent: [{\n      type: Output,\n      args: ['blur']\n    }],\n    focusEvent: [{\n      type: Output,\n      args: ['focus']\n    }],\n    changeEvent: [{\n      type: Output,\n      args: ['change']\n    }],\n    openEvent: [{\n      type: Output,\n      args: ['open']\n    }],\n    closeEvent: [{\n      type: Output,\n      args: ['close']\n    }],\n    searchEvent: [{\n      type: Output,\n      args: ['search']\n    }],\n    clearEvent: [{\n      type: Output,\n      args: ['clear']\n    }],\n    addEvent: [{\n      type: Output,\n      args: ['add']\n    }],\n    removeEvent: [{\n      type: Output,\n      args: ['remove']\n    }],\n    scroll: [{\n      type: Output,\n      args: ['scroll']\n    }],\n    scrollToEnd: [{\n      type: Output,\n      args: ['scrollToEnd']\n    }],\n    optionTemplate: [{\n      type: ContentChild,\n      args: [NgOptionTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    optgroupTemplate: [{\n      type: ContentChild,\n      args: [NgOptgroupTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    labelTemplate: [{\n      type: ContentChild,\n      args: [NgLabelTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    multiLabelTemplate: [{\n      type: ContentChild,\n      args: [NgMultiLabelTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    headerTemplate: [{\n      type: ContentChild,\n      args: [NgHeaderTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    footerTemplate: [{\n      type: ContentChild,\n      args: [NgFooterTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    notFoundTemplate: [{\n      type: ContentChild,\n      args: [NgNotFoundTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    placeholderTemplate: [{\n      type: ContentChild,\n      args: [NgPlaceholderTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    typeToSearchTemplate: [{\n      type: ContentChild,\n      args: [NgTypeToSearchTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    loadingTextTemplate: [{\n      type: ContentChild,\n      args: [NgLoadingTextTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    tagTemplate: [{\n      type: ContentChild,\n      args: [NgTagTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    loadingSpinnerTemplate: [{\n      type: ContentChild,\n      args: [NgLoadingSpinnerTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    clearButtonTemplate: [{\n      type: ContentChild,\n      args: [NgClearButtonTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    dropdownPanel: [{\n      type: ViewChild,\n      args: [forwardRef(() => NgDropdownPanelComponent)]\n    }],\n    searchInput: [{\n      type: ViewChild,\n      args: ['searchInput', {\n        static: true\n      }]\n    }],\n    clearButton: [{\n      type: ViewChild,\n      args: ['clearButton']\n    }],\n    ngOptions: [{\n      type: ContentChildren,\n      args: [NgOptionComponent, {\n        descendants: true\n      }]\n    }],\n    useDefaultClass: [{\n      type: HostBinding,\n      args: ['class.ng-select']\n    }],\n    filtered: [{\n      type: HostBinding,\n      args: ['class.ng-select-filtered']\n    }],\n    single: [{\n      type: HostBinding,\n      args: ['class.ng-select-single']\n    }],\n    items: [{\n      type: Input\n    }],\n    disabled: [{\n      type: HostBinding,\n      args: ['class.ng-select-disabled']\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    clearSearchOnAdd: [{\n      type: Input\n    }],\n    deselectOnClick: [{\n      type: Input\n    }],\n    keyDownFn: [{\n      type: Input\n    }],\n    handleKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\nclass NgSelectModule {\n  static {\n    this.ɵfac = function NgSelectModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgSelectModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NgSelectModule,\n      imports: [NgDropdownPanelComponent, NgOptionComponent, NgSelectComponent, NgOptgroupTemplateDirective, NgOptionTemplateDirective, NgLabelTemplateDirective, NgMultiLabelTemplateDirective, NgHeaderTemplateDirective, NgFooterTemplateDirective, NgPlaceholderTemplateDirective, NgClearButtonTemplateDirective, NgNotFoundTemplateDirective, NgTypeToSearchTemplateDirective, NgLoadingTextTemplateDirective, NgTagTemplateDirective, NgLoadingSpinnerTemplateDirective, NgItemLabelDirective],\n      exports: [NgSelectComponent, NgOptionComponent, NgOptgroupTemplateDirective, NgOptionTemplateDirective, NgLabelTemplateDirective, NgMultiLabelTemplateDirective, NgHeaderTemplateDirective, NgFooterTemplateDirective, NgPlaceholderTemplateDirective, NgNotFoundTemplateDirective, NgTypeToSearchTemplateDirective, NgLoadingTextTemplateDirective, NgTagTemplateDirective, NgLoadingSpinnerTemplateDirective, NgClearButtonTemplateDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: SELECTION_MODEL_FACTORY,\n        useValue: DefaultSelectionModelFactory\n      }]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgSelectModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NgDropdownPanelComponent, NgOptionComponent, NgSelectComponent, NgOptgroupTemplateDirective, NgOptionTemplateDirective, NgLabelTemplateDirective, NgMultiLabelTemplateDirective, NgHeaderTemplateDirective, NgFooterTemplateDirective, NgPlaceholderTemplateDirective, NgClearButtonTemplateDirective, NgNotFoundTemplateDirective, NgTypeToSearchTemplateDirective, NgLoadingTextTemplateDirective, NgTagTemplateDirective, NgLoadingSpinnerTemplateDirective, NgItemLabelDirective],\n      exports: [NgSelectComponent, NgOptionComponent, NgOptgroupTemplateDirective, NgOptionTemplateDirective, NgLabelTemplateDirective, NgMultiLabelTemplateDirective, NgHeaderTemplateDirective, NgFooterTemplateDirective, NgPlaceholderTemplateDirective, NgNotFoundTemplateDirective, NgTypeToSearchTemplateDirective, NgLoadingTextTemplateDirective, NgTagTemplateDirective, NgLoadingSpinnerTemplateDirective, NgClearButtonTemplateDirective],\n      providers: [{\n        provide: SELECTION_MODEL_FACTORY,\n        useValue: DefaultSelectionModelFactory\n      }]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of ng-select\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ConsoleService, DefaultSelectionModel, DefaultSelectionModelFactory, NgClearButtonTemplateDirective, NgDropdownPanelComponent, NgDropdownPanelService, NgFooterTemplateDirective, NgHeaderTemplateDirective, NgItemLabelDirective, NgLabelTemplateDirective, NgLoadingSpinnerTemplateDirective, NgLoadingTextTemplateDirective, NgMultiLabelTemplateDirective, NgNotFoundTemplateDirective, NgOptgroupTemplateDirective, NgOptionComponent, NgOptionTemplateDirective, NgPlaceholderTemplateDirective, NgSelectComponent, NgSelectConfig, NgSelectModule, NgTagTemplateDirective, NgTypeToSearchTemplateDirective, SELECTION_MODEL_FACTORY };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,SAAO;AAAA,EACjB,YAAY;AACd;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,cAAc,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,WAAW,CAAC;AAAA,EACpI;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,cAAc,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,WAAW,CAAC;AAAA,EACpI;AACF;AACA,IAAM,MAAM,CAAC,aAAa;AAC1B,IAAM,MAAM,CAAC,aAAa;AAC1B,IAAM,MAAM,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC3B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AACT;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,OAAO;AAAA,EACP,OAAO;AACT;AACA,IAAM,MAAM,CAAC,IAAI,IAAI,IAAI,QAAQ;AAAA,EAC/B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,YAAY;AACd;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,WAAW;AAAA,EACzC;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,wDAAwD,GAAG,GAAG,eAAe,EAAE;AAAA,EACtN;AACA,MAAI,KAAK,GAAG;AACV,UAAM,gCAAmC,YAAY,CAAC;AACtD,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,uBAAuB,6BAA6B;AAAA,EAC/F;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,SAAS,SAAS,qFAAqF;AACnH,MAAG,cAAc,GAAG;AACpB,YAAM,UAAa,cAAc,EAAE;AACnC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,SAAS,OAAO,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,OAAO,GAAG,GAAM;AACnB,IAAG,aAAa;AAChB,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,eAAe,QAAQ,KAAK,EAAE,UAAU,OAAO,UAAU;AAAA,EACzE;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAAC;AAChF,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,8DAA8D,GAAG,GAAG,eAAe,EAAE;AAChO,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,0BAA6B,YAAY,CAAC;AAChD,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,qBAAqB,QAAQ,QAAQ;AACpD,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,iBAAiB,uBAAuB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,QAAQ,OAAO,OAAO,WAAW,QAAQ,KAAK,CAAC;AAAA,EAC1L;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,iBAAiB,GAAG,gDAAgD,GAAG,GAAG,OAAO,IAAO,oBAAoB,EAAE,eAAe,IAAI;AAAA,EACtI;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,aAAa;AAAA,EACpC;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,EAAE;AAAA,EAClG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,kBAAkB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,gBAAgB,OAAO,SAAS,CAAC;AAAA,EAC7J;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,EAAE;AAAA,EAC3B;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,wDAAwD,GAAG,GAAG,eAAe,EAAE;AAAA,EACtN;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mCAAsC,YAAY,CAAC;AACzD,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,0BAA0B,gCAAgC;AAAA,EACrG;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,GAAG,EAAE;AAAA,EAC7B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,mBAAmB;AAAA,EAC9D;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,IAAI,CAAC,EAAE,GAAG,QAAQ,EAAE;AACjD,IAAG,OAAO,GAAG,GAAM;AACnB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,sBAAsB,SAAS,OAAO,YAAY;AACrD,IAAG,YAAY,YAAY,OAAO,sBAAsB,IAAI,IAAI,EAAE;AAAA,EACpE;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,gBAAgB,EAAE,EAAE,GAAG,wDAAwD,GAAG,GAAG,QAAQ,EAAE;AAAA,EAChL;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,sBAAsB,IAAI,CAAC;AAAA,EACrD;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,EAAE;AACpC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,eAAe,SAAS,KAAK,EAAE,UAAU,OAAO,UAAU;AAAA,EAC1E;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAAC;AACjF,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,SAAS,SAAS,uEAAuE;AACrG,YAAM,WAAc,cAAc,IAAI,EAAE;AACxC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,WAAW,QAAQ,CAAC;AAAA,IACnD,CAAC,EAAE,aAAa,SAAS,2EAA2E;AAClG,YAAM,WAAc,cAAc,IAAI,EAAE;AACxC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,YAAY,QAAQ,CAAC;AAAA,IACpD,CAAC;AACD,IAAG,WAAW,GAAG,+DAA+D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,+DAA+D,GAAG,GAAG,eAAe,EAAE;AAClO,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,4BAA+B,YAAY,CAAC;AAClD,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,sBAAsB,SAAS,QAAQ,EAAE,sBAAsB,SAAS,QAAQ,EAAE,eAAe,SAAS,QAAQ,EAAE,aAAa,CAAC,SAAS,QAAQ,EAAE,mBAAmB,CAAC,CAAC,SAAS,MAAM,EAAE,oBAAoB,aAAa,OAAO,UAAU,UAAU;AACtQ,IAAG,YAAY,QAAQ,SAAS,WAAW,UAAU,QAAQ,EAAE,iBAAiB,SAAS,QAAQ,EAAE,MAAM,YAAY,OAAO,OAAO,SAAS,MAAM,EAAE,gBAAgB,OAAO,UAAU,cAAc,MAAM,EAAE,iBAAiB,SAAS,QAAQ,CAAC;AAC9O,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,SAAS,WAAW,OAAO,oBAAoB,4BAA4B,OAAO,kBAAkB,yBAAyB,EAAE,2BAA8B,gBAAgB,IAAI,KAAK,SAAS,OAAO,UAAU,SAAS,OAAO,OAAO,UAAU,CAAC;AAAA,EACtR;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,EAAE,GAAG,QAAQ,EAAE;AAC1C,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,UAAU;AACtC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAM,OAAO,YAAY,GAAI;AAAA,EACrD;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AAAC;AACzF,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,aAAa,SAAS,mFAAmF;AACrH,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,WAAW,CAAC;AAAA,IACrD,CAAC,EAAE,SAAS,SAAS,+EAA+E;AAClG,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,CAAC;AAAA,IAC1C,CAAC;AACD,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,uEAAuE,GAAG,GAAG,eAAe,EAAE;AAClP,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,yBAA4B,YAAY,CAAC;AAC/C,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,oBAAoB,CAAC,OAAO,UAAU,UAAU;AAC/D,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,eAAe,sBAAsB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,UAAU,CAAC;AAAA,EAC1J;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,YAAY;AAAA,EAC1C;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AAAC;AACzF,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,uEAAuE,GAAG,GAAG,eAAe,EAAE;AAAA,EACpP;AACA,MAAI,KAAK,GAAG;AACV,UAAM,8BAAiC,YAAY,CAAC;AACpD,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,oBAAoB,2BAA2B,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,UAAU,CAAC;AAAA,EACpK;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,gBAAgB;AAAA,EAC9C;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AAAC;AACzF,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,uEAAuE,GAAG,GAAG,eAAe,EAAE;AAAA,EACpP;AACA,MAAI,KAAK,GAAG;AACV,UAAM,kCAAqC,YAAY,CAAC;AACxD,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,wBAAwB,+BAA+B;AAAA,EAClG;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,WAAW;AAAA,EACzC;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AAAC;AACzF,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,uEAAuE,GAAG,GAAG,eAAe,EAAE;AAAA,EACpP;AACA,MAAI,KAAK,GAAG;AACV,UAAM,iCAAoC,YAAY,CAAC;AACvD,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,uBAAuB,8BAA8B,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,UAAU,CAAC;AAAA,EAC1K;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,qBAAqB,EAAE;AAC5C,IAAG,WAAW,UAAU,SAAS,8EAA8E,QAAQ;AACrH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,MAAM;AAAA,IACrD,CAAC,EAAE,UAAU,SAAS,8EAA8E,QAAQ;AAC1G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,KAAK,MAAM,CAAC;AAAA,IAClD,CAAC,EAAE,eAAe,SAAS,mFAAmF,QAAQ;AACpH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,KAAK,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,gBAAgB,SAAS,sFAAsF;AAChH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,MAAM,CAAC;AAAA,IACtC,CAAC;AACD,IAAG,wBAAwB,CAAC;AAC5B,IAAG,iBAAiB,GAAG,iDAAiD,GAAG,IAAI,OAAO,IAAO,oBAAoB,EAAE,eAAe,IAAI;AACtI,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,OAAO,EAAE;AACzF,IAAG,sBAAsB;AACzB,IAAG,WAAW,GAAG,yDAAyD,GAAG,CAAC,EAAE,GAAG,yDAAyD,GAAG,CAAC,EAAE,GAAG,yDAAyD,GAAG,CAAC;AAClN,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,sBAAsB,OAAO,QAAQ;AACpD,IAAG,WAAW,iBAAiB,OAAO,aAAa,EAAE,gBAAgB,OAAO,YAAY,EAAE,YAAY,OAAO,QAAQ,EAAE,YAAY,OAAO,gBAAgB,EAAE,kBAAkB,OAAO,cAAc,EAAE,kBAAkB,OAAO,cAAc,EAAE,eAAe,OAAO,UAAU,EAAE,SAAS,OAAO,UAAU,aAAa,EAAE,cAAc,OAAO,UAAU,UAAU,EAAE,WAAW,OAAO,WAAW,OAAO,UAAU,OAAO,UAAU,OAAO,UAAU,IAAI,EAAE,MAAM,OAAO,UAAU,EAAE,qBAAqB,OAAO,iBAAiB;AAChgB,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,OAAO,aAAa;AAClC,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,aAAa,IAAI,EAAE;AAC3C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,iBAAiB,IAAI,IAAI,EAAE;AACnD,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,iBAAiB,IAAI,IAAI,EAAE;AACnD,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,WAAW,OAAO,UAAU,cAAc,WAAW,IAAI,IAAI,EAAE;AAAA,EACzF;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,mBAAmB,KAAK,OAAO,cAAc,GAAG;AAAA,EACrD;AACF;AACA,IAAM,mBAAmB;AACzB,IAAM,sBAAsB,OAAO,iBAAiB,MAAM;AAC1D,IAAM,cAAc;AAAA,EAClB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,SAAS,oBAAoB,KAAK,KAAK,IAAI,MAAM,QAAQ,kBAAkB,SAAO,YAAY,GAAG,CAAC,IAAI;AAC/G;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,UAAU,UAAa,UAAU;AAC1C;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU,YAAY,UAAU,KAAK;AACrD;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,iBAAiB;AAC1B;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,iBAAiB;AAC1B;AACA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,SAAS;AACnB,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,QAAQ,cAAc,YAAY,KAAK,SAAS,WAAW,KAAK,WAAW,IAAI,KAAK;AAAA,EAC3F;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAyB,kBAAqB,UAAU,CAAC;AAAA,IAC5F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MACnC,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA8B,kBAAqB,WAAW,CAAC;AAAA,IAClG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACvC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,mBAAmB;AAC1E,aAAO,KAAK,qBAAqB,8BAAgC,kBAAqB,WAAW,CAAC;AAAA,IACpG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IACzC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,kBAAqB,WAAW,CAAC;AAAA,IACjG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,IACtC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sCAAsC,mBAAmB;AAC5E,aAAO,KAAK,qBAAqB,gCAAkC,kBAAqB,WAAW,CAAC;AAAA,IACtG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,IAC5C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA8B,kBAAqB,WAAW,CAAC;AAAA,IAClG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACvC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA8B,kBAAqB,WAAW,CAAC;AAAA,IAClG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACvC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,mBAAmB;AAC1E,aAAO,KAAK,qBAAqB,8BAAgC,kBAAqB,WAAW,CAAC;AAAA,IACpG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IACzC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,iCAAN,MAAM,gCAA+B;AAAA,EACnC,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uCAAuC,mBAAmB;AAC7E,aAAO,KAAK,qBAAqB,iCAAmC,kBAAqB,WAAW,CAAC;AAAA,IACvG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,IAC5C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gCAAgC,CAAC;AAAA,IACvG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,kCAAN,MAAM,iCAAgC;AAAA,EACpC,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wCAAwC,mBAAmB;AAC9E,aAAO,KAAK,qBAAqB,kCAAoC,kBAAqB,WAAW,CAAC;AAAA,IACxG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,uBAAuB,EAAE,CAAC;AAAA,IAC7C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iCAAiC,CAAC;AAAA,IACxG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,iCAAN,MAAM,gCAA+B;AAAA,EACnC,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uCAAuC,mBAAmB;AAC7E,aAAO,KAAK,qBAAqB,iCAAmC,kBAAqB,WAAW,CAAC;AAAA,IACvG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,IAC5C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gCAAgC,CAAC;AAAA,IACvG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAA2B,kBAAqB,WAAW,CAAC;AAAA,IAC/F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,oCAAN,MAAM,mCAAkC;AAAA,EACtC,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0CAA0C,mBAAmB;AAChF,aAAO,KAAK,qBAAqB,oCAAsC,kBAAqB,WAAW,CAAC;AAAA,IAC1G;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,yBAAyB,EAAE,CAAC;AAAA,IAC/C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mCAAmC,CAAC;AAAA,IAC1G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA;AAAA,MAEL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAEH,IAAM,iCAAN,MAAM,gCAA+B;AAAA,EACnC,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uCAAuC,mBAAmB;AAC7E,aAAO,KAAK,qBAAqB,iCAAmC,kBAAqB,WAAW,CAAC;AAAA,IACvG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,IAC5C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gCAAgC,CAAC;AAAA,IACvG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,QAAQ;AAEf,SAAO,eAAe,QAAQ,QAAQ,MAAM;AAE1C,UAAM,MAAM,KAAK,OAAO,IAAI,KAAK;AACjC,WAAO,IAAI,SAAS,EAAE;AAAA,EACxB,CAAC;AACH;AACA,IAAM,aAAa;AAAA,EACjB,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AAAA,EACV,KAAU;AACZ;AACA,SAAS,kBAAkB,MAAM;AAC/B,QAAM,QAAQ,OAAK,WAAW,CAAC,KAAK;AACpC,SAAO,KAAK,QAAQ,qBAAqB,KAAK;AAChD;AACA,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,WAAW,iBAAiB;AACtC,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,SAAS,CAAC;AACf,SAAK,iBAAiB,CAAC;AACvB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,eAAe,KAAK,YAAY;AAAA,EAC9C;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK,UAAU,gBAAgB,KAAK,OAAO,WAAW,KAAK,cAAc;AAAA,EAClF;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,UAAU,YAAY,KAAK,UAAU,oBAAoB,KAAK,cAAc;AAAA,EAC1F;AAAA,EACA,IAAI,mBAAmB;AACrB,QAAI,IAAI,KAAK,cAAc,SAAS;AACpC,WAAO,KAAK,GAAG,KAAK;AAClB,YAAM,OAAO,KAAK,cAAc,CAAC;AACjC,UAAI,CAAC,KAAK,UAAU;AAClB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS,OAAO;AACd,SAAK,SAAS,MAAM,IAAI,CAAC,MAAM,UAAU,KAAK,QAAQ,MAAM,KAAK,CAAC;AAClE,QAAI,KAAK,UAAU,SAAS;AAC1B,WAAK,UAAU,KAAK,SAAS,KAAK,QAAQ,KAAK,UAAU,OAAO;AAChE,WAAK,SAAS,KAAK,SAAS,KAAK,OAAO;AAAA,IAC1C,OAAO;AACL,WAAK,UAAU,oBAAI,IAAI;AACvB,WAAK,QAAQ,IAAI,QAAW,KAAK,MAAM;AAAA,IACzC;AACA,SAAK,iBAAiB,CAAC,GAAG,KAAK,MAAM;AAAA,EACvC;AAAA,EACA,OAAO,MAAM;AACX,QAAI,KAAK,YAAY,KAAK,kBAAkB;AAC1C;AAAA,IACF;AACA,UAAM,WAAW,KAAK,UAAU;AAChC,QAAI,CAAC,UAAU;AACb,WAAK,cAAc;AAAA,IACrB;AACA,SAAK,gBAAgB,OAAO,MAAM,UAAU,KAAK,UAAU,sBAAsB;AACjF,QAAI,KAAK,UAAU,cAAc;AAC/B,WAAK,cAAc,IAAI;AAAA,IACzB;AAAA,EACF;AAAA,EACA,SAAS,MAAM;AACb,QAAI,CAAC,KAAK,UAAU;AAClB;AAAA,IACF;AACA,SAAK,gBAAgB,SAAS,MAAM,KAAK,UAAU,QAAQ;AAC3D,QAAI,KAAK,UAAU,gBAAgB,UAAU,KAAK,KAAK,KAAK,KAAK,UAAU,UAAU;AACnF,WAAK,cAAc,IAAI;AAAA,IACzB;AAAA,EACF;AAAA,EACA,SAAS,OAAO;AACd,QAAI;AACJ,QAAI,KAAK,UAAU,aAAa;AAC9B,eAAS,UAAQ,KAAK,UAAU,YAAY,KAAK,OAAO,KAAK;AAAA,IAC/D,WAAW,KAAK,UAAU,WAAW;AACnC,eAAS,UAAQ,CAAC,KAAK,YAAY,KAAK,cAAc,KAAK,OAAO,KAAK,UAAU,SAAS,MAAM;AAAA,IAClG,OAAO;AACL,eAAS,UAAQ,KAAK,UAAU,SAAS,CAAC,KAAK,YAAY,KAAK,SAAS,KAAK,UAAU,KAAK,cAAc,OAAO,KAAK,UAAU,SAAS;AAAA,IAC5I;AACA,WAAO,KAAK,OAAO,KAAK,UAAQ,OAAO,IAAI,CAAC;AAAA,EAC9C;AAAA,EACA,QAAQ,MAAM;AACZ,UAAM,SAAS,KAAK,QAAQ,MAAM,KAAK,OAAO,MAAM;AACpD,SAAK,OAAO,KAAK,MAAM;AACvB,SAAK,eAAe,KAAK,MAAM;AAC/B,WAAO;AAAA,EACT;AAAA,EACA,cAAc,eAAe,OAAO;AAClC,SAAK,gBAAgB,MAAM,YAAY;AACvC,SAAK,OAAO,QAAQ,UAAQ;AAC1B,WAAK,WAAW,gBAAgB,KAAK,YAAY,KAAK;AACtD,WAAK,SAAS;AAAA,IAChB,CAAC;AACD,QAAI,KAAK,UAAU,cAAc;AAC/B,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,YAAY,MAAM;AAChB,WAAO,kBAAkB,IAAI,EAAE,kBAAkB;AACjD,WAAO,KAAK,cAAc,KAAK,UAAQ;AACrC,YAAM,QAAQ,kBAAkB,KAAK,KAAK,EAAE,kBAAkB;AAC9D,aAAO,MAAM,OAAO,GAAG,KAAK,MAAM,MAAM;AAAA,IAC1C,CAAC;AAAA,EACH;AAAA,EACA,OAAO,MAAM;AACX,QAAI,CAAC,MAAM;AACT,WAAK,mBAAmB;AACxB;AAAA,IACF;AACA,SAAK,iBAAiB,CAAC;AACvB,WAAO,KAAK,UAAU,WAAW,OAAO,kBAAkB,IAAI,EAAE,kBAAkB;AAClF,UAAM,QAAQ,KAAK,UAAU,YAAY,KAAK;AAC9C,UAAM,eAAe,KAAK,UAAU;AACpC,eAAW,OAAO,MAAM,KAAK,KAAK,QAAQ,KAAK,CAAC,GAAG;AACjD,YAAM,eAAe,CAAC;AACtB,iBAAW,QAAQ,KAAK,QAAQ,IAAI,GAAG,GAAG;AACxC,YAAI,iBAAiB,KAAK,UAAU,KAAK,OAAO,YAAY,KAAK,WAAW;AAC1E;AAAA,QACF;AACA,cAAM,aAAa,KAAK,UAAU,WAAW,KAAK,QAAQ;AAC1D,YAAI,MAAM,MAAM,UAAU,GAAG;AAC3B,uBAAa,KAAK,IAAI;AAAA,QACxB;AAAA,MACF;AACA,UAAI,aAAa,SAAS,GAAG;AAC3B,cAAM,CAAC,IAAI,IAAI,aAAa,MAAM,EAAE;AACpC,YAAI,KAAK,QAAQ;AACf,gBAAM,OAAO,KAAK,OAAO,KAAK,OAAK,MAAM,KAAK,MAAM;AACpD,eAAK,eAAe,KAAK,IAAI;AAAA,QAC/B;AACA,aAAK,eAAe,KAAK,GAAG,YAAY;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,eAAe,WAAW,KAAK,OAAO,QAAQ;AACrD;AAAA,IACF;AACA,QAAI,KAAK,UAAU,gBAAgB,KAAK,cAAc,SAAS,GAAG;AAChE,WAAK,iBAAiB,KAAK,OAAO,OAAO,OAAK,CAAC,EAAE,QAAQ;AAAA,IAC3D,OAAO;AACL,WAAK,iBAAiB,KAAK;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,aAAa;AACX,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,eAAe;AACb,SAAK,YAAY,CAAE;AAAA,EACrB;AAAA,EACA,mBAAmB;AACjB,SAAK,YAAY,EAAE;AAAA,EACrB;AAAA,EACA,SAAS,MAAM;AACb,SAAK,eAAe,KAAK,eAAe,QAAQ,IAAI;AAAA,EACtD;AAAA,EACA,sBAAsB,aAAa;AACjC,QAAI,KAAK,eAAe,WAAW,GAAG;AACpC;AAAA,IACF;AACA,UAAM,kBAAkB,KAAK,oBAAoB;AACjD,QAAI,kBAAkB,IAAI;AACxB,WAAK,eAAe;AAAA,IACtB,OAAO;AACL,WAAK,eAAe,cAAc,KAAK,cAAc,UAAU,OAAK,CAAC,EAAE,QAAQ,IAAI;AAAA,IACrF;AAAA,EACF;AAAA,EACA,cAAc,QAAQ,KAAK;AACzB,QAAI,CAAC,SAAS,MAAM,GAAG;AACrB,aAAO;AAAA,IACT;AACA,QAAI,IAAI,QAAQ,GAAG,MAAM,IAAI;AAC3B,aAAO,OAAO,GAAG;AAAA,IACnB,OAAO;AACL,YAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,UAAI,QAAQ;AACZ,eAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC/C,YAAI,SAAS,MAAM;AACjB,iBAAO;AAAA,QACT;AACA,gBAAQ,MAAM,KAAK,CAAC,CAAC;AAAA,MACvB;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ,MAAM,OAAO;AACnB,UAAM,QAAQ,UAAU,KAAK,cAAc,IAAI,KAAK,iBAAiB,KAAK,cAAc,MAAM,KAAK,UAAU,SAAS;AACtH,UAAM,QAAQ,UAAU,KAAK,cAAc,IAAI,KAAK,iBAAiB;AACrE,WAAO;AAAA,MACL;AAAA,MACA,OAAO,UAAU,KAAK,IAAI,MAAM,SAAS,IAAI;AAAA,MAC7C;AAAA,MACA,UAAU,KAAK;AAAA,MACf,QAAQ,GAAG,KAAK,UAAU,UAAU,IAAI,KAAK;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,UAAM,WAAW,KAAK,UAAU;AAChC,eAAW,YAAY,KAAK,eAAe;AACzC,YAAM,QAAQ,KAAK,UAAU,YAAY,KAAK,cAAc,SAAS,OAAO,KAAK,UAAU,SAAS,IAAI,SAAS;AACjH,YAAM,OAAO,UAAU,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI;AACvD,WAAK,gBAAgB,SAAS,UAAU,QAAQ;AAChD,WAAK,gBAAgB,OAAO,QAAQ,UAAU,UAAU,KAAK,UAAU,sBAAsB;AAAA,IAC/F;AACA,QAAI,KAAK,UAAU,cAAc;AAC/B,WAAK,iBAAiB,KAAK,cAAc,OAAO,OAAK,KAAK,cAAc,QAAQ,CAAC,MAAM,EAAE;AAAA,IAC3F;AAAA,EACF;AAAA,EACA,cAAc,MAAM;AAClB,SAAK,eAAe,KAAK,IAAI;AAC7B,QAAI,KAAK,QAAQ;AACf,YAAM,SAAS,KAAK;AACpB,YAAM,eAAe,KAAK,eAAe,KAAK,OAAK,MAAM,MAAM;AAC/D,UAAI,CAAC,cAAc;AACjB,aAAK,eAAe,KAAK,MAAM;AAAA,MACjC;AAAA,IACF,WAAW,KAAK,UAAU;AACxB,iBAAW,SAAS,KAAK,UAAU;AACjC,cAAM,WAAW;AACjB,aAAK,eAAe,KAAK,KAAK;AAAA,MAChC;AAAA,IACF;AACA,SAAK,iBAAiB,CAAC,GAAG,KAAK,eAAe,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;AAAA,EACjF;AAAA,EACA,cAAc,MAAM;AAClB,SAAK,iBAAiB,KAAK,eAAe,OAAO,OAAK,MAAM,IAAI;AAChE,QAAI,KAAK,QAAQ;AACf,YAAM,WAAW,KAAK,OAAO;AAC7B,UAAI,SAAS,MAAM,OAAK,EAAE,QAAQ,GAAG;AACnC,aAAK,iBAAiB,KAAK,eAAe,OAAO,OAAK,MAAM,KAAK,MAAM;AAAA,MACzE;AAAA,IACF,WAAW,KAAK,UAAU;AACxB,WAAK,iBAAiB,KAAK,cAAc,OAAO,OAAK,EAAE,WAAW,IAAI;AAAA,IACxE;AAAA,EACF;AAAA,EACA,iBAAiB,QAAQ,KAAK;AAC5B,UAAM,QAAQ,kBAAkB,IAAI,KAAK,EAAE,kBAAkB;AAC7D,WAAO,MAAM,QAAQ,MAAM,IAAI;AAAA,EACjC;AAAA,EACA,kBAAkB,OAAO;AACvB,QAAI,QAAQ,GAAG;AACb,aAAO,KAAK,gBAAgB,KAAK,eAAe,SAAS,IAAI,IAAI,KAAK,eAAe;AAAA,IACvF;AACA,WAAO,KAAK,gBAAgB,IAAI,KAAK,eAAe,SAAS,IAAI,KAAK,eAAe;AAAA,EACvF;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,KAAK,eAAe,WAAW,KAAK,KAAK,eAAe,MAAM,OAAK,EAAE,QAAQ,GAAG;AAClF;AAAA,IACF;AACA,SAAK,eAAe,KAAK,kBAAkB,KAAK;AAChD,QAAI,KAAK,WAAW,UAAU;AAC5B,WAAK,YAAY,KAAK;AAAA,IACxB;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,UAAU,cAAc;AAC/B,aAAO;AAAA,IACT;AACA,QAAI,KAAK,eAAe,MAAM,KAAK,eAAe,QAAW;AAC3D,aAAO;AAAA,IACT;AACA,UAAM,gBAAgB,KAAK,eAAe,QAAQ,KAAK,gBAAgB;AACvE,QAAI,KAAK,oBAAoB,gBAAgB,GAAG;AAC9C,aAAO;AAAA,IACT;AACA,WAAO,KAAK,IAAI,KAAK,aAAa,aAAa;AAAA,EACjD;AAAA,EACA,SAAS,OAAO,MAAM;AACpB,UAAM,SAAS,oBAAI,IAAI;AACvB,QAAI,MAAM,WAAW,GAAG;AACtB,aAAO;AAAA,IACT;AAEA,QAAI,MAAM,QAAQ,MAAM,CAAC,EAAE,MAAM,IAAI,CAAC,GAAG;AACvC,iBAAW,QAAQ,OAAO;AACxB,cAAM,YAAY,KAAK,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,UAAU,KAAK,QAAQ,GAAG,KAAK,CAAC;AAClF,eAAO,IAAI,MAAM,QAAQ;AAAA,MAC3B;AACA,aAAO;AAAA,IACT;AACA,UAAM,UAAU,WAAW,KAAK,UAAU,OAAO;AACjD,UAAM,QAAQ,UAAQ;AACpB,YAAM,MAAM,UAAU,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,IAAI;AACxD,aAAO,UAAU,GAAG,IAAI,MAAM;AAAA,IAChC;AAEA,eAAW,QAAQ,OAAO;AACxB,YAAM,MAAM,MAAM,IAAI;AACtB,YAAM,QAAQ,OAAO,IAAI,GAAG;AAC5B,UAAI,OAAO;AACT,cAAM,KAAK,IAAI;AAAA,MACjB,OAAO;AACL,eAAO,IAAI,KAAK,CAAC,IAAI,CAAC;AAAA,MACxB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS,QAAQ;AACf,UAAM,cAAc,WAAW,KAAK,UAAU,OAAO;AACrD,UAAM,QAAQ,CAAC;AACf,eAAW,OAAO,MAAM,KAAK,OAAO,KAAK,CAAC,GAAG;AAC3C,UAAI,IAAI,MAAM;AACd,UAAI,QAAQ,QAAW;AACrB,cAAM,eAAe,OAAO,IAAI,MAAS,KAAK,CAAC;AAC/C,cAAM,KAAK,GAAG,aAAa,IAAI,OAAK;AAClC,YAAE,QAAQ;AACV,iBAAO;AAAA,QACT,CAAC,CAAC;AACF;AAAA,MACF;AACA,YAAM,cAAc,SAAS,GAAG;AAChC,YAAM,SAAS;AAAA,QACb,OAAO,cAAc,KAAK,OAAO,GAAG;AAAA,QACpC,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU,CAAC,KAAK,UAAU;AAAA,QAC1B,QAAQ,MAAM;AAAA,MAChB;AACA,YAAM,WAAW,cAAc,KAAK,UAAU,YAAY,KAAK,UAAU;AACzE,YAAM,aAAa,KAAK,UAAU,eAAe,MAAM;AACrD,YAAI,aAAa;AACf,iBAAO,IAAI;AAAA,QACb;AACA,eAAO;AAAA,UACL,CAAC,QAAQ,GAAG;AAAA,QACd;AAAA,MACF;AACA,YAAM,WAAW,OAAO,IAAI,GAAG,EAAE,IAAI,OAAK;AACxC,UAAE,SAAS;AACX,UAAE,WAAW;AACb,UAAE,QAAQ;AACV,eAAO;AAAA,MACT,CAAC;AACD,aAAO,WAAW;AAClB,aAAO,QAAQ,WAAW,KAAK,SAAS,IAAI,OAAK,EAAE,KAAK,CAAC;AACzD,YAAM,KAAK,MAAM;AACjB,YAAM,KAAK,GAAG,QAAQ;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc;AACZ,SAAK,cAAc;AAAA,MACjB,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,kBAAkB;AAAA,IACpB;AAAA,EACF;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe,WAAW,aAAa,QAAQ;AAC7C,UAAM,IAAI,KAAK;AACf,UAAM,eAAe,EAAE,aAAa;AACpC,UAAM,YAAY,KAAK,IAAI,GAAG,SAAS;AACvC,UAAM,mBAAmB,YAAY,eAAe;AACpD,QAAI,MAAM,KAAK,IAAI,aAAa,KAAK,KAAK,gBAAgB,KAAK,EAAE,mBAAmB,EAAE;AACtF,UAAM,cAAc;AACpB,UAAM,WAAW,KAAK,IAAI,GAAG,cAAc,EAAE,gBAAgB;AAC7D,QAAI,QAAQ,KAAK,IAAI,UAAU,KAAK,MAAM,gBAAgB,CAAC;AAC3D,QAAI,aAAa,EAAE,aAAa,KAAK,KAAK,KAAK,IAAI,EAAE,aAAa,KAAK,IAAI,OAAO,MAAM;AACxF,iBAAa,CAAC,MAAM,UAAU,IAAI,aAAa;AAC/C,YAAQ,CAAC,MAAM,KAAK,IAAI,QAAQ;AAChC,UAAM,CAAC,MAAM,GAAG,IAAI,MAAM;AAC1B,aAAS;AACT,YAAQ,KAAK,IAAI,GAAG,KAAK;AACzB,WAAO;AACP,UAAM,KAAK,IAAI,aAAa,GAAG;AAC/B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,YAAY,aAAa;AACrC,UAAM,mBAAmB,KAAK,IAAI,GAAG,KAAK,MAAM,cAAc,UAAU,CAAC;AACzE,SAAK,cAAc;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,SAAS,YAAY,YAAY;AAC3C,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK;AACT,UAAM,aAAa,UAAU;AAC7B,UAAM,MAAM;AACZ,UAAM,SAAS,MAAM;AACrB,QAAI,eAAe,cAAc,eAAe,SAAS;AACvD,aAAO;AAAA,IACT;AACA,QAAI,aAAa,QAAQ;AACvB,aAAO,MAAM,aAAa;AAAA,IAC5B,WAAW,WAAW,KAAK;AACzB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAAwB;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,wBAAuB;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,gBAAgB,CAAC,OAAO,SAAS,UAAU,MAAM;AACvD,IAAM,mBAAmB,OAAO,0BAA0B,cAAc,0BAA0B;AAClG,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,WAAW,OAAO,eAAe,aAAa,WAAW;AACnE,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,gBAAgB;AACrB,SAAK,YAAY;AACjB,SAAK,QAAQ,CAAC;AACd,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,oBAAoB,MAAM,IAAI;AACnC,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,YAAY,IAAI,QAAQ;AAC7B,SAAK,oBAAoB;AACzB,SAAK,sBAAsB;AAC3B,SAAK,sBAAsB;AAC3B,SAAK,YAAY,YAAY;AAAA,EAC/B;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,QAAI,UAAU,KAAK,cAAc;AAC/B,WAAK,eAAe;AACpB,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,KAAK,YAAY;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,cAAc;AACvB,YAAM,SAAS,KAAK,WAAW,QAAQ;AACvC,aAAO,cAAc,SAAS,IAAI;AAAA,IACpC;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,SAAK,UAAU,KAAK,UAAU;AAC9B,SAAK,kBAAkB,KAAK,kBAAkB;AAC9C,SAAK,mBAAmB,KAAK,iBAAiB;AAC9C,SAAK,gBAAgB,KAAK,kBAAkB;AAC5C,SAAK,cAAc;AACnB,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAAA,EAC/B;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,OAAO;AACjB,YAAM,SAAS,QAAQ;AACvB,WAAK,eAAe,OAAO,cAAc,OAAO,WAAW;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,KAAK;AACpB,SAAK,UAAU,SAAS;AACxB,SAAK,UAAU,YAAY;AAC3B,QAAI,KAAK,UAAU;AACjB,WAAK,UAAU,YAAY,KAAK,UAAU,YAAY,KAAK,SAAS;AAAA,IACtE;AAAA,EACF;AAAA,EACA,SAAS,QAAQ,kBAAkB,OAAO;AACxC,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,MAAM,QAAQ,MAAM;AACvC,QAAI,QAAQ,KAAK,SAAS,KAAK,aAAa;AAC1C;AAAA,IACF;AACA,QAAI;AACJ,QAAI,KAAK,eAAe;AACtB,YAAM,aAAa,KAAK,cAAc,WAAW;AACjD,iBAAW,KAAK,cAAc,YAAY,QAAQ,YAAY,YAAY,KAAK,mBAAmB;AAAA,IACpG,OAAO;AACL,YAAM,OAAO,KAAK,UAAU,cAAc,IAAI,OAAO,MAAM,EAAE;AAC7D,YAAM,aAAa,kBAAkB,KAAK,YAAY,KAAK;AAC3D,iBAAW,KAAK,cAAc,YAAY,KAAK,WAAW,KAAK,cAAc,UAAU;AAAA,IACzF;AACA,QAAI,UAAU,QAAQ,GAAG;AACvB,WAAK,iBAAiB,YAAY;AAAA,IACpC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,UAAM,QAAQ,KAAK;AACnB,UAAM,YAAY,MAAM,eAAe,MAAM;AAAA,EAC/C;AAAA,EACA,iBAAiB;AACf,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,0BAA0B;AACxB,SAAK,mBAAmB,KAAK,0BAA0B,KAAK,SAAS;AACrE,QAAI,cAAc,SAAS,KAAK,gBAAgB,GAAG;AACjD,WAAK,qBAAqB,KAAK,gBAAgB;AAAA,IACjD,OAAO;AACL,WAAK,qBAAqB,QAAQ;AAAA,IACpC;AACA,QAAI,KAAK,UAAU;AACjB,WAAK,iBAAiB;AAAA,IACxB;AACA,SAAK,UAAU,MAAM,UAAU;AAAA,EACjC;AAAA,EACA,qBAAqB,iBAAiB;AACpC,kBAAc,QAAQ,cAAY;AAChC,YAAM,mBAAmB,aAAa,QAAQ;AAC9C,WAAK,UAAU,YAAY,KAAK,WAAW,gBAAgB;AAC3D,WAAK,UAAU,YAAY,KAAK,SAAS,gBAAgB;AAAA,IAC3D,CAAC;AACD,UAAM,gBAAgB,aAAa,eAAe;AAClD,SAAK,UAAU,SAAS,KAAK,WAAW,aAAa;AACrD,SAAK,UAAU,SAAS,KAAK,SAAS,aAAa;AAAA,EACrD;AAAA,EACA,gBAAgB;AACd,SAAK,MAAM,kBAAkB,MAAM;AACjC,gBAAU,KAAK,iBAAiB,eAAe,QAAQ,EAAE,KAAK,UAAU,KAAK,SAAS,GAAG,UAAU,GAAG,gBAAgB,CAAC,EAAE,UAAU,OAAK;AACtI,cAAM,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE,aAAa;AACxD,YAAI,CAAC,QAAQ,KAAK,WAAW,KAAK,CAAC,EAAE,QAAQ;AAC3C;AAAA,QACF;AACA,cAAM,YAAY,CAAC,QAAQ,KAAK,WAAW,IAAI,EAAE,OAAO,YAAY,KAAK,CAAC,EAAE;AAC5E,aAAK,mBAAmB,SAAS;AAAA,MACnC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AACpB,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,SAAK,MAAM,kBAAkB,MAAM;AACjC,YAAM,UAAU,KAAK,WAAW,cAAc;AAAA,QAC5C,SAAS;AAAA,MACX,CAAC,GAAG,UAAU,KAAK,WAAW,SAAS;AAAA,QACrC,SAAS;AAAA,MACX,CAAC,CAAC,EAAE,KAAK,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU,YAAU,KAAK,cAAc,MAAM,CAAC;AAAA,IACpF,CAAC;AAAA,EACH;AAAA,EACA,cAAc,QAAQ;AACpB,QAAI,KAAK,QAAQ,SAAS,OAAO,MAAM,KAAK,KAAK,UAAU,SAAS,OAAO,MAAM,GAAG;AAClF;AAAA,IACF;AACA,UAAM,OAAO,OAAO,QAAQ,OAAO,gBAAgB,OAAO,aAAa;AACvE,QAAI,OAAO,UAAU,OAAO,OAAO,cAAc,QAAQ,KAAK,CAAC,KAAK,KAAK,QAAQ,SAAS,KAAK,CAAC,CAAC,GAAG;AAClG;AAAA,IACF;AACA,SAAK,MAAM,IAAI,MAAM,KAAK,aAAa,KAAK,CAAC;AAAA,EAC/C;AAAA,EACA,eAAe,OAAO,aAAa;AACjC,SAAK,QAAQ,SAAS,CAAC;AACvB,SAAK,oBAAoB;AACzB,SAAK,cAAc,MAAM;AACzB,QAAI,KAAK,eAAe;AACtB,WAAK,kBAAkB,WAAW;AAAA,IACpC,OAAO;AACL,WAAK,kBAAkB;AACvB,WAAK,aAAa,WAAW;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,aAAa,aAAa;AACxB,SAAK,OAAO,KAAK,KAAK,KAAK;AAC3B,QAAI,gBAAgB,OAAO;AACzB;AAAA,IACF;AACA,SAAK,MAAM,kBAAkB,MAAM;AACjC,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,cAAM,cAAc,KAAK,iBAAiB;AAC1C,aAAK,cAAc,cAAc,GAAG,WAAW;AAC/C,aAAK,wBAAwB;AAC7B,aAAK,SAAS,KAAK,YAAY,WAAW;AAAA,MAC5C,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,aAAa;AAC7B,SAAK,MAAM,kBAAkB,MAAM;AACjC,WAAK,mBAAmB,EAAE,KAAK,MAAM;AACnC,YAAI,aAAa;AACf,eAAK,kBAAkB,KAAK,YAAY;AACxC,eAAK,wBAAwB;AAAA,QAC/B,OAAO;AACL,eAAK,kBAAkB;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,WAAW;AAC5B,QAAI,KAAK,eAAe;AACtB,WAAK,kBAAkB,SAAS;AAAA,IAClC;AACA,SAAK,sBAAsB;AAC3B,SAAK,iBAAiB,SAAS;AAAA,EACjC;AAAA,EACA,qBAAqB,QAAQ;AAC3B,QAAI,KAAK,qBAAqB;AAC5B,WAAK,gBAAgB,MAAM,SAAS,GAAG,MAAM;AAC7C,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,QAAI,CAAC,KAAK,iBAAiB;AACzB;AAAA,IACF;AACA,SAAK,gBAAgB,MAAM,SAAS;AAAA,EACtC;AAAA,EACA,wBAAwB;AACtB,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,kBAAkB,YAAY,MAAM;AAClC,QAAI,aAAa,KAAK,wBAAwB,WAAW;AACvD;AAAA,IACF;AACA,gBAAY,aAAa,KAAK,iBAAiB;AAC/C,UAAM,QAAQ,KAAK,cAAc,eAAe,WAAW,KAAK,aAAa,KAAK,YAAY;AAC9F,SAAK,qBAAqB,MAAM,YAAY;AAC5C,SAAK,cAAc,MAAM,YAAY,cAAc,MAAM,UAAU;AACnE,SAAK,MAAM,IAAI,MAAM;AACnB,WAAK,OAAO,KAAK,KAAK,MAAM,MAAM,MAAM,OAAO,MAAM,GAAG,CAAC;AACzD,WAAK,OAAO,KAAK;AAAA,QACf,OAAO,MAAM;AAAA,QACb,KAAK,MAAM;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AACD,QAAI,UAAU,SAAS,KAAK,KAAK,wBAAwB,GAAG;AAC1D,WAAK,iBAAiB,YAAY;AAClC,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,cAAc,WAAW,aAAa,KAAK,KAAK,gBAAgB,GAAG;AAC1E,aAAO,QAAQ,QAAQ,KAAK,cAAc,UAAU;AAAA,IACtD;AACA,UAAM,CAAC,KAAK,IAAI,KAAK;AACrB,SAAK,OAAO,KAAK,CAAC,KAAK,CAAC;AACxB,WAAO,QAAQ,QAAQ,EAAE,KAAK,MAAM;AAClC,YAAM,SAAS,KAAK,UAAU,cAAc,IAAI,MAAM,MAAM,EAAE;AAC9D,YAAM,eAAe,OAAO;AAC5B,WAAK,gBAAgB,MAAM,SAAS,GAAG,eAAe,KAAK,WAAW;AACtE,YAAM,cAAc,KAAK,iBAAiB;AAC1C,WAAK,cAAc,cAAc,cAAc,WAAW;AAC1D,aAAO,KAAK,cAAc;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,WAAW;AAC1B,QAAI,KAAK,qBAAqB,cAAc,GAAG;AAC7C;AAAA,IACF;AACA,UAAM,UAAU,KAAK,gBAAgB,KAAK,kBAAkB,KAAK;AACjE,QAAI,YAAY,KAAK,UAAU,gBAAgB,QAAQ,eAAe,GAAG;AACvE,WAAK,MAAM,IAAI,MAAM,KAAK,YAAY,KAAK,CAAC;AAC5C,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,0BAA0B,YAAY;AACpC,QAAI,KAAK,aAAa,QAAQ;AAC5B,aAAO,KAAK;AAAA,IACd;AACA,UAAM,aAAa,KAAK,QAAQ,sBAAsB;AACtD,UAAM,YAAY,SAAS,gBAAgB,aAAa,SAAS,KAAK;AACtE,UAAM,YAAY,WAAW,MAAM,OAAO;AAC1C,UAAM,SAAS,WAAW;AAC1B,UAAM,iBAAiB,WAAW,sBAAsB,EAAE;AAC1D,QAAI,YAAY,SAAS,iBAAiB,YAAY,SAAS,gBAAgB,cAAc;AAC3F,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,KAAK,UAAU;AAClB;AAAA,IACF;AACA,SAAK,UAAU,KAAK,UAAU,aAAa,KAAK,UAAU,WAAW,cAAc,KAAK,QAAQ,IAAI,SAAS,cAAc,KAAK,QAAQ;AACxI,QAAI,CAAC,KAAK,SAAS;AACjB,YAAM,IAAI,MAAM,qBAAqB,KAAK,QAAQ,mCAAmC;AAAA,IACvF;AACA,SAAK,iBAAiB;AACtB,SAAK,QAAQ,YAAY,KAAK,SAAS;AAAA,EACzC;AAAA,EACA,mBAAmB;AACjB,UAAM,SAAS,KAAK,QAAQ,sBAAsB;AAClD,UAAM,SAAS,KAAK,QAAQ,sBAAsB;AAClD,UAAM,aAAa,OAAO,OAAO,OAAO;AACxC,SAAK,UAAU,MAAM,OAAO,aAAa;AACzC,SAAK,UAAU,MAAM,QAAQ,OAAO,QAAQ;AAC5C,SAAK,UAAU,MAAM,WAAW,OAAO,QAAQ;AAAA,EACjD;AAAA,EACA,mBAAmB;AACjB,UAAM,SAAS,KAAK,QAAQ,sBAAsB;AAClD,UAAM,SAAS,KAAK,QAAQ,sBAAsB;AAClD,UAAM,QAAQ,OAAO;AACrB,QAAI,KAAK,qBAAqB,OAAO;AACnC,YAAM,eAAe,OAAO,SAAS,OAAO;AAC5C,WAAK,UAAU,MAAM,SAAS,eAAe,QAAQ;AACrD,WAAK,UAAU,MAAM,MAAM;AAAA,IAC7B,WAAW,KAAK,qBAAqB,UAAU;AAC7C,YAAM,YAAY,OAAO,MAAM,OAAO;AACtC,WAAK,UAAU,MAAM,MAAM,YAAY,QAAQ;AAC/C,WAAK,UAAU,MAAM,SAAS;AAAA,IAChC;AAAA,EACF;AAAA,EACA,0BAA0B;AACxB,SAAK,MAAM,kBAAkB,MAAM;AACjC,gBAAU,KAAK,WAAW,WAAW,EAAE,KAAK,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU,WAAS;AACxF,cAAM,SAAS,MAAM;AACrB,YAAI,OAAO,YAAY,SAAS;AAC9B;AAAA,QACF;AACA,cAAM,eAAe;AAAA,MACvB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,kBAAqB,SAAS,GAAM,kBAAqB,MAAM,GAAM,kBAAkB,sBAAsB,GAAM,kBAAqB,UAAU,GAAM,kBAAkB,UAAU,CAAC,CAAC;AAAA,IACtP;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,MACjC,WAAW,SAAS,+BAA+B,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,GAAG,UAAU;AACjC,UAAG,YAAY,KAAK,GAAG,UAAU;AACjC,UAAG,YAAY,KAAK,GAAG,UAAU;AAAA,QACnC;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AACxE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB,GAAG;AAAA,QAC1E;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,cAAc;AAAA,QACd,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,QACrE,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,mBAAmB,CAAC,GAAG,mBAAmB;AAAA,MAC5C;AAAA,MACA,SAAS;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,MACA,UAAU,CAAI,oBAAoB;AAAA,MAClC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,QAAQ,WAAW,GAAG,2BAA2B,aAAa,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,CAAC;AAAA,MACrO,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,OAAO,CAAC;AAChF,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,UAAG,UAAU,GAAG,OAAO,MAAM,CAAC;AAC9B,UAAG,eAAe,GAAG,OAAO,MAAM,CAAC;AACnC,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa,EAAE;AAClB,UAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,OAAO,CAAC;AAAA,QAClF;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,IAAI,iBAAiB,IAAI,EAAE;AAC5C,UAAG,UAAU;AACb,UAAG,YAAY,cAAc,IAAI,kBAAkB,CAAC;AACpD,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,iBAAiB,IAAI,aAAa;AACjD,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,sBAAsB,IAAI,iBAAiB,IAAI,MAAM,MAAM;AAC1E,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,iBAAiB,IAAI,EAAE;AAAA,QAC9C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAgB;AAAA,MAC/B,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBV,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,YAAY;AACtB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,eAAe,IAAI,QAAQ;AAAA,EAClC;AAAA,EACA,IAAI,QAAQ;AACV,YAAQ,KAAK,WAAW,cAAc,eAAe,IAAI,KAAK;AAAA,EAChE;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,UAAU;AACpB,WAAK,aAAa,KAAK;AAAA,QACrB,OAAO,KAAK;AAAA,QACZ,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,UAAU,KAAK,gBAAgB;AACtC,WAAK,iBAAiB,KAAK;AAC3B,WAAK,aAAa,KAAK;AAAA,QACrB,OAAO,KAAK;AAAA,QACZ,UAAU,KAAK;AAAA,QACf,OAAO,KAAK,WAAW,cAAc;AAAA,MACvC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,SAAS;AAAA,EAC7B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAsB,kBAAqB,UAAU,CAAC;AAAA,IACzF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,MACzB,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACxD;AAAA,MACA,UAAU,CAAI,oBAAoB;AAAA,MAClC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAI;AAAA,CACH,SAAUA,UAAS;AAClB,EAAAA,SAAQ,KAAK,IAAI;AACjB,EAAAA,SAAQ,OAAO,IAAI;AACnB,EAAAA,SAAQ,KAAK,IAAI;AACjB,EAAAA,SAAQ,OAAO,IAAI;AACnB,EAAAA,SAAQ,SAAS,IAAI;AACrB,EAAAA,SAAQ,WAAW,IAAI;AACvB,EAAAA,SAAQ,WAAW,IAAI;AACzB,GAAG,YAAY,UAAU,CAAC,EAAE;AAC5B,SAAS,+BAA+B;AACtC,SAAO,IAAI,sBAAsB;AACnC;AACA,IAAM,wBAAN,MAA4B;AAAA,EAC1B,cAAc;AACZ,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO,MAAM,UAAU,cAAc;AACnC,SAAK,WAAW;AAChB,QAAI,CAAC,KAAK,YAAY,CAAC,YAAY,cAAc;AAC/C,WAAK,UAAU,KAAK,IAAI;AAAA,IAC1B;AACA,QAAI,UAAU;AACZ,UAAI,KAAK,QAAQ;AACf,cAAM,gBAAgB,KAAK,OAAO,SAAS;AAC3C,cAAM,gBAAgB,KAAK,OAAO,SAAS,OAAO,OAAK,EAAE,QAAQ,EAAE;AACnE,aAAK,OAAO,WAAW,kBAAkB;AAAA,MAC3C,WAAW,KAAK,UAAU;AACxB,aAAK,0BAA0B,KAAK,UAAU,IAAI;AAClD,aAAK,gBAAgB,IAAI;AACzB,YAAI,gBAAgB,KAAK,gBAAgB,IAAI,GAAG;AAC9C,eAAK,YAAY,CAAC,GAAG,KAAK,UAAU,OAAO,OAAK,EAAE,WAAW,IAAI,GAAG,IAAI;AAAA,QAC1E,OAAO;AACL,eAAK,YAAY,CAAC,GAAG,KAAK,WAAW,GAAG,KAAK,SAAS,OAAO,OAAK,CAAC,EAAE,QAAQ,CAAC;AAAA,QAChF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,MAAM,UAAU;AACvB,SAAK,YAAY,KAAK,UAAU,OAAO,OAAK,MAAM,IAAI;AACtD,SAAK,WAAW;AAChB,QAAI,UAAU;AACZ,UAAI,KAAK,UAAU,KAAK,OAAO,UAAU;AACvC,cAAM,WAAW,KAAK,OAAO;AAC7B,aAAK,cAAc,KAAK,MAAM;AAC9B,aAAK,gBAAgB,KAAK,MAAM;AAChC,aAAK,UAAU,KAAK,GAAG,SAAS,OAAO,OAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC;AACtE,aAAK,OAAO,WAAW;AAAA,MACzB,WAAW,KAAK,UAAU;AACxB,aAAK,0BAA0B,KAAK,UAAU,KAAK;AACnD,aAAK,gBAAgB,IAAI;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,cAAc;AAClB,SAAK,YAAY,eAAe,KAAK,UAAU,OAAO,OAAK,EAAE,QAAQ,IAAI,CAAC;AAAA,EAC5E;AAAA,EACA,0BAA0B,UAAU,UAAU;AAC5C,eAAW,SAAS,UAAU;AAC5B,UAAI,MAAM,UAAU;AAClB;AAAA,MACF;AACA,YAAM,WAAW;AAAA,IACnB;AAAA,EACF;AAAA,EACA,gBAAgB,QAAQ;AACtB,SAAK,YAAY,CAAC,GAAG,KAAK,UAAU,OAAO,OAAK,EAAE,WAAW,MAAM,GAAG,GAAG,OAAO,SAAS,OAAO,OAAK,EAAE,WAAW,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC;AAAA,EACvJ;AAAA,EACA,cAAc,QAAQ;AACpB,SAAK,YAAY,KAAK,UAAU,OAAO,OAAK,MAAM,MAAM;AAAA,EAC1D;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,KAAK,SAAS,MAAM,OAAK,CAAC,EAAE,YAAY,EAAE,QAAQ;AAAA,EAC3D;AACF;AACA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,cAAc;AACZ,SAAK,mBAAmB;AACxB,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,uBAAuB;AAC5B,SAAK,cAAc;AACnB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,gBAAe;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,KAAK,SAAS;AACZ,YAAQ,KAAK,OAAO;AAAA,EACtB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,gBAAe;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,0BAA0B,IAAI,eAAe,2BAA2B;AAC9E,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,SAAS,WAAW,QAAQ,mBAAmB,aAAa,KAAK,UAAU;AACrF,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,oBAAoB;AACzB,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,4BAA4B;AACjC,SAAK,mBAAmB;AACxB,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,yBAAyB;AAC9B,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,aAAa,CAAC;AACnB,SAAK,wBAAwB,MAAM,MAAM;AAAA,MACvC,WAAW;AAAA,IACb,CAAC;AACD,SAAK,WAAW;AAChB,SAAK,uBAAuB;AAC5B,SAAK,gBAAgB;AACrB,SAAK,qBAAqB;AAC1B,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,SAAS;AAEd,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,kBAAkB;AACvB,SAAK,gBAAgB,CAAC;AACtB,SAAK,aAAa;AAClB,SAAK,aAAa,MAAM;AACxB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,eAAe,CAAC;AACrB,SAAK,eAAe;AACpB,SAAK,YAAY,IAAI,QAAQ;AAC7B,SAAK,aAAa,IAAI,QAAQ;AAC9B,SAAK,SAAS,CAAC;AACf,SAAK,YAAY,OAAK;AACtB,SAAK,YAAY,UAAQ;AACvB,YAAM,SAAS,KAAK,cAAc,KAAK,OAAK,EAAE,UAAU,IAAI;AAC5D,WAAK,SAAS,MAAM;AAAA,IACtB;AACA,SAAK,gBAAgB,CAAC,GAAG,SAAS;AAChC,UAAI,KAAK,WAAW;AAClB,eAAO,KAAK,UAAU,KAAK,KAAK;AAAA,MAClC;AACA,aAAO;AAAA,IACT;AACA,SAAK,YAAY,OAAK;AAAA,IAAC;AACvB,SAAK,aAAa,MAAM;AAAA,IAAC;AACzB,SAAK,mBAAmB,MAAM;AAC9B,SAAK,YAAY,IAAI,UAAU,MAAM,oBAAoB,kBAAkB,IAAI,6BAA6B,CAAC;AAC7G,SAAK,UAAU,YAAY;AAAA,EAC7B;AAAA,EACA,IAAI,WAAW;AACb,WAAO,CAAC,CAAC,KAAK,cAAc,KAAK,cAAc,KAAK;AAAA,EACtD;AAAA,EACA,IAAI,SAAS;AACX,WAAO,CAAC,KAAK;AAAA,EACf;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,gBAAgB;AACrB,SAAK,SAAS,SAAS,CAAC;AAAA,EAC1B;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,YAAY,KAAK;AAAA,EAC/B;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,IAAI;AAClB,QAAI,OAAO,UAAa,OAAO,QAAQ,CAAC,WAAW,EAAE,GAAG;AACtD,YAAM,MAAM,mCAAmC;AAAA,IACjD;AACA,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,IAAI,mBAAmB;AACrB,QAAI,UAAU,KAAK,iBAAiB,GAAG;AACrC,aAAO,KAAK;AAAA,IACd,WAAW,UAAU,KAAK,OAAO,gBAAgB,GAAG;AAClD,aAAO,KAAK,OAAO;AAAA,IACrB;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,iBAAiB,OAAO;AAC1B,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,IAAI,kBAAkB;AACpB,QAAI,UAAU,KAAK,gBAAgB,GAAG;AACpC,aAAO,KAAK;AAAA,IACd,WAAW,UAAU,KAAK,OAAO,eAAe,GAAG;AACjD,aAAO,KAAK,OAAO;AAAA,IACrB;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB,OAAO;AACzB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,cAAc,IAAI,OAAK,EAAE,KAAK;AAAA,EAC5C;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,cAAc,SAAS;AAAA,EACrC;AAAA,EACA,IAAI,uBAAuB;AACzB,QAAI,KAAK,eAAe;AACtB,aAAO,KAAK,cAAc;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,aAAa;AACf,QAAI,CAAC,KAAK,YAAY;AACpB,aAAO;AAAA,IACT;AACA,UAAM,OAAO,KAAK,WAAW,YAAY,EAAE,KAAK;AAChD,WAAO,KAAK,UAAU,CAAC,KAAK,UAAU,cAAc,KAAK,OAAK,EAAE,MAAM,YAAY,MAAM,IAAI,MAAM,CAAC,KAAK,gBAAgB,KAAK,UAAU,CAAC,KAAK,cAAc,KAAK,OAAK,EAAE,MAAM,YAAY,MAAM,IAAI,MAAM,CAAC,KAAK;AAAA,EACjN;AAAA,EACA,IAAI,sBAAsB;AACxB,WAAO,KAAK,sBAAsB,CAAC,KAAK;AAAA,EAC1C;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,aAAa,KAAK,UAAU,UAAU,SAAS;AAAA,EAC7D;AAAA,EACA,IAAI,aAAa;AACf,UAAM,OAAO,KAAK,YAAY,KAAK;AACnC,WAAO,QAAQ,KAAK,UAAU,KAAK;AAAA,EACrC;AAAA,EACA,WAAW;AACT,SAAK,kBAAkB;AACvB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,UAAU;AACpB,WAAK,UAAU,cAAc;AAAA,IAC/B;AACA,QAAI,QAAQ,OAAO;AACjB,WAAK,UAAU,QAAQ,MAAM,gBAAgB,CAAC,CAAC;AAAA,IACjD;AACA,QAAI,QAAQ,QAAQ;AAClB,WAAK,cAAc,UAAU,QAAQ,OAAO,YAAY;AAAA,IAC1D;AACA,QAAI,QAAQ,SAAS;AACnB,UAAI,CAAC,QAAQ,OAAO;AAClB,aAAK,UAAU,CAAC,GAAG,KAAK,KAAK,CAAC;AAAA,MAChC;AAAA,IACF;AACA,QAAI,QAAQ,YAAY;AACtB,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,aAAa;AAClB,WAAK,uBAAuB;AAAA,IAC9B;AACA,QAAI,UAAU,KAAK,SAAS,GAAG;AAC7B,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,KAAK;AACpB,SAAK,UAAU,SAAS;AAAA,EAC1B;AAAA,EACA,cAAc,QAAQ;AACpB,UAAM,UAAU,OAAO;AACvB,QAAI,OAAO,OAAO,OAAO,EAAE,SAAS,OAAO,GAAG;AAC5C,UAAI,KAAK,UAAU,MAAM,MAAM,OAAO;AACpC;AAAA,MACF;AACA,WAAK,cAAc,MAAM;AAAA,IAC3B,WAAW,WAAW,QAAQ,WAAW,GAAG;AAC1C,WAAK,WAAW,KAAK,QAAQ,kBAAkB,CAAC;AAAA,IAClD;AAAA,EACF;AAAA,EACA,cAAc,QAAQ;AACpB,UAAM,SAAS,OAAO;AACtB,QAAI,KAAK,eAAe,KAAK,YAAY,kBAAkB,QAAQ;AACjE,WAAK,mBAAmB,MAAM;AAAA,IAChC,OAAO;AACL,WAAK,mBAAmB,MAAM;AAAA,IAChC;AAAA,EACF;AAAA,EACA,mBAAmB,QAAQ;AACzB,YAAQ,OAAO,KAAK;AAAA,MAClB,KAAK,QAAQ;AACX,aAAK,iBAAiB,MAAM;AAC5B;AAAA,MACF,KAAK,QAAQ;AACX,aAAK,eAAe,MAAM;AAC1B;AAAA,MACF,KAAK,QAAQ;AACX,aAAK,aAAa,MAAM;AACxB;AAAA,MACF,KAAK,QAAQ;AACX,aAAK,aAAa,MAAM;AACxB;AAAA,MACF,KAAK,QAAQ;AACX,aAAK,WAAW,MAAM;AACtB;AAAA,MACF,KAAK,QAAQ;AACX,aAAK,MAAM;AACX,eAAO,eAAe;AACtB;AAAA,MACF,KAAK,QAAQ;AACX,aAAK,iBAAiB;AACtB;AAAA,IACJ;AAAA,EACF;AAAA,EACA,mBAAmB,QAAQ;AACzB,YAAQ,OAAO,KAAK;AAAA,MAClB,KAAK,QAAQ;AACX,aAAK,iBAAiB;AACtB,eAAO,eAAe;AACtB;AAAA,IACJ;AAAA,EACF;AAAA,EACA,gBAAgB,QAAQ;AACtB,QAAI,KAAK,6BAA6B,OAAO,WAAW,GAAG;AACzD,aAAO;AAAA,IACT;AACA,UAAM,SAAS,OAAO;AACtB,QAAI,OAAO,YAAY,SAAS;AAC9B,aAAO,eAAe;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,SAAS,kBAAkB,GAAG;AACjD,WAAK,iBAAiB;AACtB;AAAA,IACF;AACA,QAAI,OAAO,UAAU,SAAS,kBAAkB,GAAG;AACjD,WAAK,iBAAiB;AACtB;AAAA,IACF;AACA,QAAI,OAAO,UAAU,SAAS,eAAe,GAAG;AAC9C;AAAA,IACF;AACA,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,MAAM;AAAA,IACb;AACA,QAAI,KAAK,YAAY;AACnB,WAAK,KAAK;AAAA,IACZ,OAAO;AACL,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,QAAQ;AACf,WAAK,MAAM;AAAA,IACb,OAAO;AACL,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,UAAU;AACjB,WAAK,UAAU,cAAc,IAAI;AACjC,WAAK,eAAe;AAAA,IACtB;AACA,SAAK,aAAa;AAClB,SAAK,MAAM;AACX,SAAK,WAAW,KAAK;AACrB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,aAAa;AACX,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,SAAK,UAAU,cAAc;AAC7B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,UAAU,cAAc;AAC7B,SAAK,kBAAkB,KAAK;AAC5B,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,YAAY;AACjB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,SAAS;AACP,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,KAAK;AAAA,IACZ,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,OAAO;AACL,QAAI,KAAK,YAAY,KAAK,UAAU,KAAK,aAAa;AACpD;AAAA,IACF;AACA,QAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,UAAU,KAAK,UAAU,iBAAiB;AACxE;AAAA,IACF;AACA,SAAK,SAAS;AACd,SAAK,UAAU,sBAAsB,KAAK,SAAS;AACnD,SAAK,UAAU,KAAK;AACpB,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,MAAM;AAAA,IACb;AACA,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,QAAQ;AACN,QAAI,CAAC,KAAK,UAAU,KAAK,aAAa;AACpC;AAAA,IACF;AACA,SAAK,SAAS;AACd,SAAK,eAAe;AACpB,QAAI,CAAC,KAAK,qBAAqB;AAC7B,WAAK,aAAa;AAAA,IACpB,OAAO;AACL,WAAK,UAAU,mBAAmB;AAAA,IACpC;AACA,SAAK,UAAU,WAAW;AAC1B,SAAK,WAAW;AAChB,SAAK,WAAW,KAAK;AACrB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,WAAW,MAAM;AACf,QAAI,CAAC,QAAQ,KAAK,YAAY,KAAK,UAAU;AAC3C;AAAA,IACF;AACA,QAAI,KAAK,mBAAmB,KAAK,UAAU;AACzC,WAAK,SAAS,IAAI;AAAA,IACpB,OAAO;AACL,WAAK,OAAO,IAAI;AAAA,IAClB;AACA,QAAI,KAAK,qBAAqB;AAC5B,WAAK,wBAAwB;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,OAAO,MAAM;AACX,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,UAAU,OAAO,IAAI;AAC1B,UAAI,KAAK,oBAAoB,CAAC,KAAK,qBAAqB;AACtD,aAAK,aAAa;AAAA,MACpB;AACA,WAAK,eAAe;AACpB,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,KAAK,KAAK,KAAK;AAAA,MAC/B;AAAA,IACF;AACA,QAAI,KAAK,iBAAiB,KAAK,UAAU,iBAAiB;AACxD,WAAK,MAAM;AAAA,IACb;AACA,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,QAAQ;AACN,SAAK,YAAY,cAAc,MAAM;AAAA,EACvC;AAAA,EACA,OAAO;AACL,SAAK,YAAY,cAAc,KAAK;AAAA,EACtC;AAAA,EACA,SAAS,MAAM;AACb,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,SAAK,UAAU,SAAS,IAAI;AAC5B,SAAK,MAAM;AACX,SAAK,eAAe;AACpB,SAAK,YAAY,KAAK,KAAK,KAAK;AAChC,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,YAAY;AACV,QAAI;AACJ,QAAI,WAAW,KAAK,MAAM,GAAG;AAC3B,YAAM,KAAK,OAAO,KAAK,UAAU;AAAA,IACnC,OAAO;AACL,YAAM,KAAK,aAAa,KAAK,aAAa;AAAA,QACxC,CAAC,KAAK,SAAS,GAAG,KAAK;AAAA,MACzB;AAAA,IACF;AACA,UAAM,YAAY,UAAQ,KAAK,gBAAgB,CAAC,KAAK,SAAS,KAAK,UAAU,QAAQ,MAAM,IAAI,IAAI,KAAK,UAAU,QAAQ,IAAI;AAC9H,QAAI,UAAU,GAAG,GAAG;AAClB,UAAI,KAAK,UAAQ,KAAK,OAAO,UAAU,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM;AAAA,MAAC,CAAC;AAAA,IAC/D,WAAW,KAAK;AACd,WAAK,OAAO,UAAU,GAAG,CAAC;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,YAAY;AACV,WAAO,KAAK,cAAc,KAAK,YAAY,KAAK,eAAe,CAAC,KAAK;AAAA,EACvE;AAAA,EACA,eAAe;AACb,SAAK,KAAK;AACV,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,cAAc,MAAM;AAAA,IACvC;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,UAAM,QAAQ,KAAK,UAAU,cAAc,WAAW;AACtD,YAAQ,SAAS,CAAC,KAAK,gBAAgB,CAAC,KAAK,WAAW,SAAS,KAAK,gBAAgB,KAAK,cAAc,CAAC,KAAK,YAAY,CAAC,KAAK;AAAA,EACnI;AAAA,EACA,mBAAmB;AACjB,UAAM,QAAQ,KAAK,UAAU,cAAc,WAAW;AACtD,WAAO,SAAS,KAAK,gBAAgB,CAAC,KAAK,cAAc,CAAC,KAAK;AAAA,EACjE;AAAA,EACA,qBAAqB;AACnB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,iBAAiB,MAAM;AACrB,SAAK,eAAe;AACpB,QAAI,KAAK,sBAAsB;AAC7B;AAAA,IACF;AACA,SAAK,OAAO,IAAI;AAAA,EAClB;AAAA,EACA,OAAO,MAAM;AACX,QAAI,KAAK,gBAAgB,CAAC,KAAK,sBAAsB;AACnD;AAAA,IACF;AACA,SAAK,aAAa;AAClB,QAAI,KAAK,iBAAiB,KAAK,cAAc,KAAK,kBAAkB,IAAI;AACtE,WAAK,UAAU,KAAK,IAAI;AAAA,IAC1B;AACA,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,UAAU,OAAO,KAAK,UAAU;AACrC,UAAI,KAAK,QAAQ;AACf,aAAK,UAAU,sBAAsB,KAAK,SAAS;AAAA,MACrD;AAAA,IACF;AACA,SAAK,YAAY,KAAK;AAAA,MACpB;AAAA,MACA,OAAO,KAAK,UAAU,cAAc,IAAI,OAAK,EAAE,KAAK;AAAA,IACtD,CAAC;AACD,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,aAAa,QAAQ;AACnB,QAAI,KAAK,SAAS;AAChB;AAAA,IACF;AACA,QAAI,KAAK,qBAAqB;AAC5B,WAAK,wBAAwB;AAAA,IAC/B;AACA,SAAK,QAAQ,UAAU,IAAI,mBAAmB;AAC9C,SAAK,WAAW,KAAK,MAAM;AAC3B,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,YAAY,QAAQ;AAClB,SAAK,QAAQ,UAAU,OAAO,mBAAmB;AACjD,SAAK,UAAU,KAAK,MAAM;AAC1B,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,UAAU;AAClC,WAAK,WAAW;AAAA,IAClB;AACA,QAAI,KAAK,qBAAqB;AAC5B,WAAK,wBAAwB;AAAA,IAC/B;AACA,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,YAAY,MAAM;AAChB,QAAI,KAAK,UAAU;AACjB;AAAA,IACF;AACA,SAAK,UAAU,SAAS,IAAI;AAAA,EAC9B;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,IAAI,WAAW;AACvB,WAAK,IAAI,cAAc;AAAA,IACzB;AAAA,EACF;AAAA,EACA,0BAA0B;AACxB,UAAM,WAAW,KAAK,gBAAgB,CAAC;AACvC,SAAK,aAAa,UAAU,SAAS;AAAA,EACvC;AAAA,EACA,UAAU,OAAO;AACf,UAAM,YAAY,MAAM,CAAC;AACzB,SAAK,YAAY,KAAK,aAAa,KAAK;AACxC,SAAK,aAAa,UAAU,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI,KAAK,cAAc,KAAK,cAAc,KAAK;AAC3G,SAAK,UAAU,SAAS,KAAK;AAC7B,QAAI,MAAM,SAAS,KAAK,KAAK,UAAU;AACrC,WAAK,UAAU,iBAAiB;AAAA,IAClC;AACA,QAAI,KAAK,UAAU,UAAU,KAAK,UAAU,KAAK,CAAC,KAAK,cAAc;AACnE,WAAK,UAAU,OAAO,KAAK,UAAU;AAAA,IACvC;AACA,QAAI,KAAK,gBAAgB,KAAK,QAAQ;AACpC,WAAK,UAAU,sBAAsB,KAAK,SAAS;AAAA,IACrD;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,UAAM,eAAe,aAAW;AAC9B,WAAK,QAAQ,QAAQ,IAAI,aAAW;AAAA,QAClC,gBAAgB,OAAO;AAAA,QACvB,gBAAgB,OAAO,WAAW,cAAc;AAAA,QAChD,UAAU,OAAO;AAAA,MACnB,EAAE;AACF,WAAK,UAAU,SAAS,KAAK,KAAK;AAClC,UAAI,KAAK,UAAU;AACjB,aAAK,UAAU,iBAAiB;AAAA,MAClC;AACA,WAAK,cAAc;AAAA,IACrB;AACA,UAAM,qBAAqB,MAAM;AAC/B,YAAM,qBAAqB,MAAM,KAAK,UAAU,SAAS,KAAK,SAAS;AACvE,YAAM,GAAG,KAAK,UAAU,IAAI,YAAU,OAAO,YAAY,CAAC,EAAE,KAAK,UAAU,kBAAkB,CAAC,EAAE,UAAU,YAAU;AAClH,cAAM,OAAO,KAAK,UAAU,SAAS,OAAO,KAAK;AACjD,aAAK,WAAW,OAAO;AACvB,aAAK,QAAQ,OAAO,SAAS,KAAK;AAClC,aAAK,IAAI,cAAc;AAAA,MACzB,CAAC;AAAA,IACH;AACA,SAAK,UAAU,QAAQ,KAAK,UAAU,KAAK,SAAS,GAAG,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU,aAAW;AACrG,WAAK,YAAY,KAAK;AACtB,mBAAa,OAAO;AACpB,yBAAmB;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,OAAO;AACxB,QAAI,CAAC,UAAU,KAAK,KAAK,KAAK,YAAY,UAAU,MAAM,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW,GAAG;AACpG,aAAO;AAAA,IACT;AACA,UAAM,kBAAkB,UAAQ;AAC9B,UAAI,CAAC,UAAU,KAAK,WAAW,KAAK,SAAS,IAAI,KAAK,KAAK,WAAW;AACpE,aAAK,SAAS,KAAK,kBAAkB,KAAK,UAAU,IAAI,CAAC,6EAA6E;AACtI,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,QAAI,KAAK,UAAU;AACjB,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,aAAK,SAAS,KAAK,0CAA0C;AAC7D,eAAO;AAAA,MACT;AACA,aAAO,MAAM,MAAM,UAAQ,gBAAgB,IAAI,CAAC;AAAA,IAClD,OAAO;AACL,aAAO,gBAAgB,KAAK;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,kBAAkB,SAAS;AACzB,QAAI,CAAC,KAAK,mBAAmB,OAAO,GAAG;AACrC;AAAA,IACF;AACA,UAAM,SAAS,SAAO;AACpB,UAAI,OAAO,KAAK,UAAU,SAAS,GAAG;AACtC,UAAI,MAAM;AACR,aAAK,UAAU,OAAO,IAAI;AAAA,MAC5B,OAAO;AACL,cAAM,cAAc,SAAS,GAAG;AAChC,cAAM,cAAc,CAAC,eAAe,CAAC,KAAK;AAC1C,YAAI,eAAe,aAAa;AAC9B,eAAK,UAAU,OAAO,KAAK,UAAU,QAAQ,KAAK,IAAI,CAAC;AAAA,QACzD,WAAW,KAAK,WAAW;AACzB,iBAAO;AAAA,YACL,CAAC,KAAK,SAAS,GAAG;AAAA,YAClB,CAAC,KAAK,SAAS,GAAG;AAAA,UACpB;AACA,eAAK,UAAU,OAAO,KAAK,UAAU,QAAQ,MAAM,IAAI,CAAC;AAAA,QAC1D;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,UAAU;AACjB,cAAQ,QAAQ,UAAQ,OAAO,IAAI,CAAC;AAAA,IACtC,OAAO;AACL,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,YAAY;AACnB;AAAA,IACF;AACA,SAAK,WAAW,KAAK,UAAU,KAAK,SAAS,GAAG,IAAI,YAAU,KAAK,aAAa,KAAK,MAAM,CAAC,GAAG,aAAa,GAAG,GAAG,OAAO,MAAM,KAAK,aAAa,SAAS,CAAC,GAAG,IAAI,MAAM,KAAK,aAAa,KAAK,EAAE,CAAC,CAAC,EAAE,UAAU,UAAQ;AACrN,YAAM,OAAO,KAAK,UAAU,YAAY,IAAI;AAC5C,UAAI,MAAM;AACR,YAAI,KAAK,QAAQ;AACf,eAAK,UAAU,SAAS,IAAI;AAC5B,eAAK,gBAAgB;AACrB,eAAK,IAAI,aAAa;AAAA,QACxB,OAAO;AACL,eAAK,OAAO,IAAI;AAAA,QAClB;AAAA,MACF;AACA,WAAK,eAAe,CAAC;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AACpB,UAAMC,SAAQ,KAAK,YAAY;AAC/B,UAAM,aAAa;AAAA,MACjB,MAAM;AAAA,MACN,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,iBAAiB,KAAK;AAAA,OACnB,KAAK;AAEV,eAAW,OAAO,OAAO,KAAK,UAAU,GAAG;AACzC,MAAAA,OAAM,aAAa,KAAK,WAAW,GAAG,CAAC;AAAA,IACzC;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,UAAM,QAAQ,CAAC;AACf,eAAW,QAAQ,KAAK,eAAe;AACrC,UAAI,KAAK,WAAW;AAClB,YAAI,QAAQ;AACZ,YAAI,KAAK,UAAU;AACjB,gBAAM,WAAW,KAAK,aAAa,KAAK,YAAY,KAAK;AACzD,kBAAQ,KAAK,MAAM,YAAY,KAAK,OAAO;AAAA,QAC7C,OAAO;AACL,kBAAQ,KAAK,UAAU,cAAc,KAAK,OAAO,KAAK,SAAS;AAAA,QACjE;AACA,cAAM,KAAK,KAAK;AAAA,MAClB,OAAO;AACL,cAAM,KAAK,KAAK,KAAK;AAAA,MACvB;AAAA,IACF;AACA,UAAM,WAAW,KAAK,cAAc,IAAI,OAAK,EAAE,KAAK;AACpD,QAAI,KAAK,UAAU;AACjB,WAAK,UAAU,KAAK;AACpB,WAAK,YAAY,KAAK,QAAQ;AAAA,IAChC,OAAO;AACL,WAAK,UAAU,UAAU,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI;AACpD,WAAK,YAAY,KAAK,SAAS,CAAC,CAAC;AAAA,IACnC;AACA,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,eAAe;AACb,QAAI,CAAC,KAAK,YAAY;AACpB;AAAA,IACF;AACA,SAAK,cAAc,IAAI;AACvB,SAAK,UAAU,mBAAmB;AAAA,EACpC;AAAA,EACA,cAAc,YAAY;AACxB,SAAK,aAAa;AAClB,QAAI,KAAK,cAAc;AACrB,WAAK,UAAU,KAAK,UAAU;AAAA,IAChC;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,eAAe;AACvC;AAAA,IACF;AACA,SAAK,cAAc,SAAS,KAAK,UAAU,UAAU;AAAA,EACvD;AAAA,EACA,eAAe;AACb,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,eAAe;AACvC;AAAA,IACF;AACA,SAAK,cAAc,YAAY;AAAA,EACjC;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,UAAU,KAAK,mBAAmB,KAAK,UAAU;AAExD,WAAK,IAAI,cAAc;AACvB,WAAK,cAAc,eAAe;AAAA,IACpC;AAAA,EACF;AAAA,EACA,WAAW,QAAQ;AACjB,QAAI,KAAK,WAAW,OAAO;AACzB,UAAI,KAAK,UAAU,KAAK,CAAC,OAAO,YAAY,KAAK,sBAAsB,GAAG;AACxE,aAAK,aAAa;AAClB,eAAO,eAAe;AAAA,MACxB,WAAW,CAAC,KAAK,QAAQ;AACvB;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,aAAa;AACpB,UAAI,KAAK,UAAU,YAAY;AAC7B,aAAK,WAAW,KAAK,UAAU,UAAU;AACzC,eAAO,eAAe;AAAA,MACxB,WAAW,KAAK,YAAY;AAC1B,aAAK,UAAU;AACf,eAAO,eAAe;AAAA,MACxB,OAAO;AACL,aAAK,MAAM;AAAA,MACb;AAAA,IACF,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,aAAa,QAAQ;AACnB,QAAI,KAAK,UAAU,KAAK,aAAa;AACnC,UAAI,KAAK,UAAU,YAAY;AAC7B,aAAK,WAAW,KAAK,UAAU,UAAU;AAAA,MAC3C,WAAW,KAAK,YAAY;AAC1B,aAAK,UAAU;AAAA,MACjB;AAAA,IACF,WAAW,KAAK,aAAa;AAC3B,WAAK,KAAK;AAAA,IACZ,OAAO;AACL;AAAA,IACF;AACA,WAAO,eAAe;AAAA,EACxB;AAAA,EACA,aAAa,QAAQ;AACnB,QAAI,KAAK,UAAU,KAAK,aAAa;AACnC;AAAA,IACF;AACA,SAAK,KAAK;AACV,WAAO,eAAe;AAAA,EACxB;AAAA,EACA,iBAAiB,QAAQ;AACvB,QAAI,KAAK,eAAe,CAAE,GAAG;AAC3B,WAAK,UAAU,WAAW;AAC1B,WAAK,aAAa;AAAA,IACpB,OAAO;AACL,WAAK,UAAU,aAAa;AAC5B,WAAK,gBAAgB;AAAA,IACvB;AACA,SAAK,KAAK;AACV,WAAO,eAAe;AAAA,EACxB;AAAA,EACA,eAAe,QAAQ;AACrB,QAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,IACF;AACA,QAAI,KAAK,eAAe,EAAE,GAAG;AAC3B,WAAK,UAAU,WAAW;AAC1B,WAAK,aAAa;AAAA,IACpB,OAAO;AACL,WAAK,UAAU,iBAAiB;AAChC,WAAK,gBAAgB;AAAA,IACvB;AACA,WAAO,eAAe;AAAA,EACxB;AAAA,EACA,eAAe,UAAU;AACvB,UAAM,YAAY,KAAK,UAAU,cAAc;AAC/C,WAAO,KAAK,UAAU,KAAK,cAAc,KAAK,UAAU,eAAe,YAAY,KAAK,cAAc,KAAK,UAAU,cAAc;AAAA,EACrI;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,cAAc,CAAC,KAAK,aAAa,CAAC,KAAK,oBAAoB,CAAC,KAAK,UAAU;AAClF;AAAA,IACF;AACA,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,KAAK,UAAU,gBAAgB;AAAA,IAC/C,OAAO;AACL,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,mBAAmB,QAAQ;AACzB,SAAK,cAAc,KAAK,eAAe,OAAO;AAC9C,SAAK,mBAAmB,KAAK,oBAAoB,OAAO;AACxD,SAAK,eAAe,KAAK,gBAAgB,OAAO;AAChD,SAAK,mBAAmB,KAAK,oBAAoB,OAAO;AACxD,SAAK,aAAa,KAAK,cAAc,OAAO;AAC5C,SAAK,cAAc,KAAK,eAAe,OAAO;AAC9C,SAAK,eAAe,KAAK,gBAAgB,OAAO;AAChD,SAAK,gBAAgB,KAAK,iBAAiB,MAAM;AACjD,SAAK,cAAc,UAAU,KAAK,WAAW,IAAI,KAAK,cAAc,OAAO;AAC3E,SAAK,WAAW,KAAK,YAAY,OAAO;AACxC,SAAK,YAAY,KAAK,aAAa,OAAO;AAC1C,SAAK,YAAY,KAAK,aAAa,OAAO;AAC1C,SAAK,aAAa,KAAK,cAAc,OAAO;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,QAAQ;AACvB,WAAO,UAAU,KAAK,aAAa,IAAI,KAAK,gBAAgB,KAAK,wBAAwB,MAAM;AAAA,EACjG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,wBAAwB,QAAQ;AAC9B,WAAO,UAAU,OAAO,oBAAoB,IAAI,CAAC,OAAO,uBAAuB;AAAA,EACjF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAsB,kBAAkB,OAAO,GAAM,kBAAkB,WAAW,GAAM,kBAAkB,cAAc,GAAM,kBAAkB,yBAAyB,CAAC,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,cAAc,CAAC;AAAA,IACrU;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,MACzB,gBAAgB,SAAS,iCAAiC,IAAI,KAAK,UAAU;AAC3E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,2BAA2B,GAAG,WAAW;AACrE,UAAG,eAAe,UAAU,6BAA6B,GAAG,WAAW;AACvE,UAAG,eAAe,UAAU,0BAA0B,GAAG,WAAW;AACpE,UAAG,eAAe,UAAU,+BAA+B,GAAG,WAAW;AACzE,UAAG,eAAe,UAAU,2BAA2B,GAAG,WAAW;AACrE,UAAG,eAAe,UAAU,2BAA2B,GAAG,WAAW;AACrE,UAAG,eAAe,UAAU,6BAA6B,GAAG,WAAW;AACvE,UAAG,eAAe,UAAU,gCAAgC,GAAG,WAAW;AAC1E,UAAG,eAAe,UAAU,iCAAiC,GAAG,WAAW;AAC3E,UAAG,eAAe,UAAU,gCAAgC,GAAG,WAAW;AAC1E,UAAG,eAAe,UAAU,wBAAwB,GAAG,WAAW;AAClE,UAAG,eAAe,UAAU,mCAAmC,GAAG,WAAW;AAC7E,UAAG,eAAe,UAAU,gCAAgC,GAAG,WAAW;AAC1E,UAAG,eAAe,UAAU,mBAAmB,CAAC;AAAA,QAClD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AACzE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB,GAAG;AAC1E,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,uBAAuB,GAAG;AAC3E,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB,GAAG;AAC1E,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAClE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,yBAAyB,GAAG;AAC7E,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB,GAAG;AAC1E,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,QAC/D;AAAA,MACF;AAAA,MACA,WAAW,SAAS,wBAAwB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,0BAA0B,CAAC;AAC1C,UAAG,YAAY,KAAK,CAAC;AACrB,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAClE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,QACpE;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,SAAS,6CAA6C,QAAQ;AACrF,mBAAO,IAAI,cAAc,MAAM;AAAA,UACjC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,uBAAuB,IAAI,SAAS,EAAE,sBAAsB,IAAI,QAAQ,EAAE,sBAAsB,IAAI,MAAM,EAAE,wBAAwB,IAAI,UAAU,EAAE,uBAAuB,IAAI,SAAS,EAAE,oBAAoB,IAAI,MAAM,EAAE,aAAa,IAAI,eAAe,EAAE,sBAAsB,IAAI,QAAQ,EAAE,oBAAoB,IAAI,MAAM,EAAE,sBAAsB,IAAI,QAAQ;AAAA,QACnX;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,mBAAmB;AAAA,QACnB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,QACzD,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,kBAAkB;AAAA,QAClB,2BAA2B;AAAA,QAC3B,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,SAAS,CAAC,GAAG,WAAW,WAAW,gBAAgB;AAAA,QACnD,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,QACrE,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,QAClE,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,QAC/D,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,QAC/D,kBAAkB,CAAC,GAAG,oBAAoB,oBAAoB,eAAe;AAAA,QAC7E,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,cAAc,CAAC,GAAG,gBAAgB,gBAAgB,eAAe;AAAA,QACjE,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,QACrE,iBAAiB,CAAC,GAAG,mBAAmB,mBAAmB,gBAAgB;AAAA,QAC3E,wBAAwB,CAAC,GAAG,0BAA0B,0BAA0B,gBAAgB;AAAA,QAChG,UAAU;AAAA,QACV,WAAW;AAAA,QACX,kBAAkB,CAAC,GAAG,oBAAoB,oBAAoB,gBAAgB;AAAA,QAC9E,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,QACrD,uBAAuB,CAAC,GAAG,uBAAuB;AAAA,QAClD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,QACtD,sBAAsB,CAAC,GAAG,wBAAwB,wBAAwB,gBAAgB;AAAA,QAC1F,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,eAAe;AAAA,QACpE,oBAAoB,CAAC,GAAG,sBAAsB,sBAAsB,gBAAgB;AAAA,QACpF,SAAS;AAAA,QACT,WAAW;AAAA,QACX,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,QACtD,QAAQ;AAAA,QACR,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,QAC5D,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,QACzD,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,iBAAiB;AAAA,QACjB,WAAW;AAAA,MACb;AAAA,MACA,SAAS;AAAA,QACP,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,aAAa;AAAA,MACf;AAAA,MACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,kBAAiB;AAAA,QAC/C,OAAO;AAAA,MACT,GAAG,sBAAsB,CAAC,GAAM,oBAAoB;AAAA,MACpD,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,eAAe,EAAE,GAAG,CAAC,8BAA8B,EAAE,GAAG,CAAC,wBAAwB,EAAE,GAAG,CAAC,iCAAiC,EAAE,GAAG,CAAC,eAAe,EAAE,GAAG,CAAC,yBAAyB,EAAE,GAAG,CAAC,sBAAsB,EAAE,GAAG,CAAC,2BAA2B,EAAE,GAAG,CAAC,+BAA+B,EAAE,GAAG,CAAC,8BAA8B,EAAE,GAAG,CAAC,GAAG,uBAAuB,GAAG,WAAW,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,qBAAqB,QAAQ,QAAQ,YAAY,GAAG,QAAQ,UAAU,kBAAkB,oBAAoB,SAAS,SAAS,YAAY,YAAY,OAAO,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,qBAAqB,GAAG,iBAAiB,gBAAgB,YAAY,YAAY,kBAAkB,kBAAkB,eAAe,SAAS,cAAc,sBAAsB,WAAW,MAAM,mBAAmB,GAAG,CAAC,eAAe,QAAQ,aAAa,UAAU,QAAQ,UAAU,GAAG,oBAAoB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,YAAY,GAAG,mBAAmB,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,eAAe,QAAQ,GAAG,iBAAiB,QAAQ,GAAG,OAAO,GAAG,CAAC,GAAG,kBAAkB,GAAG,eAAe,QAAQ,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,QAAQ,UAAU,YAAY,KAAK,GAAG,oBAAoB,GAAG,OAAO,GAAG,CAAC,eAAe,QAAQ,GAAG,UAAU,GAAG,CAAC,GAAG,qBAAqB,GAAG,UAAU,UAAU,eAAe,gBAAgB,iBAAiB,gBAAgB,YAAY,YAAY,kBAAkB,kBAAkB,eAAe,SAAS,cAAc,WAAW,MAAM,mBAAmB,GAAG,CAAC,GAAG,aAAa,GAAG,sBAAsB,sBAAsB,eAAe,aAAa,mBAAmB,kBAAkB,GAAG,CAAC,QAAQ,UAAU,GAAG,aAAa,GAAG,kBAAkB,GAAG,CAAC,GAAG,aAAa,GAAG,SAAS,WAAW,GAAG,CAAC,GAAG,mBAAmB,GAAG,eAAe,QAAQ,GAAG,CAAC,QAAQ,UAAU,GAAG,aAAa,GAAG,aAAa,OAAO,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,aAAa,oBAAoB,CAAC;AAAA,MACt8D,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,UAAG,WAAW,aAAa,SAAS,oDAAoD,QAAQ;AAC9F,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,gBAAgB,MAAM,CAAC;AAAA,UACnD,CAAC;AACD,UAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,UAAG,WAAW,GAAG,0CAA0C,GAAG,CAAC,EAAE,GAAG,0CAA0C,GAAG,CAAC,EAAE,GAAG,0CAA0C,GAAG,GAAG,MAAM,EAAE;AAC/K,UAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC;AACjD,UAAG,WAAW,QAAQ,SAAS,iDAAiD,QAAQ;AACtF,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,YAAY,MAAM,CAAC;AAAA,UAC/C,CAAC,EAAE,UAAU,SAAS,mDAAmD,QAAQ;AAC/E,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,OAAO,gBAAgB,CAAC;AAAA,UAChD,CAAC,EAAE,kBAAkB,SAAS,6DAA6D;AACzF,YAAG,cAAc,GAAG;AACpB,kBAAM,iBAAoB,YAAY,CAAC;AACvC,mBAAU,YAAY,IAAI,iBAAiB,eAAe,KAAK,CAAC;AAAA,UAClE,CAAC,EAAE,oBAAoB,SAAS,+DAA+D;AAC7F,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,mBAAmB,CAAC;AAAA,UAChD,CAAC,EAAE,SAAS,SAAS,kDAAkD,QAAQ;AAC7E,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,aAAa,MAAM,CAAC;AAAA,UAChD,CAAC,EAAE,SAAS,SAAS,oDAAoD;AACvE,YAAG,cAAc,GAAG;AACpB,kBAAM,iBAAoB,YAAY,CAAC;AACvC,mBAAU,YAAY,IAAI,OAAO,eAAe,KAAK,CAAC;AAAA,UACxD,CAAC;AACD,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,WAAW,GAAG,0CAA0C,GAAG,CAAC,EAAE,GAAG,0CAA0C,GAAG,CAAC;AAClH,UAAG,eAAe,IAAI,QAAQ,EAAE;AAChC,UAAG,UAAU,IAAI,QAAQ,EAAE;AAC3B,UAAG,aAAa,EAAE;AAClB,UAAG,WAAW,IAAI,2CAA2C,GAAG,IAAI,qBAAqB,EAAE;AAC3F,UAAG,eAAe,IAAI,OAAO,EAAE;AAC/B,UAAG,WAAW,IAAI,2CAA2C,GAAG,CAAC;AACjE,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,YAAY,yBAAyB,IAAI,eAAe,SAAS,EAAE,gBAAgB,IAAI,QAAQ;AAClG,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,cAAc,WAAW,KAAK,CAAC,IAAI,cAAc,IAAI,mBAAmB,IAAI,EAAE;AACnG,UAAG,UAAU;AACb,UAAG,eAAe,CAAC,IAAI,sBAAsB,CAAC,IAAI,aAAa,IAAI,cAAc,SAAS,IAAI,IAAI,EAAE;AACpG,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,YAAY,IAAI,sBAAsB,IAAI,eAAe,SAAS,IAAI,IAAI,EAAE;AACjG,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,YAAY,IAAI,QAAQ,EAAE,YAAY,CAAC,IAAI,cAAc,IAAI,UAAU,gBAAgB,EAAE,UAAU,UAAU,IAAI,gBAAgB,QAAQ,YAAY,SAAY,UAAU,EAAE;AAC3L,UAAG,YAAY,yBAAyB,IAAI,SAAS,IAAI,aAAa,OAAO,OAAO,IAAI,UAAU,cAAc,OAAO,OAAO,IAAI,UAAU,WAAW,SAAS,IAAI,EAAE,iBAAiB,IAAI,SAAS,IAAI,aAAa,IAAI,EAAE,iBAAiB,IAAI,MAAM,EAAE,cAAc,IAAI,SAAS,EAAE,MAAM,IAAI,UAAU,EAAE,YAAY,IAAI,QAAQ;AACnU,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,UAAU,IAAI,EAAE;AACrC,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,UAAU,IAAI,IAAI,EAAE;AACzC,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,SAAS,KAAK,EAAE;AACrC,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,UAAU,IAAI,iBAAiB,IAAI,KAAK,EAAE;AAAA,QACjE;AAAA,MACF;AAAA,MACA,cAAc,CAAC,kBAAkB,sBAAsB,0BAA0B,OAAO;AAAA,MACxF,QAAQ,CAAC,svKAA0vK;AAAA,MACnwK,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,iBAAiB;AAAA,QAC/C,OAAO;AAAA,MACT,GAAG,sBAAsB;AAAA,MACzB,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,kBAAkB,sBAAsB,0BAA0B,OAAO;AAAA,MACnF,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,svKAA0vK;AAAA,IACrwK,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,KAAK;AAAA,IACd,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,QAChC,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,QAClC,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,QAC/B,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,QACpC,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,QAChC,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,QAChC,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,QAClC,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,QACrC,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,iCAAiC;AAAA,QACtC,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,QACrC,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,QAC7B,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC,mCAAmC;AAAA,QACxC,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,QACrC,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,MAAM,wBAAwB,CAAC;AAAA,IACnD,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,QACpB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,QACxB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,0BAA0B,mBAAmB,mBAAmB,6BAA6B,2BAA2B,0BAA0B,+BAA+B,2BAA2B,2BAA2B,gCAAgC,gCAAgC,6BAA6B,iCAAiC,gCAAgC,wBAAwB,mCAAmC,oBAAoB;AAAA,MAC9d,SAAS,CAAC,mBAAmB,mBAAmB,6BAA6B,2BAA2B,0BAA0B,+BAA+B,2BAA2B,2BAA2B,gCAAgC,6BAA6B,iCAAiC,gCAAgC,wBAAwB,mCAAmC,8BAA8B;AAAA,IAChb,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,0BAA0B,mBAAmB,mBAAmB,6BAA6B,2BAA2B,0BAA0B,+BAA+B,2BAA2B,2BAA2B,gCAAgC,gCAAgC,6BAA6B,iCAAiC,gCAAgC,wBAAwB,mCAAmC,oBAAoB;AAAA,MAC9d,SAAS,CAAC,mBAAmB,mBAAmB,6BAA6B,2BAA2B,0BAA0B,+BAA+B,2BAA2B,2BAA2B,gCAAgC,6BAA6B,iCAAiC,gCAAgC,wBAAwB,mCAAmC,8BAA8B;AAAA,MAC9a,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["KeyCode", "input"]}