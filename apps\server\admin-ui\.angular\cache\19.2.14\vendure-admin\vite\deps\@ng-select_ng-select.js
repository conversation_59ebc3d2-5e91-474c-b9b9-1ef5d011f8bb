import {
  ConsoleService,
  DefaultSelectionModel,
  DefaultSelectionModelFactory,
  NgClearButtonTemplateDirective,
  NgDropdownPanelComponent,
  NgDropdownPanelService,
  NgFooterTemplateDirective,
  NgHeaderTemplateDirective,
  NgItemLabelDirective,
  NgLabelTemplateDirective,
  NgLoadingSpinnerTemplateDirective,
  NgLoadingTextTemplateDirective,
  NgMultiLabelTemplateDirective,
  NgNotFoundTemplateDirective,
  NgOptgroupTemplateDirective,
  NgOptionComponent,
  NgOptionTemplateDirective,
  NgPlaceholderTemplateDirective,
  NgSelectComponent,
  NgSelectConfig,
  NgSelectModule,
  NgTagTemplateDirective,
  NgTypeToSearchTemplateDirective,
  SELECTION_MODEL_FACTORY
} from "./chunk-A47MZO3O.js";
import "./chunk-DPOUSLOI.js";
import "./chunk-4PUVEEMI.js";
import "./chunk-LUYNUHYL.js";
import "./chunk-TXDUYLVM.js";
export {
  ConsoleService,
  DefaultSelectionModel,
  DefaultSelectionModelFactory,
  NgClearButtonTemplateDirective,
  NgDropdownPanelComponent,
  NgDropdownPanelService,
  NgFooterTemplateDirective,
  NgHeaderTemplateDirective,
  NgItemLabelDirective,
  NgLabelTemplateDirective,
  NgLoadingSpinnerTemplateDirective,
  NgLoadingTextTemplateDirective,
  NgMultiLabelTemplateDirective,
  NgNotFoundTemplateDirective,
  NgOptgroupTemplateDirective,
  NgOptionComponent,
  NgOptionTemplateDirective,
  NgPlaceholderTemplateDirective,
  NgSelectComponent,
  NgSelectConfig,
  NgSelectModule,
  NgTagTemplateDirective,
  NgTypeToSearchTemplateDirective,
  SELECTION_MODEL_FACTORY
};
