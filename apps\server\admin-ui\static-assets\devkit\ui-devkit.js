!function(t,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((t="undefined"!=typeof globalThis?globalThis:t||self).VendureUiClient={})}(this,(function(t){"use strict";var r=function(t,n){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])},r(t,n)};function n(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function e(){this.constructor=t}r(t,n),t.prototype=null===n?Object.create(n):(e.prototype=n.prototype,new e)}function e(t){var r="function"==typeof Symbol&&Symbol.iterator,n=r&&t[r],e=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&e>=t.length&&(t=void 0),{value:t&&t[e++],done:!t}}};throw new TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")}function o(t,r){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var e,o,i=n.call(t),u=[];try{for(;(void 0===r||r-- >0)&&!(e=i.next()).done;)u.push(e.value)}catch(t){o={error:t}}finally{try{e&&!e.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return u}function i(t,r,n){if(n||2===arguments.length)for(var e,o=0,i=r.length;o<i;o++)!e&&o in r||(e||(e=Array.prototype.slice.call(r,0,o)),e[o]=r[o]);return t.concat(e||Array.prototype.slice.call(r))}function u(t){return"function"==typeof t}"function"==typeof SuppressedError&&SuppressedError;var s,c=((s=function(t){return function(r){t(this),this.message=r?r.length+" errors occurred during unsubscription:\n"+r.map((function(t,r){return r+1+") "+t.toString()})).join("\n  "):"",this.name="UnsubscriptionError",this.errors=r}}((function(t){Error.call(t),t.stack=(new Error).stack}))).prototype=Object.create(Error.prototype),s.prototype.constructor=s,s);function a(t,r){if(t){var n=t.indexOf(r);0<=n&&t.splice(n,1)}}var l=function(){function t(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}var r;return t.prototype.unsubscribe=function(){var t,r,n,s,a;if(!this.closed){this.closed=!0;var l=this._parentage;if(l)if(this._parentage=null,Array.isArray(l))try{for(var f=e(l),h=f.next();!h.done;h=f.next()){h.value.remove(this)}}catch(r){t={error:r}}finally{try{h&&!h.done&&(r=f.return)&&r.call(f)}finally{if(t)throw t.error}}else l.remove(this);var y=this.initialTeardown;if(u(y))try{y()}catch(t){a=t instanceof c?t.errors:[t]}var d=this._finalizers;if(d){this._finalizers=null;try{for(var v=e(d),b=v.next();!b.done;b=v.next()){var m=b.value;try{p(m)}catch(t){a=null!=a?a:[],t instanceof c?a=i(i([],o(a)),o(t.errors)):a.push(t)}}}catch(t){n={error:t}}finally{try{b&&!b.done&&(s=v.return)&&s.call(v)}finally{if(n)throw n.error}}}if(a)throw new c(a)}},t.prototype.add=function(r){var n;if(r&&r!==this)if(this.closed)p(r);else{if(r instanceof t){if(r.closed||r._hasParent(this))return;r._addParent(this)}(this._finalizers=null!==(n=this._finalizers)&&void 0!==n?n:[]).push(r)}},t.prototype._hasParent=function(t){var r=this._parentage;return r===t||Array.isArray(r)&&r.includes(t)},t.prototype._addParent=function(t){var r=this._parentage;this._parentage=Array.isArray(r)?(r.push(t),r):r?[r,t]:t},t.prototype._removeParent=function(t){var r=this._parentage;r===t?this._parentage=null:Array.isArray(r)&&a(r,t)},t.prototype.remove=function(r){var n=this._finalizers;n&&a(n,r),r instanceof t&&r._removeParent(this)},t.EMPTY=((r=new t).closed=!0,r),t}();function f(t){return t instanceof l||t&&"closed"in t&&u(t.remove)&&u(t.add)&&u(t.unsubscribe)}function p(t){u(t)?t():t.unsubscribe()}l.EMPTY;var h={Promise:void 0},y=function(t,r){for(var n=[],e=2;e<arguments.length;e++)n[e-2]=arguments[e];return setTimeout.apply(void 0,i([t,r],o(n)))};function d(){}var v=function(t){function r(r){var n=t.call(this)||this;return n.isStopped=!1,r?(n.destination=r,f(r)&&r.add(n)):n.destination=w,n}return n(r,t),r.create=function(t,r,n){return new m(t,r,n)},r.prototype.next=function(t){this.isStopped||this._next(t)},r.prototype.error=function(t){this.isStopped||(this.isStopped=!0,this._error(t))},r.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},r.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,t.prototype.unsubscribe.call(this),this.destination=null)},r.prototype._next=function(t){this.destination.next(t)},r.prototype._error=function(t){try{this.destination.error(t)}finally{this.unsubscribe()}},r.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},r}(l),b=function(){function t(t){this.partialObserver=t}return t.prototype.next=function(t){var r=this.partialObserver;if(r.next)try{r.next(t)}catch(t){_(t)}},t.prototype.error=function(t){var r=this.partialObserver;if(r.error)try{r.error(t)}catch(t){_(t)}else _(t)},t.prototype.complete=function(){var t=this.partialObserver;if(t.complete)try{t.complete()}catch(t){_(t)}},t}(),m=function(t){function r(r,n,e){var o,i=t.call(this)||this;return o=u(r)||!r?{next:null!=r?r:void 0,error:null!=n?n:void 0,complete:null!=e?e:void 0}:r,i.destination=new b(o),i}return n(r,t),r}(v);function _(t){var r;r=t,y((function(){throw r}))}var w={closed:!0,next:d,error:function(t){throw t},complete:d},g="function"==typeof Symbol&&Symbol.observable||"@@observable";function x(t){return t}var S=function(){function t(t){t&&(this._subscribe=t)}return t.prototype.lift=function(r){var n=new t;return n.source=this,n.operator=r,n},t.prototype.subscribe=function(t,r,n){var e,o=this,i=(e=t)&&e instanceof v||function(t){return t&&u(t.next)&&u(t.error)&&u(t.complete)}(e)&&f(e)?t:new m(t,r,n);return function(){var t=o,r=t.operator,n=t.source;i.add(r?r.call(i,n):n?o._subscribe(i):o._trySubscribe(i))}(),i},t.prototype._trySubscribe=function(t){try{return this._subscribe(t)}catch(r){t.error(r)}},t.prototype.forEach=function(t,r){var n=this;return new(r=P(r))((function(r,e){var o=new m({next:function(r){try{t(r)}catch(t){e(t),o.unsubscribe()}},error:e,complete:r});n.subscribe(o)}))},t.prototype._subscribe=function(t){var r;return null===(r=this.source)||void 0===r?void 0:r.subscribe(t)},t.prototype[g]=function(){return this},t.prototype.pipe=function(){for(var t,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return(0===(t=r).length?x:1===t.length?t[0]:function(r){return t.reduce((function(t,r){return r(t)}),r)})(this)},t.prototype.toPromise=function(t){var r=this;return new(t=P(t))((function(t,n){var e;r.subscribe((function(t){return e=t}),(function(t){return n(t)}),(function(){return t(e)}))}))},t.create=function(r){return new t(r)},t}();function P(t){var r;return null!==(r=null!=t?t:h.Promise)&&void 0!==r?r:Promise}function O(t){return function(r){if(function(t){return u(null==t?void 0:t.lift)}(r))return r.lift((function(r){try{return t(r,this)}catch(t){this.error(t)}}));throw new TypeError("Unable to lift unknown Observable type")}}var E=function(t){function r(r,n,e,o,i,u){var s=t.call(this,r)||this;return s.onFinalize=i,s.shouldUnsubscribe=u,s._next=n?function(t){try{n(t)}catch(t){r.error(t)}}:t.prototype._next,s._error=o?function(t){try{o(t)}catch(t){r.error(t)}finally{this.unsubscribe()}}:t.prototype._error,s._complete=e?function(){try{e()}catch(t){r.error(t)}finally{this.unsubscribe()}}:t.prototype._complete,s}return n(r,t),r.prototype.unsubscribe=function(){var r;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var n=this.closed;t.prototype.unsubscribe.call(this),!n&&(null===(r=this.onFinalize)||void 0===r||r.call(this))}},r}(v),A=new S((function(t){return t.complete()}));function T(t){return t<=0?function(){return A}:O((function(r,n){var e,o,i,u=0;r.subscribe(new E(n,(function(r){++u<=t&&(n.next(r),t<=u&&n.complete())}),e,o,i))}))}var z="http://localhost:3000";function j(t,r){var n=t+"__"+Math.random().toString(36).substr(3),e={requestId:n,type:t,data:r};return new S((function(t){var r=window.opener||window.parent,o=function(){r.postMessage({requestId:n,type:"cancellation",data:null},z)};return window.addEventListener("message",(function(r){var e=r.data;if(e&&e.requestId===n){if(e.complete)return t.complete(),void o();if(e.error)return t.error(e.data),void o();t.next(e.data)}})),r.postMessage(e,z),o}))}t.getActivatedRoute=function(){return j("active-route",{}).toPromise()},t.graphQlMutation=function(t,r){var n=j("graphql-mutation",{document:t,variables:r});return{then:function(){for(var t,r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];return(t=n.pipe(T(1)).toPromise()).then.apply(t,r)},stream:n}},t.graphQlQuery=function(t,r,n){var e=j("graphql-query",{document:t,variables:r,fetchPolicy:n});return{then:function(){for(var t,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return(t=e.pipe(T(1)).toPromise()).then.apply(t,r)},stream:e}},t.notify=function(t){j("notification",t).toPromise()},t.setTargetOrigin=function(t){z=t}}));
