{"name": "@storefront/frontend", "description": "A headless commerce storefront starter kit built with Vendure & Qwik", "homepage": "https://qwik.builder.io/", "license": "", "private": true, "type": "module", "engines": {"node": ">=16"}, "scripts": {"build": "qwik build", "build.client": "vite build && npm run i18n-translate", "build.preview": "vite build --ssr src/entry.preview.tsx", "build.server": "vite build -c adapters/cloudflare-pages/vite.config.ts", "build.types": "tsc --incremental --noEmit", "deploy": "wrangler pages dev ./dist", "dev": "vite --mode ssr --port 8080", "dev.debug": "node --inspect-brk ./node_modules/vite/bin/vite.js --mode ssr --force", "fmt": "prettier --write .", "fmt.check": "prettier --check .", "i18n-extract": "node_modules/.bin/localize-extract -s \"dist/build/*.js\" -f json -o src/locales/message.en.json", "i18n-translate": "node_modules/.bin/localize-translate -s \"*.js\" -t src/locales/message.*.json -o dist/build/{{LOCALE}} -r ./dist/build", "i18n-sort": "ts-node --project scripts/tsconfig.scripts.json scripts/sort-i18n.ts src/locales", "lint": "eslint \"src/**/*.ts*\"", "preview": "qwik build preview && vite preview --open", "start": "pnpm generate && vite --open --mode ssr", "qwik": "qwik", "generate-shop": "cross-env DOTENV_CONFIG_PATH=.env graphql-codegen -r dotenv/config --config codegen-shop.ts", "generate-dev": "cross-env IS_DEV=TRUE pnpm generate-shop", "generate-local": "cross-env IS_LOCAL=TRUE pnpm generate-shop", "generate": "pnpm generate-shop"}, "devDependencies": {"@angular/compiler": "^17.1.3", "@angular/compiler-cli": "^17.1.3", "@qwik.dev/router": "2.0.0-alpha.8", "@qwik.dev/core": "2.0.0-alpha.8", "@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/introspection": "^4.0.2", "@graphql-codegen/typescript": "^4.0.4", "@graphql-codegen/typescript-operations": "^4.1.2", "@tailwindcss/forms": "^0.5.7", "@types/braintree-web-drop-in": "^1.39.3", "@types/eslint": "8.56.2", "@types/node": "20.11.17", "@typescript-eslint/eslint-plugin": "7.0.1", "@typescript-eslint/parser": "7.0.1", "autoprefixer": "10.4.17", "concurrently": "8.2.2", "cross-env": "^7.0.3", "eslint": "8.57.0", "eslint-plugin-qwik": "2.0.0-alpha.8", "husky": "^9.0.10", "node-fetch": "3.3.2", "postcss": "8.4.35", "prettier": "3.2.5", "pretty-quick": "^4.0.0", "tailwindcss": "3.4.1", "typescript": "5.3.3", "undici": "6.6.2", "vite": "5.4.6", "vite-tsconfig-paths": "4.3.1", "wrangler": "3.28.1"}, "dependencies": {"@angular/localize": "^17.1.3", "@graphql-codegen/typescript-generic-sdk": "^4.0.1", "@stripe/stripe-js": "^3.0.0", "braintree-web-drop-in": "^1.42.0", "framer-motion": "^12.12.2", "graphql": "^16.8.1", "graphql-tag": "^2.12.6", "qwik-image": "0.0.16", "zod": "^3.22.4"}, "overrides": {"@angular/compiler-cli": {"typescript": "5.3.3"}}}