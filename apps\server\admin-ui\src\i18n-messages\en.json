{"admin": {"create-new-administrator": "Create new administrator"}, "asset": {"add-asset": "Add asset", "add-asset-with-count": "Add {count, plural, =0 {assets} one {1 asset} other {{count} assets}}", "assets-selected-count": "{ count } assets selected", "change-asset": "Change asset", "dimensions": "Dimensions", "focal-point": "Focal point", "notify-create-assets-success": "Created {count, plural, one {new Asset} other {{count} new Assets}}", "original-asset-size": "Source size", "preview": "Preview", "remove-asset": "Remove asset", "select-asset": "Select asset", "select-assets": "Select assets", "set-as-featured-asset": "Set as featured asset", "set-focal-point": "Set focal point", "size": "Size", "source-file": "Source file", "unset-focal-point": "Unset", "update-focal-point": "Update point", "update-focal-point-error": "Could not update focal point", "update-focal-point-success": "Updated focal point", "upload-assets": "Upload assets", "uploading": "Uploading..."}, "breadcrumb": {"administrators": "Administrators", "assets": "Assets", "channels": "Channels", "collections": "Collections", "countries": "Countries", "customer-groups": "Customer groups", "customers": "Customers", "dashboard": "Dashboard", "facets": "Facets", "global-settings": "Global settings", "job-queue": "Job queue", "manage-variants": "Manage variants", "modifying-order": "Modifying order", "orders": "Orders", "payment-methods": "Payment methods", "product-options": "Product options", "products": "Products", "profile": "Profile", "promotions": "Promotions", "roles": "Roles", "scheduled-tasks": "Scheduled tasks", "seller-orders": "Seller orders", "sellers": "Sellers", "shipping-methods": "Shipping methods", "stock-locations": "Stock locations", "system-status": "System status", "tax-categories": "Tax categories", "tax-rates": "Tax rates", "zones": "Zones"}, "catalog": {"add-facet-value": "Add facet value", "add-facets": "Add facets", "add-option": "Add option", "add-price-in-another-currency": "Add a price in another currency", "add-stock-location": "Add stock location", "add-stock-to-location": "Add stock to location", "asset": "<PERSON><PERSON>", "asset-preview-links": "Asset preview links", "assets": "Assets", "assign-product-to-channel-success": "Successfully assigned {count, plural, one {1 product} other {{count} products}} to { channel }", "assign-products-to-channel": "Assign products to channel", "assign-to-named-channel": "Assign to { channelCode }", "assign-variant-to-channel-success": "Successfully assigned {count, plural, one {1 product variant} other {{count} product variants}} to { channel }", "assign-variants-to-channel": "Assign product variants to channel", "auto-update-option-variant-name": "Automatically update the names of ProductVariants using this option", "auto-update-product-variant-name": "Automatically update the names of ProductVariants", "calculated-price": "Calculated price", "calculated-price-tooltip": "There is a custom price calculation configured which modifies the price set above:", "cannot-create-variants-without-options": "Product variants cannot be created until an option group with at least two product options has been defined", "channel-price-preview": "Channel price preview", "collection": "Collection", "collection-contents": "Collection contents", "collections": "Collections", "confirm-bulk-delete-products": "Delete {count} products?", "confirm-cancel": "Cancel?", "confirm-delete-assets": "Delete {count} {count, plural, one {asset} other {assets}}?", "confirm-delete-facet-value": "Delete facet value?", "confirm-delete-product": "Delete product?", "confirm-delete-product-option": "Delete product option \"{name}\"?", "confirm-delete-product-option-group": "Delete product option group \"{name}\"?", "confirm-delete-product-option-group-body": "This option group is used by {count} {count, plural, one {variant} other {variants}}. Are you sure you want to delete it?", "confirm-delete-product-variant": "Delete product variant \"{name}\"?", "confirm-deletion-of-unused-variants-body": "The following product variants have been made obsolete due to the addition of new options. They will be deleted during the creation of the new product variants.", "confirm-deletion-of-unused-variants-title": "Delete obsolete product variants?", "create-draft-order": "Create draft order", "create-facet-value": "Create new facet value", "create-new-collection": "Create new collection", "create-new-facet": "Create new facet", "create-new-product": "New product", "create-new-stock-location": "Create new stock location", "create-product-option-group": "Create product option group", "create-product-variant": "Create product variant", "default-currency": "Default currency", "do-not-inherit-filters": "Do not inherit filters", "drop-files-to-upload": "Drop files to upload", "duplicate-collections": "Duplicate collections", "duplicate-facets": "Duplicate facets", "duplicate-products": "Duplicate products", "edit-facet-values": "Edit facet values", "edit-options": "Edit options", "facet": "Facet", "facet-value-not-available": "Facet value \"{ id }\" not available", "facet-values": "Facet values", "facets": "Facets", "filter-by-name": "Filter by name", "filter-inheritance": "Filter inheritance", "filters": "Filters", "inherit-filters-from-parent": "Inherit filters from parent", "live-preview-contents": "Live-preview contents", "manage-variants": "Manage variants", "move-collection-to": "Move to { name }", "move-collections": "Move collections", "move-collections-success": "Moved {count, plural, one {1 collection} other {{count} collections}}", "move-down": "Move down", "move-to": "Move to", "move-up": "Move up", "name": "Name", "no-channel-selected": "No channel selected", "no-featured-asset": "No featured asset", "no-selection": "No selection", "no-stock-locations-available-on-current-channel": "No stock locations are available on the current channel. Set up at least one stock location before adding products.", "notify-bulk-delete-products-success": "Successfully deleted {count, plural, one {1 product} other {{count} products}}", "notify-remove-facets-from-channel-success": "Successfully removed {count, plural, one {1 facet} other {{count} facets}} from { channelCode }", "notify-remove-product-from-channel-error": "Could not remove product from channel", "notify-remove-product-from-channel-success": "Successfully removed product from channel", "notify-remove-variant-from-channel-error": "Could not remove product variant from channel", "notify-remove-variant-from-channel-success": "Successfully removed product variant from channel", "number-of-variants": "# variants", "option": "Option", "option-name": "Option name", "option-values": "Option values", "out-of-stock-threshold": "Out-of-stock threshold", "out-of-stock-threshold-tooltip": "Sets the stock level at which this variant is considered to be out of stock. Using a negative value enables backorder support.", "page-description-options-editor": "Edit the names & codes of the options for this product. To add or remove options, use the \"manage variants\" button below the product variant list.", "price": "Price", "price-and-tax": "Price and tax", "price-conversion-factor": "Price conversion factor", "price-in-channel": "Price in { channel }", "price-includes-tax-at": "Includes tax at { rate }%", "price-with-tax-in-default-zone": "Inc. { rate }% tax: { price }", "private": "Private", "product": "Product", "product-name": "Product name", "product-options": "Product options", "product-variant-exists": "A product variant with these options already exists", "product-variants": "Product variants", "products": "Products", "public": "Public", "quick-jump-placeholder": "Quick jump to variant", "rebuild-search-index": "Rebuild search index", "reindex-error": "An error occurred while rebuilding search index", "reindex-successful": "Indexed {count, plural, one {product variant} other {{count} product variants}} in {time}ms", "reindexing": "Rebuilding search index", "remove-from-channel": "Remove from {channelCode, select, undefined{channel} other{{channelCode}}}", "remove-option": "Remove option", "remove-product-from-channel": "Remove product from channel", "remove-product-variant-from-channel": "Remove product variant from channel", "reorder-collection": "Re-order collection", "root-collection": "Root collection", "run-pending-search-index-updates": "Search index: run {count, plural, one {1 pending update} other {{count} pending updates}}", "running-search-index-updates": "Running {count, plural, one {1 update} other {{count} updates}} to search index", "search-asset-name-or-tag": "Search by asset name or tags", "search-for-term": "Search for term", "search-product-name-or-code": "Search by product name or code", "select-product": "Select product", "select-product-variant": "Select product variant", "sku": "SKU", "slug": "Slug", "slug-pattern-error": "Slug is invalid", "stock-allocated": "Allocated", "stock-levels": "Stock levels", "stock-location": "Stock location", "stock-locations": "Stock locations", "stock-on-hand": "Stock", "tax-category": "Tax category", "taxes": "Taxes", "track-inventory": "Track inventory", "track-inventory-false": "Do not track", "track-inventory-inherit": "Inherit from global settings", "track-inventory-tooltip": "When tracked, product variant stock levels will be automatically adjusted when sold", "track-inventory-true": "Track", "update-product-option": "Update product option", "use-global-value": "Use global value", "values": "Values", "variant": "<PERSON><PERSON><PERSON>", "variant-count": "{count, plural, one {1 variant} other {{count} variants}}", "view-contents": "View contents", "visibility": "Visibility"}, "common": {"ID": "ID", "add-filter": "Add filter", "add-item-to-list": "Add item to list", "add-note": "Add note", "apply": "Apply", "assign-to-channel": "Assign to channel", "assign-to-channels": "Assign to {count, plural, one {channel} other {channels}}", "available-currencies": "Available currencies", "available-languages": "Available languages", "boolean-and": "and", "boolean-false": "false", "boolean-or": "or", "boolean-true": "true", "breadcrumb": "Breadcrumb", "browser-default": "Browser default", "cancel": "Cancel", "cancel-navigation": "Cancel navigation", "change-selection": "Change selection", "channel": "Channel", "channels": "Channels", "clear-selection": "Clear selection", "code": "Code", "collapse-entries": "Collapse entries", "confirm": "Confirm", "confirm-bulk-assign-to-channel": "Assign items to channel?", "confirm-bulk-delete": "Delete the selected items?", "confirm-bulk-remove-from-channel": "Remove items from current channel?", "confirm-delete-note": "Delete note?", "confirm-navigation": "Confirm navigation", "contents": "Contents", "create": "Create", "created-at": "Created at", "custom-fields": "Custom fields", "data-table-filter-date-mode": "Date mode", "data-table-filter-date-range": "Date range", "data-table-filter-date-relative": "Relative date", "default-channel": "Default channel", "default-language": "Default language", "default-tax-category": "Default tax category", "delete": "Delete", "description": "Description", "details": "Details", "disable": "Disable", "disabled": "Disabled", "discard-changes": "Discard changes", "duplicate": "Duplicate", "edit": "Edit", "edit-field": "Edit field", "edit-note": "Edit note", "enable": "Enable", "enabled": "Enabled", "end-date": "End date", "expand-entries": "Expand entries", "extension-running-in-separate-window": "Extension is running in a separate window", "filter": "Filter", "filter-preset-name": "Filter preset name", "force-delete": "Force delete", "force-remove": "Force remove", "general": "General", "guest": "Guest", "id": "ID", "image": "Image", "items-per-page-option": "{ count } per page", "items-selected-count": "{ count } {count, plural, one {item} other {items}} selected", "keep-editing": "Keep editing", "language": "Language", "launch-extension": "Launch extension", "list-items-and-n-more": "{ items } and {nMore} more", "live-update": "Live update", "locale": "Locale", "log-out": "Log out", "login": "Log in", "login-image-title": "Hi! Welcome back. Good to see you.", "login-title": "Log in to {brand}", "manage-tags": "Manage tags", "manage-tags-description": "Update or delete tags globally.", "medium-date": "Medium date", "more": "More...", "name": "Name", "no-alerts": "No alerts", "no-bulk-actions-available": "No bulk actions available", "no-channel-selected": "No channel selected", "no-results": "No results", "not-applicable": "Not applicable", "not-set": "Not set", "notify-assign-to-channel-success-with-count": "Successfully assigned {count, plural, one {1 item} other {{count} items}} to { channelCode }", "notify-bulk-update-success": "Updated { count } { entity }", "notify-create-error": "An error occurred, could not create { entity }", "notify-create-success": "Created new { entity }", "notify-delete-error": "An error occurred, could not delete { entity }", "notify-delete-error-with-count": "Could not delete {count, plural, one {1 item} other {{count} items}}", "notify-delete-success": "Deleted { entity }", "notify-delete-success-with-count": "Successfully deleted {count, plural, one {1 item} other {{count} items}}", "notify-duplicate-error": "Could not duplicate { name } due to an error: { error }", "notify-duplicate-error-excess": "An additional { count } {count, plural, one {item} other {items}} could not be duplicated due to errors", "notify-duplicate-success": "Successfully duplicated {count, plural, one {1 item} other {{count} items}}: { names }", "notify-remove-from-channel-success-with-count": "Successfully removed { count } items from channel", "notify-save-changes-error": "An error occurred, could not save changes", "notify-saved-changes": "Saved changes", "notify-update-error": "An error occurred, could not update { entity }", "notify-update-success": "Updated { entity }", "notify-updated-tags-success": "Successfully updated tags", "okay": "Okay", "operator-contains": "contains", "operator-eq": "equals", "operator-gt": "greater than", "operator-lt": "less than", "operator-not-contains": "does not contain", "operator-not-eq": "does not equal", "operator-notContains": "does not contain", "operator-regex": "matches regex", "password": "Password", "position": "Position", "price": "Price", "price-with-tax": "Price with tax", "private": "Private", "public": "Public", "remember-me": "Remember me", "remove": "Remove", "remove-from-channel": "Remove from current channel", "remove-item-from-list": "Remove item from list", "rename-filter-preset": "<PERSON>ame preset", "reset-columns": "Reset columns", "results-count": "{ count } {count, plural, one {result} other {results}}", "sample-formatting": "Sample formatting", "save-filter-preset": "Save as preset", "search-and-filter-list": "Search and filter this list", "search-by-name": "Search by name", "select": "Select...", "select-display-language": "Select display language", "select-items-with-count": "Select { count } {count, plural, one {item} other {items}}", "select-products": "Select products", "select-relation-id": "Select relation ID", "select-table-columns": "Select table columns", "select-today": "Select today", "select-variants": "Select variants", "seller": "<PERSON><PERSON>", "set-language": "Set language", "short-date": "Short date", "slug": "Slug", "start-date": "Start date", "status": "Status", "tags": "Tags", "theme": "Theme", "there-are-unsaved-changes": "There are unsaved changes. Navigating away will cause these changes to be lost.", "toggle-all": "Toggle all", "total-items": "{currentStart} - {currentEnd} of {totalItems}", "update": "Update", "updated-at": "Updated at", "username": "Username", "value": "Value", "view-contents": "View contents", "view-next-month": "View next month", "view-previous-month": "View previous month", "visibility": "Visibility", "with-selected": "With {count} selected..."}, "customer": {"add-customer-to-group": "Add customer to group", "add-customer-to-groups-with-count": "Add customer to {count, plural, one {1 group} other {{count} groups}}", "add-customers-to-group": "Add customers to group", "add-customers-to-group-success": "Added {customerCount, plural, one {1 customer} other {{customerCount} customers}} to \"{ groupName }\"", "add-customers-to-group-with-count": "Add {count, plural, one {1 customer} other {{count} customers}}", "add-customers-to-group-with-name": "Add customers to \"{ groupName }\"", "addresses": "Addresses", "city": "City", "company": "Company", "confirm-remove-customer-from-group": "Remove customer from group?", "country": "Country", "create-customer-group": "Create customer group", "create-new-address": "Create new address", "create-new-customer": "Create new customer", "create-new-customer-group": "Create new customer group", "customer": "Customer", "customer-group": "Customer group", "customer-groups": "Customer groups", "customer-history": "Customer history", "customers": "Customers", "default-billing-address": "Default billing", "default-shipping-address": "Default shipping", "email-address": "Email address", "email-verification-sent": "A verification email has been sent to { emailAddress }", "first-name": "First name", "full-name": "Full name", "guest": "Guest", "history-customer-added-to-group": "Customer added to group \"{ groupName }\"", "history-customer-address-created": "Address created", "history-customer-address-deleted": "Address deleted", "history-customer-address-updated": "Address updated", "history-customer-detail-updated": "Customer details updated", "history-customer-email-update-requested": "Email address update requested", "history-customer-email-update-verified": "Email address update verified", "history-customer-password-reset-requested": "Password reset requested", "history-customer-password-reset-verified": "Password reset verified", "history-customer-password-updated": "Password updated", "history-customer-registered": "Customer registered", "history-customer-removed-from-group": "Customer removed from group \"{ groupName }\"", "history-customer-verified": "Customer verified", "history-using-external-auth-strategy": "using { strategy }", "history-using-native-auth-strategy": "using email address", "last-login": "Last login", "last-name": "Last name", "name": "Name", "new-email-address": "New email address", "no-orders-placed": "No orders placed", "not-a-member-of-any-groups": "This customer is not a member of any groups", "old-email-address": "Old email address", "orders": "Orders", "password": "Password", "phone-number": "Phone number", "postal-code": "Postal code", "province": "Province", "registered": "Registered", "remove-customers-from-group-success": "Removed {customerCount, plural, one {1 customer} other {{customerCount} customers}} from \"{ groupName }\"", "remove-from-group": "Remove from this group", "search-customers-by-email": "Search by email address", "search-customers-by-email-last-name-postal-code": "Search by email / last name / postal code", "select-customer": "Select customer", "set-as-default-billing-address": "Set as default billing", "set-as-default-shipping-address": "Set as default shipping", "street-line-1": "Street line 1", "street-line-2": "Street line 2", "title": "Title", "update-customer-group": "Update customer group", "verified": "Verified", "view-group-members": "View group members"}, "dashboard": {"add-widget": "Add widget", "latest-orders": "Latest orders", "metric-average-order-value": "Average order value", "metric-number-of-orders": "Number of orders", "metric-order-total-value": "Order total value", "metrics": "Metrics", "orders-summary": "Orders summary", "remove-widget": "Remove widget", "thisMonth": "This month", "thisWeek": "This week", "today": "Today", "total-order-value": "Total value", "total-orders": "Total orders", "widget-resize": "Resize", "widget-width": "Width: {width}", "yesterday": "Yesterday"}, "datetime": {"ago-days": "{count, plural, one {1 day} other {{count} days}} ago", "ago-hours": "{count, plural, one {1 hr} other {{count} hrs}} ago", "ago-minutes": "{count, plural, one {1 min} other {{count} mins}} ago", "ago-seconds": "{count, plural, =0 {just now} one {1 sec ago} other {{count} secs ago}}", "ago-years": "{count, plural, one {1 year} other {{count} years}} ago", "day": "day", "duration-milliseconds": "{ms}ms", "duration-minutes:seconds": "{m}:{s}m", "duration-seconds": "{s}s", "month": "month", "month-apr": "April", "month-aug": "August", "month-dec": "December", "month-feb": "February", "month-jan": "January", "month-jul": "July", "month-jun": "June", "month-mar": "March", "month-may": "May", "month-nov": "November", "month-oct": "October", "month-sep": "September", "relative-past-days": "Past {count, plural, one {1 day} other {{count} days}}", "relative-past-months": "Past {count, plural, one {1 month} other {{count} months}}", "relative-past-years": "Past {count, plural, one {1 year} other {{count} years}}", "time": "Time", "weekday-fr": "Fr", "weekday-mo": "Mo", "weekday-sa": "Sa", "weekday-su": "Su", "weekday-th": "Th", "weekday-tu": "Tu", "weekday-we": "We", "year": "year"}, "editor": {"height": "Height", "image-alt": "Description (alt)", "image-src": "Source", "image-title": "Title", "insert-image": "Insert image", "link-href": "<PERSON> href", "link-target": "Link target", "link-title": "Link title", "remove-link": "Remove", "set-link": "Set link", "width": "<PERSON><PERSON><PERSON>"}, "error": {"403-forbidden": "You are not currently authorized to access \"{ path }\". Either you lack permissions, or your session has expired.", "could-not-connect-to-server": "Could not connect to the Vendure server at { url }", "health-check-failed": "System health check failed", "no-default-shipping-zone-set": "This channel has no default shipping zone. This may cause errors when calculating order shipping charges.", "no-default-tax-zone-set": "This channel has no default tax zone, which will cause errors when calculating prices. Please create or select a zone."}, "marketing": {"actions": "Actions", "add-action": "Add action", "add-condition": "Add condition", "conditions": "Conditions", "coupon-code": "Coupon code", "create-new-promotion": "Create new promotion", "duplicate-promotions": "Duplicate promotions", "ends-at": "Ends at", "per-customer-limit": "Per-customer limit", "per-customer-limit-tooltip": "Maximum number of times this promotion can be used by a single customer", "promotion": "Promotion", "search-by-name-or-coupon-code": "Search by name or coupon code", "starts-at": "Starts at", "usage-limit": "Total usage limit", "usage-limit-tooltip": "Maximum number of times this promotion can be used in total"}, "nav": {"administrators": "Administrators", "assets": "Assets", "catalog": "Catalog", "channels": "Channels", "collections": "Collections", "countries": "Countries", "customer-groups": "Customer groups", "customers": "Customers", "facets": "Facets", "global-settings": "Global settings", "job-queue": "Job queue", "marketing": "Marketing", "orders": "Orders", "payment-methods": "Payment methods", "products": "Products", "promotions": "Promotions", "roles": "Roles", "sales": "Sales", "scheduled-tasks": "Scheduled tasks", "sellers": "Sellers", "settings": "Settings", "shipping-methods": "Shipping methods", "stock-locations": "Stock locations", "system": "System", "system-status": "System status", "tax-categories": "Tax categories", "tax-rates": "Tax rates", "zones": "Zones"}, "order": {"add-item-to-order": "Add item to order", "add-note": "Add note", "add-payment": "Add payment", "add-payment-to-order": "Add payment to order", "add-payment-to-order-success": "Successfully added a payment to the order", "add-surcharge": "Add surcharge", "added-items": "Added items", "amount": "Amount", "arrange-additional-payment": "Arrange additional payment", "assign-order-to-another-customer": "Assign order to another customer", "billing-address": "Billing address", "cancel": "Cancel", "cancel-entire-order": "Cancel entire order", "cancel-fulfillment": "Cancel fulfillment", "cancel-modification": "Cancel modification", "cancel-order": "Cancel order or items", "cancel-payment": "Cancel payment", "cancel-reason-customer-request": "Customer request", "cancel-reason-not-available": "Not available", "cancel-selected-items": "Cancel selected items", "cancel-specified-items": "Cancel specified items", "cancellation-reason": "Cancellation reason", "cancelled-order-items-success": "Cancelled { count } { count, plural, one {item} other {items} } from the order", "cancelled-order-success": "Successfully cancelled order", "complete-draft-order": "Complete draft", "confirm-modifications": "Confirm modifications", "contents": "Contents", "create-fulfillment": "Create fulfillment", "create-fulfillment-success": "Created fulfillment", "customer": "Customer", "delete-draft-order": "Delete draft", "draft-order": "Draft order", "edit-billing-address": "Edit billing address", "edit-shipping-address": "Edit shipping address", "error-message": "Error message", "existing-address": "Existing address", "existing-customer": "Existing customer", "filter-is-active": "Is active", "fulfill": "Fulfill", "fulfill-order": "Fulfill order", "fulfillment": "Fulfillment", "fulfillment-method": "Fulfillment method", "history-coupon-code-applied": "Coupon code applied", "history-coupon-code-removed": "Coupon code removed", "history-customer-updated": "Customer updated", "history-fulfillment-created": "Fulfillment created", "history-fulfillment-delivered": "Fulfillment delivered", "history-fulfillment-shipped": "Fulfillment shipped", "history-fulfillment-transition": "Fulfillment transitioned from {from} to {to}", "history-items-cancelled": "{count} {count, plural, one {item} other {items}} cancelled", "history-order-cancelled": "Order cancelled", "history-order-created": "Order created", "history-order-fulfilled": "Order fulfilled", "history-order-modified": "Order modified", "history-order-transition": "Order transitioned from {from} to {to}", "history-payment-settled": "Payment settled", "history-payment-transition": "Payment #{id} transitioned from {from} to {to}", "history-refund-transition": "Refund #{id} transitioned from {from} to {to}", "item-count": "{count} {count, plural, one {item} other {items}}", "line-fulfillment-all": "All items fulfilled", "line-fulfillment-none": "No items fulfilled", "line-fulfillment-partial": "{ count } of { total } items fulfilled", "manually-transition-to-state": "Manually transition to state...", "manually-transition-to-state-message": "Manually transition the order to another state. Note that order states are governed by rules which may prevent certain transitions.", "modification-adding-items": "Adding {count} {count, plural, one {item} other {items}}", "modification-adding-surcharges": "Adding {count} {count, plural, one {surcharge} other {surcharges}}", "modification-adjusting-lines": "Adjusting {count} {count, plural, one {line} other {lines}}", "modification-not-settled": "Not settled", "modification-recalculate-shipping": "Recalculate shipping", "modification-settled": "Settled", "modification-summary": "Summary of modifications", "modification-updating-billing-address": "Updating billing address", "modification-updating-shipping-address": "Updating shipping address", "modified-items": "Modified items", "modify-order": "Modify order", "modify-order-price-difference": "Price difference", "net-price": "Net price", "new-customer": "New customer", "no-modifications-made": "No modifications have been made", "note": "Note", "note-is-private": "Note is private", "note-only-visible-to-administrators": "Visible to admins only", "note-visible-to-customer": "Visible to admins and customer", "order": "Order", "order-history": "Order history", "order-is-empty": "Order is empty", "order-state-diagram": "Order state diagram", "order-type": "Order type", "order-type-aggregate": "Aggregate", "order-type-regular": "Regular", "order-type-seller": "<PERSON><PERSON>", "orders": "Orders", "original-quantity-at-checkout": "Original quantity at checkout", "payment": "Payment", "payment-amount": "Payment amount", "payment-metadata": "Payment metadata", "payment-method": "Payment method", "payment-state": "State", "payments": "Payments", "placed-at": "Placed at", "preview-changes": "Preview changes", "previous-customer": "Previous customer", "product-name": "Product name", "product-sku": "SKU", "promotions-applied": "Promotions applied", "prorated-unit-price": "Prorated unit price", "quantity": "Quantity", "refund": "Refund", "refund-amount": "Refund amount", "refund-and-cancel-order": "Refund & cancel order", "refund-cancellation-reason": "Refund/cancellation reason", "refund-cancellation-reason-required": "Refund/cancellation reason is required", "refund-metadata": "Refund metadata", "refund-order-failed": "Refund failed", "refund-order-success": "Successfully refunded order", "refund-reason": "Refund reason", "refund-reason-customer-request": "Customer request", "refund-reason-not-available": "Not available", "refund-shipping": "Refund shipping", "refund-this-payment": "Refund this payment", "refund-total": "Refund total", "refund-total-error": "Refund total must be between {min} and {max}", "refund-total-warning": "Please specify refund amounts that equal the refund total.", "refund-with-amount": "Refund {amount}", "refundable-amount": "Refundable amount", "refunded-count": "{count} {count, plural, one {item} other {items}} refunded", "removed-items": "Removed items", "return-to-stock": "Return to stock", "search-by-order-filters": "Search by name / code / transaction ID", "select-address": "Select address", "select-shipping-method": "Select shipping method", "select-state": "Select state", "seller-orders": "Seller orders", "set-billing-address": "Set billing address", "set-coupon-codes": "Set coupon codes", "set-customer-for-order": "Set customer", "set-customer-success": "Successfully set customer", "set-fulfillment-state": "Mark as {state}", "set-shipping-address": "Set shipping address", "set-shipping-method": "Set shipping method", "settle-payment": "Settle payment", "settle-payment-error": "Could not settle payment", "settle-payment-success": "Successfully settled payment", "settle-refund": "Settle refund", "settle-refund-manual-instructions": "After manually refunding via your payment provider ({method}), enter the transaction ID here.", "settle-refund-success": "Successfully settled refund", "shipping": "Shipping", "shipping-address": "Shipping address", "shipping-cancelled": "Shipping cancelled", "shipping-method": "Shipping method", "state": "State", "sub-total": "Sub total", "successfully-updated-fulfillment": "Successfully updated fulfillment", "surcharges": "Surcharges", "tax-base": "Tax base", "tax-description": "Tax description", "tax-rate": "Tax rate", "tax-summary": "Tax summary", "tax-total": "Tax total", "total": "Total", "tracking-code": "Tracking code", "transaction-id": "Transaction ID", "transition-to-state": "Transition to { state } state", "transitioned-payment-to-state-success": "Successfully transitioned payment to { state }", "transitioned-to-state-success": "Successfully transitioned to { state }", "unable-to-transition-to-state-try-another": "The order could not be transitioned back to the \"{state}\" state. Please select an alternative state.", "unfulfilled": "Unfulfilled", "unit-price": "Unit price"}, "settings": {"add-countries-to-zone": "Add countries to { zoneName }", "add-countries-to-zone-success": "Added { countryCount } {countryCount, plural, one {country} other {countries}} to zone \"{ zoneName }\"", "add-products-to-test-order": "Add products to the test order", "administrator": "Administrator", "channel": "Channel", "channel-token": "Channel token", "country": "Country", "create-new-channel": "Create new channel", "create-new-country": "Create new country", "create-new-payment-method": "Create new payment method", "create-new-role": "Create new role", "create-new-seller": "Create new seller", "create-new-shipping-method": "Create new shipping method", "create-new-tax-category": "Create tax category", "create-new-tax-rate": "Create new tax rate", "create-new-zone": "Create new zone", "default-currency": "Default currency", "default-role-label": "This is a default Role and cannot be modified", "default-shipping-zone": "Default shipping zone", "default-tax-zone": "Default tax zone", "defaults": "De<PERSON>ults", "eligible": "Eligible", "email-address": "Email address", "email-address-or-identifier": "Email address or identifier", "first-name": "First name", "fulfillment-handler": "Fulfillment handler", "global-available-languages-tooltip": "Sets the languages that are available for all channels. Individual channels can then support a subset of these languages.", "global-out-of-stock-threshold": "Global out-of-stock threshold", "global-out-of-stock-threshold-tooltip": "Sets the stock level at which this a variant is considered to be out of stock. Using a negative value enables backorder support. Can be overridden by product variants.", "last-name": "Last name", "no-eligible-shipping-methods": "No eligible shipping methods", "password": "Password", "payment-eligibility-checker": "Payment eligibility checker", "payment-handler": "Payment handler", "payment-method": "Payment method", "permissions": "Permissions", "prices-include-tax": "Prices include tax for the default Zone", "profile": "Profile", "rate": "Rate", "remove-countries-from-zone-success": "Removed { countryCount } {countryCount, plural, one {country} other {countries}} from zone \"{ zoneName }\"", "remove-from-zone": "Remove from zone", "role": "Role", "roles": "Roles", "search-by-product-name-or-sku": "Search by product name or SKU", "seller": "<PERSON><PERSON>", "shipping-calculator": "Shipping calculator", "shipping-eligibility-checker": "Shipping eligibility checker", "shipping-method": "Shipping method", "tax-category": "Tax category", "tax-rate": "Tax rate", "test-address": "Test address", "test-result": "Test result", "test-shipping-method": "Test shipping method", "test-shipping-methods": "Test shipping methods", "track-inventory-default": "Track inventory by default", "view-zone-members": "View members", "zone": "Zone"}, "state": {"adding-items": "Adding items", "arranging-additional-payment": "Arranging additional payment", "arranging-payment": "Arranging payment", "authorized": "Authorized", "cancelled": "Cancelled", "created": "Created", "declined": "Declined", "delivered": "Delivered", "draft": "Draft", "error": "Error", "failed": "Failed", "modifying": "Modifying", "partially-delivered": "Partially delivered", "partially-shipped": "Partially shipped", "payment-authorized": "Payment authorized", "payment-settled": "Payment settled", "pending": "Pending", "settled": "Settled", "shipped": "Shipped"}, "system": {"all-job-queues": "All job queues", "could-not-trigger-task": "Could not trigger task", "health-all-systems-up": "All systems up", "health-error": "Error: one or more systems are down!", "health-last-checked": "Last checked", "health-message": "Message", "health-refresh": "Refresh", "health-status": "Status", "health-status-down": "Down", "health-status-up": "Up", "job-data": "Job data", "job-duration": "Duration", "job-error": "Job error", "job-queue-name": "Queue name", "job-result": "Job result", "job-state": "Job state", "job-state-all": "All states", "job-state-cancelled": "Cancelled", "job-state-completed": "Completed", "job-state-failed": "Failed", "job-state-pending": "Pending", "job-state-running": "Running", "last-executed-at": "Last executed", "last-result": "Last result", "next-execution-at": "Next execution", "run-task": "Run task", "schedule": "Schedule", "task-id": "Task ID", "task-will-be-triggered": "Task will be triggered"}}