# Generated Admin UI

This directory and its entire contents was generated the `compileUiExtensions()` function of the `@vendure/ui-devkit/compiler` package. It is not recommended to modify these files, since any changes will be overwritten upon re-compiling the ui extensions.

## Production app

When compiling in production mode (`devMode: false`), the compiled application will be output to the `./dist` directory. This is a production-ready Angular application which can then be served from any web server, with attention to the [Angular server configuration guide](https://angular.io/guide/deployment#server-configuration).
