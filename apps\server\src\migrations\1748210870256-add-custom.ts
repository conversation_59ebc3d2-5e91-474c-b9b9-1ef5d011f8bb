import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCustom1748210870256 implements MigrationInterface {
	public async up(queryRunner: QueryRunner): Promise<any> {
		await queryRunner.query(`ALTER TABLE "decal" DROP CONSTRAINT "FK_decal_asset"`, undefined);
		await queryRunner.query(`DROP INDEX "public"."IDX_decal_category"`, undefined);
		await queryRunner.query(`DROP INDEX "public"."IDX_decal_isActive"`, undefined);
		await queryRunner.query(
			`ALTER TABLE "product" ADD "customFieldsIscustomizable" boolean DEFAULT false`,
			undefined
		);
		await queryRunner.query(`ALTER TABLE "product" ADD "customFieldsDesignareas" text`, undefined);
		await queryRunner.query(
			`ALTER TABLE "order_line" ADD "customFieldsCustomdesign" text`,
			undefined
		);
		await queryRunner.query(
			`ALTER TABLE "decal" ALTER COLUMN "minScale" SET DEFAULT '0.5'`,
			undefined
		);
		await queryRunner.query(
			`ALTER TABLE "decal" ALTER COLUMN "maxScale" SET DEFAULT '2'`,
			undefined
		);
		await queryRunner.query(
			`ALTER TABLE "decal" ADD CONSTRAINT "FK_1338c21de2007d36e01c3d0368b" FOREIGN KEY ("assetId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
			undefined
		);
	}

	public async down(queryRunner: QueryRunner): Promise<any> {
		await queryRunner.query(
			`ALTER TABLE "decal" DROP CONSTRAINT "FK_1338c21de2007d36e01c3d0368b"`,
			undefined
		);
		await queryRunner.query(
			`ALTER TABLE "decal" ALTER COLUMN "maxScale" SET DEFAULT 2.0`,
			undefined
		);
		await queryRunner.query(
			`ALTER TABLE "decal" ALTER COLUMN "minScale" SET DEFAULT 0.5`,
			undefined
		);
		await queryRunner.query(
			`ALTER TABLE "order_line" DROP COLUMN "customFieldsCustomdesign"`,
			undefined
		);
		await queryRunner.query(
			`ALTER TABLE "product" DROP COLUMN "customFieldsDesignareas"`,
			undefined
		);
		await queryRunner.query(
			`ALTER TABLE "product" DROP COLUMN "customFieldsIscustomizable"`,
			undefined
		);
		await queryRunner.query(
			`CREATE INDEX "IDX_decal_isActive" ON "decal" ("isActive") `,
			undefined
		);
		await queryRunner.query(
			`CREATE INDEX "IDX_decal_category" ON "decal" ("category") `,
			undefined
		);
		await queryRunner.query(
			`ALTER TABLE "decal" ADD CONSTRAINT "FK_decal_asset" FOREIGN KEY ("assetId") REFERENCES "asset"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
			undefined
		);
	}
}
