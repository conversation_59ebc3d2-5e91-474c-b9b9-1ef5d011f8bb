import { component$ } from '@qwik.dev/core';
import { useNavigate } from '@qwik.dev/router';
import { Image } from 'qwik-image';
import { HighlightedButton } from '~/components/buttons/HighlightedButton';
import { Order } from '~/generated/graphql';
import { formatPrice } from '~/utils';

type IProps = {
	order: Order;
};

export default component$<IProps>(({ order }) => {
	const navigate = useNavigate();

	return (
		<div class="bg-gray-800 border border-gray-600 rounded-lg p-6 max-w-sm transition-colors hover:border-gray-500">
			<Image
				layout="fixed"
				width="200"
				height="200"
				aspectRatio={1}
				class="w-full h-48 object-center object-cover rounded-lg mb-4"
				src={order.lines[0]?.featuredAsset?.preview}
				alt={order.lines[0]?.productVariant?.name}
			/>
			<div class="space-y-3">
				<div>
					<h3 class="text-sm text-gray-400">
						Order:
						<span class="ml-2 text-lg font-semibold text-white">{order?.code}</span>
					</h3>
					<span class="bg-accent2 text-white text-xs px-3 py-1 mt-2 inline-block rounded-full uppercase font-medium tracking-wide">
						{order.state}
					</span>
					<p class="mt-2 text-accent1 font-semibold">
						{formatPrice(order?.totalWithTax, order?.currencyCode || 'USD')}
					</p>
				</div>
				<HighlightedButton
					extraClass="w-full"
					onClick$={() => {
						navigate(`/account/orders/${order?.code}`);
					}}
				>
					View Details
				</HighlightedButton>
			</div>
		</div>
	);
});
