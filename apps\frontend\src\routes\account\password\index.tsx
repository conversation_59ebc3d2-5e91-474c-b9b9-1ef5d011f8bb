import { $, component$, useContext, useSignal } from '@qwik.dev/core';
import { useNavigate } from '@qwik.dev/router';
import { HighlightedButton } from '~/components/buttons/HighlightedButton';
import { ErrorMessage } from '~/components/error-message/ErrorMessage';
import CheckIcon from '~/components/icons/CheckIcon';
import EyeIcon from '~/components/icons/EyeIcon';
import EyeSlashIcon from '~/components/icons/EyeSlashIcon';
import { APP_STATE } from '~/constants';
import { updateCustomerPasswordMutation } from '~/providers/shop/customer/customer';

export default component$(() => {
	const navigate = useNavigate();
	const appState = useContext(APP_STATE);
	const currentPassword = useSignal('');
	const newPassword = useSignal('');
	const confirmPassword = useSignal('');
	const errorMessage = useSignal('');
	const showPasswordAsTextSignal = useSignal(false);

	const updatePassword = $(async () => {
		errorMessage.value = '';
		if (newPassword.value === confirmPassword.value) {
			const updateCustomerPassword = await updateCustomerPasswordMutation(
				currentPassword.value,
				newPassword.value
			);

			switch (updateCustomerPassword.__typename) {
				case 'PasswordValidationError':
					errorMessage.value = 'Please set a stronger new password!';
					break;
				case 'InvalidCredentialsError':
					errorMessage.value = 'Current password does not match!';
					break;
				case 'NativeAuthStrategyError':
					errorMessage.value = 'Login method mismatch!';
					break;
				default:
					navigate('/account');
					break;
			}
		} else {
			errorMessage.value = 'Confirm password does not match!';
		}
	});

	const togglePasswordFields = $(() => {
		showPasswordAsTextSignal.value = !showPasswordAsTextSignal.value;
		if (showPasswordAsTextSignal.value) {
			const hiddenInputs = document.querySelectorAll('input[type="password"]');
			hiddenInputs.forEach((input: any) => {
				input.type = 'text';
			});
		} else {
			const visibleInputs = document.querySelectorAll('input[type="text"]');
			visibleInputs.forEach((input: any) => {
				input.type = 'password';
			});
		}
	});

	return appState.customer ? (
		<div class="max-w-6xl mx-auto p-4">
			<div class="max-w-md mx-auto">
				<div class="bg-gray-800 border border-gray-600 rounded-lg p-6">
					<h3 class="text-lg font-semibold text-white mb-6">Change Password</h3>
					<form class="space-y-6">
						<div>
							<label class="block text-sm font-medium text-gray-300 mb-2">Current Password</label>
							<input
								type={showPasswordAsTextSignal.value ? 'text' : 'password'}
								onChange$={(_, el) => {
									currentPassword.value = el.value;
								}}
								autoComplete="current-password"
								class="input-text"
							/>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-300 mb-2">New Password</label>
							<input
								type={showPasswordAsTextSignal.value ? 'text' : 'password'}
								onChange$={(_, el) => {
									newPassword.value = el.value;
								}}
								autoComplete="new-password"
								class="input-text"
							/>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-300 mb-2">Confirm Password</label>
							<input
								type={showPasswordAsTextSignal.value ? 'text' : 'password'}
								onChange$={(_, el) => {
									confirmPassword.value = el.value;
								}}
								autoComplete="new-password"
								class="input-text"
							/>
						</div>
						<div class="flex items-center justify-between">
							<HighlightedButton onClick$={updatePassword}>
								<CheckIcon /> &nbsp; Save Changes
							</HighlightedButton>
							<button
								type="button"
								preventdefault:click
								onClick$={togglePasswordFields}
								class="text-gray-400 hover:text-primary transition-colors p-2"
							>
								{showPasswordAsTextSignal.value ? <EyeIcon /> : <EyeSlashIcon />}
							</button>
						</div>
						{errorMessage.value !== '' && (
							<ErrorMessage
								heading="We ran into a problem changing your password!"
								message={errorMessage.value}
							/>
						)}
					</form>
				</div>
			</div>
		</div>
	) : (
		<div class="max-w-6xl mx-auto p-4">
			<div class="bg-gray-800 border border-gray-600 rounded-lg p-8 text-center">
				<p class="text-gray-400">Loading...</p>
			</div>
		</div>
	);
});
