import { $, component$, QRL, useSignal } from '@qwik.dev/core';
import type { Decal } from '~/generated/graphql-shop';
import DecalLibrary from './DecalLibrary';
import DesignCanvas from './DesignCanvas';
import DesignControls from './DesignControls';
import type { DesignState, PlacedDecal, SavedDesign } from './types/design-types';

interface DesignStudioProps {
	productVariant: any; // Will be typed with proper Vendure types
	onDesignComplete$: QRL<(design: SavedDesign) => void>;
	onClose$: QRL<() => void>;
}

export default component$<DesignStudioProps>(({ productVariant, onDesignComplete$, onClose$ }) => {
	const designState = useSignal<DesignState>({
		selectedArea: 'front',
		decals: [],
		productVariant,
	});

	const selectedDecal = useSignal<PlacedDecal | null>(null);

	const handleDecalSelect = $((decal: Decal) => {
		// Add decal to the current design area
		const newDecal: PlacedDecal = {
			id: `decal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			decalId: decal.id,
			area: designState.value.selectedArea,
			position: { x: 200, y: 250 }, // Center of printable area
			scale: 1.0,
			rotation: 0,
			zIndex: designState.value.decals.filter((d) => d.area === designState.value.selectedArea)
				.length,
		};

		designState.value = {
			...designState.value,
			decals: [...designState.value.decals, newDecal],
		};

		selectedDecal.value = newDecal;
	});

	const handleDecalUpdate = $((updatedDecal: PlacedDecal) => {
		designState.value = {
			...designState.value,
			decals: designState.value.decals.map((decal) =>
				decal.id === updatedDecal.id ? updatedDecal : decal
			),
		};

		if (selectedDecal.value?.id === updatedDecal.id) {
			selectedDecal.value = updatedDecal;
		}
	});

	const handleDecalDelete = $((decalId: string) => {
		designState.value = {
			...designState.value,
			decals: designState.value.decals.filter((decal) => decal.id !== decalId),
		};

		if (selectedDecal.value?.id === decalId) {
			selectedDecal.value = null;
		}
	});

	const handleDecalCanvasSelect = $((decal: PlacedDecal | null) => {
		selectedDecal.value = decal;
	});

	const handleSaveDesign = $(() => {
		const savedDesign: SavedDesign = {
			version: '1.0',
			productVariantId: productVariant.id,
			areas: {
				front: {
					decals: designState.value.decals.filter((d) => d.area === 'front'),
				},
				back: {
					decals: designState.value.decals.filter((d) => d.area === 'back'),
				},
				leftSleeve: {
					decals: designState.value.decals.filter((d) => d.area === 'leftSleeve'),
				},
				rightSleeve: {
					decals: designState.value.decals.filter((d) => d.area === 'rightSleeve'),
				},
			},
			metadata: {
				createdAt: new Date().toISOString(),
				totalDecals: designState.value.decals.length,
				previewUrls: [], // TODO: Generate preview images when needed
			},
		};

		onDesignComplete$(savedDesign);
	});

	const handleClearArea = $(() => {
		designState.value = {
			...designState.value,
			decals: designState.value.decals.filter(
				(decal) => decal.area !== designState.value.selectedArea
			),
		};
		selectedDecal.value = null;
	});

	const getTotalDecalsInArea = (area: string) => {
		return designState.value.decals.filter((d) => d.area === area).length;
	};

	return (
		<div class="design-studio fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4">
			<div class="design-studio-container bg-gray-900 rounded-lg max-w-7xl w-full max-h-full overflow-hidden flex flex-col">
				{/* Header */}
				<div class="flex justify-between items-center p-6 border-b border-gray-700">
					<div>
						<h2 class="text-2xl font-bold text-white">Design Studio</h2>
						<p class="text-gray-400">Customize your {productVariant.name}</p>
					</div>
					<div class="flex gap-3">
						<button
							type="button"
							class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
							onClick$={handleClearArea}
						>
							Clear {designState.value.selectedArea}
						</button>
						<button
							type="button"
							class="bg-primary hover:bg-secondary text-white px-6 py-2 rounded transition-colors"
							onClick$={handleSaveDesign}
						>
							Save Design
						</button>
						<button
							type="button"
							class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
							onClick$={onClose$}
						>
							Close
						</button>
					</div>
				</div>

				{/* Main Content */}
				<div class="flex-1 flex overflow-hidden">
					{/* Left Panel - Decal Library */}
					<div class="w-80 p-4 border-r border-gray-700 overflow-y-auto">
						<DecalLibrary onDecalSelect$={handleDecalSelect} />
					</div>

					{/* Center Panel - Design Canvas */}
					<div class="flex-1 p-4 overflow-y-auto">
						<DesignCanvas
							designState={designState}
							onDecalUpdate$={handleDecalUpdate}
							onDecalSelect$={handleDecalCanvasSelect}
							selectedDecal={selectedDecal}
						/>
					</div>

					{/* Right Panel - Design Controls */}
					<div class="w-80 p-4 border-l border-gray-700 overflow-y-auto">
						<DesignControls
							selectedDecal={selectedDecal}
							onDecalUpdate$={handleDecalUpdate}
							onDecalDelete$={handleDecalDelete}
						/>

						{/* Design Summary */}
						<div class="mt-6 bg-gray-800 p-4 rounded-lg">
							<h4 class="text-white font-bold mb-3">Design Summary</h4>
							<div class="space-y-2 text-sm">
								<div class="flex justify-between text-gray-300">
									<span>Front:</span>
									<span>{getTotalDecalsInArea('front')} decals</span>
								</div>
								<div class="flex justify-between text-gray-300">
									<span>Back:</span>
									<span>{getTotalDecalsInArea('back')} decals</span>
								</div>
								<div class="flex justify-between text-gray-300">
									<span>Left Sleeve:</span>
									<span>{getTotalDecalsInArea('leftSleeve')} decals</span>
								</div>
								<div class="flex justify-between text-gray-300">
									<span>Right Sleeve:</span>
									<span>{getTotalDecalsInArea('rightSleeve')} decals</span>
								</div>
								<div class="pt-2 border-t border-gray-700">
									<div class="flex justify-between text-white font-medium">
										<span>Total:</span>
										<span>{designState.value.decals.length} decals</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
});
