import gql from 'graphql-tag';
import type { Decal, DecalList, DecalListOptions } from '~/generated/graphql-shop';
import { requester } from '~/utils/api';

// Define the queries
const GET_DECALS = gql`
	query getDecals($options: DecalListOptions) {
		decals(options: $options) {
			items {
				id
				name
				description
				category
				maxWidth
				maxHeight
				minScale
				maxScale
				asset {
					id
					preview
				}
			}
			totalItems
		}
	}
`;

const GET_DECALS_BY_CATEGORY = gql`
	query getDecalsByCategory($category: String!) {
		decalsByCategory(category: $category) {
			id
			name
			description
			category
			maxWidth
			maxHeight
			minScale
			maxScale
			asset {
				id
				preview
			}
		}
	}
`;

// Service functions
export const getAllDecals = async (options?: DecalListOptions): Promise<DecalList> => {
	const result = await requester<{ decals: DecalList }, { options?: DecalListOptions }>(
		GET_DECALS,
		{ options }
	);
	return result.decals;
};

export const getDecalsByCategory = async (category: string): Promise<Decal[]> => {
	const result = await requester<{ decalsByCategory: Decal[] }, { category: string }>(
		GET_DECALS_BY_CATEGORY,
		{
			category,
		}
	);
	return result.decalsByCategory;
};

export const getDecalCategories = async (): Promise<string[]> => {
	const decals = await getAllDecals();
	const categories = decals.items.map((decal: Decal) => decal.category);
	return [...new Set(categories)];
};

export const getDecalById = async (id: string): Promise<Decal | undefined> => {
	const decals = await getAllDecals();
	return decals.items.find((decal: Decal) => decal.id === id);
};
