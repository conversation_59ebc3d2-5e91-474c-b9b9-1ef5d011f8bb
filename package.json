{"name": "storefront-qwik-starter", "description": "A headless commerce storefront starter kit built with Vendure & Qwik", "homepage": "https://qwik.builder.io/", "license": "", "private": true, "type": "module", "engines": {"node": ">=16"}, "scripts": {"build": "pnpm -r build", "dev": "pnpm -r --parallel dev", "dev:frontend": "pnpm --filter @storefront/frontend dev", "dev:server": "pnpm --filter sugarjays-shop dev", "develop": "pnpm -r --parallel dev", "lint": "pnpm -r lint", "fmt": "prettier --write .", "fmt.check": "prettier --check .", "prepare": "husky install", "clean": "pnpm -r clean"}, "devDependencies": {"concurrently": "8.2.2", "husky": "^9.0.10", "prettier": "3.2.5", "pretty-quick": "^4.0.0", "typescript": "5.3.3"}}