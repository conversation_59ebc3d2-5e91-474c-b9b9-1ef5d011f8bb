import { component$, useStore, useVisibleTask$ } from '@qwik.dev/core';
import { useLocation } from '@qwik.dev/router';
import { Image } from 'qwik-image';
import { Order } from '~/generated/graphql';
import { getOrderByCodeQuery } from '~/providers/shop/orders/order';
import { formatDateTime, formatPrice } from '~/utils';

export default component$(() => {
	const {
		params: { code },
	} = useLocation();
	const store = useStore<{ order?: Order }>({});

	useVisibleTask$(async () => {
		store.order = await getOrderByCodeQuery(code);
	});

	return store.order ? (
		<div class="max-w-6xl mx-auto p-4">
			<div class="bg-gray-800 border border-gray-600 rounded-lg p-6 mb-6">
				<div class="mb-6">
					<h2 class="text-xl font-semibold text-white mb-2">
						Order <span class="text-accent1">{store.order?.code}</span>
					</h2>
					<p class="text-gray-300">
						Placed on{' '}
						<span class="text-white font-medium">{formatDateTime(store.order?.createdAt)}</span>
					</p>
				</div>

				<div class="space-y-4">
					{store.order?.lines.map((line, key) => {
						return (
							<div key={key} class="flex items-center space-x-4 p-4 bg-gray-700 rounded-lg">
								<div class="flex-shrink-0 w-20 h-20 border border-gray-600 rounded-lg overflow-hidden">
									<Image
										layout="fixed"
										width={80}
										height={80}
										aspectRatio={1}
										class="rounded-lg object-cover w-full h-full"
										src={line.featuredAsset?.preview}
									/>
								</div>
								<div class="flex-1">
									<div class="flex justify-between items-start">
										<h3 class="text-white font-medium">{line.productVariant.name}</h3>
										<p class="text-accent1 font-semibold">
											{formatPrice(line.productVariant.price, store.order?.currencyCode || 'USD')}
										</p>
									</div>
									<div class="flex justify-between items-center mt-2">
										<span class="text-gray-400 text-sm">Quantity: {line.quantity}</span>
										<span class="text-white font-medium">
											{formatPrice(
												line.productVariant.price * line.quantity,
												store.order?.currencyCode || 'USD'
											)}
										</span>
									</div>
								</div>
							</div>
						);
					})}
				</div>
			</div>
			<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<div class="bg-gray-800 border border-gray-600 rounded-lg p-6">
					<h3 class="text-lg font-semibold text-white mb-4">Order Summary</h3>
					<dl class="space-y-3">
						<div class="flex items-center justify-between">
							<dt class="text-gray-300">Subtotal</dt>
							<dd class="text-white font-medium">
								{formatPrice(store.order?.subTotal, store.order?.currencyCode || 'USD')}
							</dd>
						</div>
						<div class="flex items-center justify-between">
							<dt class="text-gray-300">
								Shipping <span class="text-gray-400 text-sm">(Standard Shipping)</span>
							</dt>
							<dd class="text-white font-medium">
								{formatPrice(store.order?.shippingWithTax, store.order?.currencyCode || 'USD')}
							</dd>
						</div>
						<div class="flex items-center justify-between">
							<dt class="text-gray-300">Tax</dt>
							<dd class="text-white font-medium">
								{formatPrice(
									store.order?.taxSummary[0].taxTotal,
									store.order?.currencyCode || 'USD'
								)}
							</dd>
						</div>
						<div class="flex items-center justify-between border-t border-gray-600 pt-3">
							<dt class="text-lg font-semibold text-white">Total</dt>
							<dd class="text-lg font-semibold text-accent1">
								{formatPrice(store.order?.totalWithTax, store.order?.currencyCode || 'USD')}
							</dd>
						</div>
					</dl>
				</div>

				<div class="bg-gray-800 border border-gray-600 rounded-lg p-6">
					<h3 class="text-lg font-semibold text-white mb-4">Shipping Address</h3>
					<div class="text-gray-300 space-y-1">
						<p class="text-white font-medium">{store.order?.shippingAddress?.fullName}</p>
						<p>{store.order?.shippingAddress?.streetLine1}</p>
						{store.order?.shippingAddress?.streetLine2 && (
							<p>{store.order?.shippingAddress?.streetLine2}</p>
						)}
						<p>
							{store.order?.shippingAddress?.city}, {store.order?.shippingAddress?.province}
						</p>
						<p>{store.order?.shippingAddress?.postalCode}</p>
					</div>
				</div>
			</div>
		</div>
	) : (
		<div class="max-w-6xl mx-auto p-4">
			<div class="bg-gray-800 border border-gray-600 rounded-lg p-8 text-center">
				<p class="text-gray-400">Loading order details...</p>
			</div>
		</div>
	);
});
