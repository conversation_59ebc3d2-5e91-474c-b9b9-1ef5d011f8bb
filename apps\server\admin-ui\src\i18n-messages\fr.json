{"admin": {"create-new-administrator": "<PERSON><PERSON>er un nouvel administrateur"}, "asset": {"add-asset": "Ajouter un fichier", "add-asset-with-count": "Ajouter {count, plural, =0 {un fichier} one {un fichier} other {des fichiers}}", "assets-selected-count": "{ count } ressources selectionnées", "change-asset": "Changer ressource", "dimensions": "Dimensions", "focal-point": "Point focal", "notify-create-assets-success": "Création {count, plural, one {d'un nouveau fichier} other {de {count} nouveaux fichiers}}", "original-asset-size": "Taille originale", "preview": "<PERSON><PERSON><PERSON><PERSON>", "remove-asset": "<PERSON><PERSON><PERSON><PERSON> le fichier", "select-asset": "Sélectionner un fichier", "select-assets": "Sélectionner des fichiers", "set-as-featured-asset": "Définir comme fichier vedette", "set-focal-point": "Définir point de focale", "size": "<PERSON><PERSON>", "source-file": "Fichier source", "unset-focal-point": "<PERSON><PERSON><PERSON>", "update-focal-point": "Mettre à jour le point focal", "update-focal-point-error": "Échec de la mise à jour du point focal", "update-focal-point-success": "Point focal mis à jour", "upload-assets": "Téléverser des ressources", "uploading": "Téléversement en cours..."}, "breadcrumb": {"administrators": "Administrateurs", "assets": "Ressources", "channels": "Canaux", "collections": "Collections", "countries": "Pays", "customer-groups": "Groupe de clients", "customers": "Clients", "dashboard": "Tableau <PERSON>", "facets": "Facettes", "global-settings": "Paramètres globaux", "job-queue": "File d'attente des tâches", "manage-variants": "Gestion des variations", "modifying-order": "Modification de la commande", "orders": "Commandes", "payment-methods": "Modes de paiement", "product-options": "Options de produit", "products": "Produits", "profile": "Profil", "promotions": "Promotions", "roles": "<PERSON><PERSON><PERSON>", "scheduled-tasks": "Tâches programmées", "seller-orders": "Commandes des vendeurs", "sellers": "Vendeurs", "shipping-methods": "Modes de livraison", "stock-locations": "Emplacements de stock", "system-status": "État du système", "tax-categories": "Catégories de taxes", "tax-rates": "Taux de taxe", "zones": "Zones"}, "catalog": {"add-facet-value": "Ajouter une valeur de facette", "add-facets": "Ajouter des facettes", "add-option": "Ajouter une option", "add-price-in-another-currency": "Ajouter un prix dans une autre devise", "add-stock-location": "Ajouter un emplacement de stock", "add-stock-to-location": "Ajouter du stock à un emplacemente", "asset": "Ressource", "asset-preview-links": "Liens de prévisualisation", "assets": "Ressources", "assign-product-to-channel-success": "produit attribué au canal \"{ channel }\"", "assign-products-to-channel": "Attribuer des produits au canal", "assign-to-named-channel": "Attribuer à { channelCode }", "assign-variant-to-channel-success": "Variation de produit attribuée au canal \"{ channel }\"", "assign-variants-to-channel": "Attribuer des variantes de produit au canal", "auto-update-option-variant-name": "Mettre à jour automatiquement les noms des variantes avec cette option", "auto-update-product-variant-name": "Mettre à jour automatiquement les noms des variantes de produit ", "calculated-price": "Prix calculé", "calculated-price-tooltip": "Il y a un calcul de prix personnalisé configuré qui modifie le prix défini ci-dessus :", "cannot-create-variants-without-options": "Impossible de créer des variantes sans options", "channel-price-preview": "Aperçu du prix par canal", "collection": "Collection", "collection-contents": "Contenu de la Collection", "collections": "Collections", "confirm-bulk-delete-products": "Confirmer la suppression en masse des produits ?", "confirm-cancel": "Confirmer l'annulation ?", "confirm-delete-assets": "Supprimer {count} {count, plural, one {fichier} other {fichiers}} ?", "confirm-delete-facet-value": "Supprimer la valeur de facette ?", "confirm-delete-product": "Supprimer le produit ?", "confirm-delete-product-option": "Confirmer la suppression de l'option \"{name}\" ?", "confirm-delete-product-option-group": "Confirmer la suppression du groupe d'options \"{name}\" ?", "confirm-delete-product-option-group-body": "Ce groupe d'option est utilisé par {count} {count, plural, one {variant} other {variants}}. Voulez-vous vraiment le supprimer ?", "confirm-delete-product-variant": "Supprimer la variante du produit \"{name}\" ?", "confirm-deletion-of-unused-variants-body": "Les variantes de produit suivantes sont devenues obsolètes en raison de l'ajout de nouvelles options. Elles seront supprimées lors de la création des nouvelles variantes de produit.", "confirm-deletion-of-unused-variants-title": "Supprimer les variantes de produit obsolètes ?", "create-draft-order": "<PERSON><PERSON><PERSON> une commande brouillon", "create-facet-value": "<PERSON><PERSON><PERSON> une nouvelle valeur de facette", "create-new-collection": "C<PERSON>er une nouvelle collection", "create-new-facet": "<PERSON><PERSON><PERSON> une nouvelle facette", "create-new-product": "Créer un nouveau produit", "create-new-stock-location": "Créer un nouvel emplacement de stock", "create-product-option-group": "Créer un groupe d'options de produit", "create-product-variant": "Créer une variante de produit", "default-currency": "<PERSON><PERSON> par défaut", "do-not-inherit-filters": "Ne pas hériter des filtres", "drop-files-to-upload": "<PERSON>é<PERSON>r les fichiers pour téléverser", "duplicate-collections": "Dupliquer les collections", "duplicate-facets": "Dup<PERSON>r les facettes", "duplicate-products": "Dup<PERSON>r les produits", "edit-facet-values": "Modifier les valeurs de facette", "edit-options": "Modifier les options", "facet": "Facette", "facet-value-not-available": "Valeur de facette \"{ id }\" non disponible", "facet-values": "Valeurs de facette", "facets": "Facettes", "filter-by-name": "Filtrer par nom", "filter-inheritance": "Héritage des filtres", "filters": "Filtres", "inherit-filters-from-parent": "Hériter des filtres du parent", "live-preview-contents": "Prévisualisation du contenu en direct", "manage-variants": "<PERSON><PERSON><PERSON> les variantes", "move-collection-to": "<PERSON><PERSON><PERSON><PERSON> la collection vers { name }", "move-collections": "Déplacer les collections", "move-collections-success": "{count, plural, one {1 collection} other {{count} collections}} déplacée", "move-down": "<PERSON><PERSON><PERSON><PERSON> vers le bas", "move-to": "<PERSON><PERSON><PERSON><PERSON> vers", "move-up": "<PERSON><PERSON><PERSON><PERSON> vers le haut", "name": "Nom", "no-channel-selected": "Aucun canal sélectionné", "no-featured-asset": "<PERSON><PERSON><PERSON> ressource vedette", "no-selection": "Aucune sélection", "no-stock-locations-available-on-current-channel": "Aucun emplacement de stock n'est disponible sur le canal actuel. Configurez au moins un emplacement de stockage avant d'ajouter des produits.", "notify-bulk-delete-products-success": "{count, plural, one {1 product} other {{count} products}} supprimé(s) avec succès", "notify-remove-facets-from-channel-success": "{count, plural, one {1 facet} other {{count} facets}} de { channelCode } supprimé(s) avec succès", "notify-remove-product-from-channel-error": "Échec du retrait du produit du canal", "notify-remove-product-from-channel-success": "Produit retiré du canal avec succès", "notify-remove-variant-from-channel-error": "Échec du retrait de la variante du produit du canal", "notify-remove-variant-from-channel-success": "Variante retirée du canal avec succès", "number-of-variants": "Nombre de variantes", "option": "Option", "option-name": "Nom de l'option", "option-values": "Valeurs de l'option", "out-of-stock-threshold": "Seuil de rupture de stock", "out-of-stock-threshold-tooltip": "Définir le niveau de stock à partir duquel la variante est considérée comme en rupture de stock. Une valeur négative permet d'accepter des commandes en attente.", "page-description-options-editor": "Modifiez les noms et les codes des options de ce produit. Pour ajouter ou supprimer des options, utilisez le bouton \"gérer les variantes\" sous la liste des variantes du produit.", "price": "Prix", "price-and-tax": "Prix ​​et taxes", "price-conversion-factor": "Facteur de conversion du prix", "price-in-channel": "Prix dans { channel }", "price-includes-tax-at": "Taxes incluses à { rate }%", "price-with-tax-in-default-zone": "{ rate }% taxe Inc. : { price }", "private": "Priv<PERSON>", "product": "Produit", "product-name": "Nom du produit", "product-options": "Options du produit", "product-variant-exists": "Une variante de produit avec ces options existe déjà", "product-variants": "Variantes du produit", "products": "Produits", "public": "Public", "quick-jump-placeholder": "Aller à la variante", "rebuild-search-index": "Reconstruire l'index de recherche", "reindex-error": "Une erreur s'est produite lors de la reconstruction de l'index de recherche", "reindex-successful": "Indexation {count, plural, one {d'une variation de produit} other {de {count} variations de produit}} en {time} ms", "reindexing": "Reconstruction de l'index de recherche", "remove-from-channel": "Retirer du canal", "remove-option": "Retirer l'option", "remove-product-from-channel": "Retirer le produit du canal", "remove-product-variant-from-channel": "Retirer la variante du produit du canal", "reorder-collection": "Réorganiser la collection", "root-collection": "Collection racine", "run-pending-search-index-updates": "Index de recherche : exécuter {count, plural, one {1 mise à jour en attente} other {{count} mises à jour en attente}}", "running-search-index-updates": "Exécution de {count, plural, one {1 update} other {{count} updates}} de l'index de recherche", "search-asset-name-or-tag": "Rechercher par nom de fichier ou mot-clé", "search-for-term": "<PERSON><PERSON><PERSON> le terme", "search-product-name-or-code": "Rechercher  par nom de produit ou code", "select-product": "Sélectionner un produit", "select-product-variant": "Sélectionner une variante de produit", "sku": "UGS", "slug": "Identifiant", "slug-pattern-error": "Identifiant invalide", "stock-allocated": "Stock attribué", "stock-levels": "Niveaux de stock", "stock-location": "Emplacement du stockage", "stock-locations": "Emplacements de stockage", "stock-on-hand": "Stock disponible", "tax-category": "Catégorie de taxe", "taxes": "Taxes", "track-inventory": "Suivre les stocks", "track-inventory-false": "Ne pas suivre", "track-inventory-inherit": "Hériter des paramètres globaux", "track-inventory-tooltip": "<PERSON><PERSON><PERSON> suivi, le stock des variantes de produit est automatiqué ajusté après une vente.", "track-inventory-true": "Suivre", "update-product-option": "Mettre à jour l'option du produit", "use-global-value": "Utiliser la valeur globale", "values": "Valeurs", "variant": "<PERSON><PERSON><PERSON>", "variant-count": "{count, plural, one {1 variant} other {{count} variants}}", "view-contents": "Voir les produits", "visibility": "Visibilité"}, "common": {"ID": "ID", "add-filter": "Ajouter un filtre", "add-item-to-list": "Ajouter un élément à la liste", "add-note": "Ajouter une note", "apply": "Appliquer", "assign-to-channel": "Assigner à un canal", "assign-to-channels": "Assigner à {count, plural, one {canal} other {canaux}}", "available-currencies": "Devises disponibles", "available-languages": "Langues disponibles", "boolean-and": "et", "boolean-false": "faux", "boolean-or": "ou", "boolean-true": "vrai", "breadcrumb": "Fil d'ariane", "browser-default": "Navigateur par défaut", "cancel": "Annuler", "cancel-navigation": "Annuler la navigation", "change-selection": "Modifier la sélection", "channel": "Canal", "channels": "Canaux", "clear-selection": "Réinitialiser la sélection", "code": "Code", "collapse-entries": "Réduire les éléments", "confirm": "Confirmer", "confirm-bulk-assign-to-channel": "Attribuer les éléments au canal ?", "confirm-bulk-delete": "Supprimer les éléments sélectionnés ?", "confirm-bulk-remove-from-channel": "Retirer des éléments du canal actuel ?", "confirm-delete-note": "Supprimer la note ?", "confirm-navigation": "Confirmer la navigation", "contents": "Contenu", "create": "<PERSON><PERSON><PERSON>", "created-at": "<PERSON><PERSON><PERSON>", "custom-fields": "<PERSON><PERSON>", "data-table-filter-date-mode": "Mode de date", "data-table-filter-date-range": "Plage de dates", "data-table-filter-date-relative": "Date relative", "default-channel": "Canal par défaut", "default-language": "Langue par défaut", "default-tax-category": "Catégorie de taxe par défaut", "delete": "<PERSON><PERSON><PERSON><PERSON>", "description": "Description", "details": "Détails", "disable": "Désactiver", "disabled": "Désactivé", "discard-changes": "Annuler les modifications", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "edit-field": "Modifier champ", "edit-note": "Modifier note", "enable": "Activer", "enabled": "Activé", "end-date": "Date de fin", "expand-entries": "Développer les éléments", "extension-running-in-separate-window": "L'extension fonctionne dans une fenêtre distincte", "filter": "<PERSON><PERSON><PERSON>", "filter-preset-name": "Nom du préréglage de filtre", "force-delete": "Forcer la suppression", "force-remove": "Forcer le retrait", "general": "Général", "guest": "Invi<PERSON>", "id": "ID", "image": "Image", "items-per-page-option": "{ count } par page", "items-selected-count": "{ count } {count, plural, one {item} other {items}} sélectionné(s)", "keep-editing": "Continuer à modifier", "language": "<PERSON><PERSON>", "launch-extension": "Lancer l'extension", "list-items-and-n-more": "{ articles } et {nMore} de plus", "live-update": "Mise à jour automatique", "locale": "<PERSON><PERSON>", "log-out": "Déconnexion", "login": "Connexion", "login-image-title": "Bonjour ! Content de vous revoir.", "login-title": "Connectez-vous à {brand}", "manage-tags": "<PERSON><PERSON><PERSON> les mot-clés", "manage-tags-description": "Mettre à jour ou supprimer les mots-clés globalement", "medium-date": "Date moyenne", "more": "Plus...", "name": "Nom", "no-alerts": "Aucune alerte", "no-bulk-actions-available": "Aucune action groupée disponible", "no-channel-selected": "Aucun canal sélectionné", "no-results": "Aucun resultat", "not-applicable": "Non applicable", "not-set": "Non défini", "notify-assign-to-channel-success-with-count": "{count, plural, one {1 item} other {{count} items}} ont été attribués avec succès à { channelCode }", "notify-bulk-update-success": "{ count } { entity } ont été mis à jour", "notify-create-error": "Une erreur est survenue, création de { entity } échouée", "notify-create-success": "Nouveau { entity } c<PERSON>é avec succès", "notify-delete-error": "Une erreur est survenue, suppression de { entity } échouée", "notify-delete-error-with-count": "Impossible de supprimer {count, plural, one {1 item} other {{count} items}}", "notify-delete-success": "{ entity } supprimé avec succès", "notify-delete-success-with-count": "{count, plural, one {1 item} other {{count} items}} ont été supprimés avec succès", "notify-duplicate-error": "Impossible de dupliquer { name } en raison d'une erreur : { error }", "notify-duplicate-error-excess": "{ count } {count, plural, one {élément} other {éléments}} supplémentaire{count, plural, one {} other {s}} n'ont pas pu être dupliqué{count, plural, one {} other {s}} en raison d'erreurs", "notify-duplicate-success": "{count, plural, one {1 élément} other {{count} éléments}} dupliqués avec succès : { names }", "notify-remove-from-channel-success-with-count": "{count, plural, one {1 item} other {{count} items}} ont été retirés du canal avec succès", "notify-save-changes-error": "Une erreur est survenue, modifications non enregistrés", "notify-saved-changes": "Modifications enregistrées avec succès", "notify-update-error": "Une erreur est survenue, mise à jour de { entity } échouée", "notify-update-success": "{ entity } mis à jour avec succès", "notify-updated-tags-success": "Mots-clés mis à jour avec succès", "okay": "OK", "operator-contains": "contient", "operator-eq": "égal à", "operator-gt": "supérieur à", "operator-lt": "inférieur à", "operator-not-contains": "ne contient pas", "operator-not-eq": "différent de", "operator-notContains": "ne contient pas", "operator-regex": "correspond à l'expression régulière", "password": "Mot de passe", "position": "Position", "price": "Prix", "price-with-tax": "Prix avec taxe", "private": "Priv<PERSON>", "public": "Public", "remember-me": "Se souvenir de moi", "remove": "<PERSON><PERSON><PERSON>", "remove-from-channel": "Retirer du canal", "remove-item-from-list": "Retirer l'élément de la liste", "rename-filter-preset": "Renommer le préréglage", "reset-columns": "Réinitialiser les colonnes", "results-count": "{ count } {count, plural, one {result} other {results}}", "sample-formatting": "Exemple de mise en forme", "save-filter-preset": "Enregistrer en tant que préréglage", "search-and-filter-list": "Rechercher et filtrer la liste", "search-by-name": "Rechercher par nom", "select": "Sélectionner...", "select-display-language": "Choisir la langue d'affichage", "select-items-with-count": "Sélectionner { count } {count, plural, one {item} other {items}}", "select-products": "Sélectionner des produits", "select-relation-id": "Sélectionner un ID de relation", "select-table-columns": "Sélectionner des colonnes de tableau", "select-today": "<PERSON><PERSON> aujou<PERSON>'hui", "select-variants": "Sélectionner des variantes", "seller": "<PERSON><PERSON><PERSON>", "set-language": "Définir la langue", "short-date": "Date courte", "slug": "Identifiant", "start-date": "Date de début", "status": "Statut", "tags": "Mots-clés", "theme": "Thème", "there-are-unsaved-changes": "Des modifications non enregistrées existent. Quitter cette page les perdra.", "toggle-all": "<PERSON>ut sélectionner/désélectionner", "total-items": "{currentStart} - {currentEnd} de {totalItems}", "update": "Mettre à jour", "updated-at": "Mis à jour le", "username": "Nom d'utilisateur", "value": "<PERSON><PERSON>", "view-contents": "Voir le contenu", "view-next-month": "Voir le mois suivant", "view-previous-month": "Voir le mois précédent", "visibility": "Visibilité", "with-selected": "Avec la sélection..."}, "customer": {"add-customer-to-group": "Ajouter le client au groupe", "add-customer-to-groups-with-count": "Ajouter le client {count, plural, one {à un groupe} other {aux {count} groups}}", "add-customers-to-group": "Ajouter les clients au groupe", "add-customers-to-group-success": "Ajout {customerCount, plural, one {d'un client} other {de {customerCount} clients}} à \"{ groupName }\"", "add-customers-to-group-with-count": "Ajout {count, plural, one {d'un client} other {de {count} clients}}", "add-customers-to-group-with-name": "Ajouter les clients à \"{ groupName }\"", "addresses": "Adresses", "city": "Ville", "company": "Entreprise", "confirm-remove-customer-from-group": "Re<PERSON><PERSON> le client du groupe ?", "country": "Pays", "create-customer-group": "Créer un groupe de clients", "create-new-address": "<PERSON><PERSON><PERSON> une nouvelle adresse", "create-new-customer": "<PERSON><PERSON>er un nouveau client", "create-new-customer-group": "Créer un nouveau groupe de clients", "customer": "Client", "customer-group": "Groupe de cliens", "customer-groups": "Groupes de clients", "customer-history": "Historique du client", "customers": "Clients", "default-billing-address": "Adresse de facturation par défaut", "default-shipping-address": "<PERSON>ress<PERSON> de livraison par défaut", "email-address": "Adresse e-mail", "email-verification-sent": "Un e-mail de vérification à été envoyé à { emailAddress }", "first-name": "Prénom", "full-name": "Nom", "guest": "Invi<PERSON>", "history-customer-added-to-group": "Client ajouté au groupe \"{ groupName }\"", "history-customer-address-created": "<PERSON><PERSON><PERSON>", "history-customer-address-deleted": "<PERSON>resse supprimée", "history-customer-address-updated": "<PERSON>resse mise à jour", "history-customer-detail-updated": "<PERSON><PERSON><PERSON> mis à jour", "history-customer-email-update-requested": "Mise à jour de l'email demandée", "history-customer-email-update-verified": "Mise à jour de l'email vérifiée", "history-customer-password-reset-requested": "Réinitialisation du mot de passe demandée", "history-customer-password-reset-verified": "Réinitialisation du mot de passe vérifiée", "history-customer-password-updated": "Mot de passe mis à jour", "history-customer-registered": "Client inscrit", "history-customer-removed-from-group": "Client retiré du groupe \"{ groupName }\"", "history-customer-verified": "Client vérifié", "history-using-external-auth-strategy": "{ strategy } utilisé", "history-using-native-auth-strategy": "Adresse e-mail utilisée", "last-login": "Dernière connexion", "last-name": "Nom de famille", "name": "Nom", "new-email-address": "Nouvelle adresse email", "no-orders-placed": "Aucune commande passée", "not-a-member-of-any-groups": "Ce client n'est membre d'aucun groupe", "old-email-address": "Ancienne adresse email", "orders": "Commandes", "password": "Mot de passe", "phone-number": "Numéro de téléphone", "postal-code": "Code postal", "province": "Province", "registered": "Inscrit", "remove-customers-from-group-success": "Retirer {customerCount, plural, one {d'un client} other {de {customerCount} clients}} de \"{ groupName }\"", "remove-from-group": "Retirer de ce groupe", "search-customers-by-email": "Chercher par adresse email", "search-customers-by-email-last-name-postal-code": "Rechercher par nom / adresse email / code postal", "select-customer": "Sélectionner client", "set-as-default-billing-address": "Etablir en tant qu'adresse de facturation par défaut", "set-as-default-shipping-address": "Etablir en tant qu'adresse de livraison par défaut", "street-line-1": "Ligne de rue  1", "street-line-2": "Ligne de rue 2", "title": "Titre", "update-customer-group": "Mettre à jour groupe de client", "verified": "<PERSON><PERSON><PERSON><PERSON>", "view-group-members": "Voir les membres du groupe"}, "dashboard": {"add-widget": "Ajouter widget", "latest-orders": "Dernières commandes", "metric-average-order-value": "Valeur moyenne des commandes", "metric-number-of-orders": "Nombre de  commandes", "metric-order-total-value": "Total des commandes", "metrics": "Statistiques", "orders-summary": "Résumé des commandes", "remove-widget": "Supprimer widget", "thisMonth": "Ce mois-ci", "thisWeek": "<PERSON><PERSON> se<PERSON>", "today": "<PERSON><PERSON><PERSON>'hui", "total-order-value": "Valeur totale", "total-orders": "Toutes les commandes", "widget-resize": "Redimensionner", "widget-width": "Largeur: {width}", "yesterday": "<PERSON>er"}, "datetime": {"ago-days": "Il y a {count, plural, one {1 jour} other {{count} jours}}", "ago-hours": "Il y a {count, plural, one {1 h} other {{count} h}}", "ago-minutes": "Il y a {count, plural, one {1 min} other {{count} mins}}", "ago-seconds": "Il y a {count, plural, =0 {just now} one {1 sec} other {{count} secs}}", "ago-years": "Il y a {count, plural, one {1 an} other {{count} ans}}", "day": "jour", "duration-milliseconds": "{ms}ms", "duration-minutes:seconds": "{m}:{s}m", "duration-seconds": "{s}s", "month": "mois", "month-apr": "Avril", "month-aug": "Août", "month-dec": "Décembre", "month-feb": "<PERSON><PERSON><PERSON><PERSON>", "month-jan": "<PERSON><PERSON>", "month-jul": "<PERSON><PERSON><PERSON>", "month-jun": "Juin", "month-mar": "Mars", "month-may": "<PERSON>", "month-nov": "Novembre", "month-oct": "Octobre", "month-sep": "Septembre", "relative-past-days": "{count} {count, plural, one {jour} other {jours}} passés", "relative-past-months": "{count} {count, plural, one {mois} other {mois}} passés", "relative-past-years": "{count} {count, plural, one {an} other {ans}} passés", "time": "Temps", "weekday-fr": "<PERSON><PERSON><PERSON><PERSON>", "weekday-mo": "<PERSON><PERSON>", "weekday-sa": "<PERSON><PERSON>", "weekday-su": "<PERSON><PERSON><PERSON>", "weekday-th": "<PERSON><PERSON>", "weekday-tu": "<PERSON><PERSON>", "weekday-we": "<PERSON><PERSON><PERSON><PERSON>", "year": "an"}, "editor": {"height": "<PERSON><PERSON>", "image-alt": "Description (alt)", "image-src": "Source", "image-title": "Titre", "insert-image": "Insérer image", "link-href": "Adresse du lien", "link-target": "Target du lien", "link-title": "Titre du lien", "remove-link": "Retirer le lien", "set-link": "Définir lien", "width": "<PERSON><PERSON>"}, "error": {"403-forbidden": "Vous n'êtes pas autorisés à accéder à \"{ path }\". Vous n'avez pas la permission ou votre session a expiré.", "could-not-connect-to-server": "Connexion échoué au serveur Vendure à { url }", "health-check-failed": "Vérification de santé du système a échoué", "no-default-shipping-zone-set": "Ce canal n'a pas de zone de livraison par défaut. <PERSON>la peut causer des erreur de calcul du cout de frais d'expédition.", "no-default-tax-zone-set": "Ce canal n'a pas de zone de taxe par défaut, ce qui peut causer des erreur lors du calcul du prix. Créez ou choisissez une zone."}, "marketing": {"actions": "Actions", "add-action": "Ajouter action", "add-condition": "Ajouter condition", "conditions": "Conditions", "coupon-code": "Code promo", "create-new-promotion": "Creer nouvelle promotion", "duplicate-promotions": "Dup<PERSON>r les promotions", "ends-at": "<PERSON><PERSON><PERSON> au", "per-customer-limit": "Limite par client", "per-customer-limit-tooltip": "Nombre maximum de fois où cette promotion peut être utilisée par un seul client", "promotion": "Promotion", "search-by-name-or-coupon-code": "Rechercher dans le nom ou dans le code des coupons", "starts-at": "Débute au", "usage-limit": "Limite totale d'utilisation", "usage-limit-tooltip": "Nombre maximum de fois où cette promotion peut être utilisée au total"}, "nav": {"administrators": "Administrateurs", "assets": "Fichiers", "catalog": "Catalogue", "channels": "Canaux", "collections": "Collections", "countries": "Pays", "customer-groups": "Groupe clients", "customers": "Clients", "facets": "Composants", "global-settings": "Réglages globaux", "job-queue": "File d'attente de tâches", "marketing": "Marketing", "orders": "Commandes", "payment-methods": "Mode de paiement", "products": "Produits", "promotions": "Promotions", "roles": "Roles", "sales": "<PERSON><PERSON><PERSON>", "scheduled-tasks": "Tâches programmées", "sellers": "Vendeurs", "settings": "Paramètres", "shipping-methods": "Mode d'expédition", "stock-locations": "Lieux de stockage", "system": "Système", "system-status": "Statut du Système", "tax-categories": "Catégories de Taxe", "tax-rates": "<PERSON><PERSON>", "zones": "Zones"}, "order": {"add-item-to-order": "Ajouter un article à la commande", "add-note": "Ajouter une note", "add-payment": "Ajouter un moyen de paiement", "add-payment-to-order": "Ajouter un moyen de paiement à la commande", "add-payment-to-order-success": "Ajout du paiement à la commande réussi", "add-surcharge": "Ajouter une majoration", "added-items": "Ajouter des éléments", "amount": "Quantité", "arrange-additional-payment": "Organiser un paiement supplémentaire", "assign-order-to-another-customer": "Attribuer la commande à un autre client", "billing-address": "Adresse de facturation", "cancel": "Annuler", "cancel-entire-order": "Annuler la commande en entier", "cancel-fulfillment": "Annuler préparation", "cancel-modification": "Annuler la modification", "cancel-order": "Annuler la commande", "cancel-payment": "Annuler le paiement", "cancel-reason-customer-request": "<PERSON><PERSON><PERSON> du <PERSON>", "cancel-reason-not-available": "Pas disponible", "cancel-selected-items": "Annuler les articles selectionnés", "cancel-specified-items": "Annuler les articles spécifiés", "cancellation-reason": "Raison de l'annulation", "cancelled-order-items-success": "Annulation de { count } { count, plural, one {article} other {articles} } de la commande", "cancelled-order-success": "Commande annulée", "complete-draft-order": "Valider le brouillon", "confirm-modifications": "Confirmer les modifications", "contents": "Contenu", "create-fulfillment": "<PERSON><PERSON>er une préparation", "create-fulfillment-success": "Préparation créée", "customer": "Client", "delete-draft-order": "<PERSON><PERSON><PERSON><PERSON> le brouillon", "draft-order": "Commande brouillon", "edit-billing-address": "Modifier l'adresse de facturation", "edit-shipping-address": "Modifier l'adresse de livraison", "error-message": "Message d'erreur", "existing-address": "Adresse existante", "existing-customer": "Client existant", "filter-is-active": "Le filtre est actif", "fulfill": "Effectuer la préparation", "fulfill-order": "<PERSON><PERSON><PERSON><PERSON> la commande", "fulfillment": "Préparation", "fulfillment-method": "Méthode de préparation", "history-coupon-code-applied": "Code promo ajouté", "history-coupon-code-removed": "Code promo retiré", "history-customer-updated": "Client mis à jour", "history-fulfillment-created": "Préparation créée", "history-fulfillment-delivered": "Préparation livrée", "history-fulfillment-shipped": "Préparation expédiée", "history-fulfillment-transition": "Transfert de la préparation de {from} à {to}", "history-items-cancelled": "{count} {count, plural, one {article annulé} other {articles annulés}}", "history-order-cancelled": "Commande annulée", "history-order-created": "Commande cré<PERSON>", "history-order-fulfilled": "Commande préparée", "history-order-modified": "Commande modifiée", "history-order-transition": "Commande transférée de {from} à {to}", "history-payment-settled": "paiement validé", "history-payment-transition": "paiement #{id} transfé<PERSON> de {from} à {to}", "history-refund-transition": "Remboursement #{id} transfé<PERSON> de {from} à {to}", "item-count": "{count} {count, plural, one {article} other {articles}}", "line-fulfillment-all": "Tous les articles ont été préparés", "line-fulfillment-none": "Aucun article préparé", "line-fulfillment-partial": "{ count } sur { total } {count, plural, one {article préparé} other {articles préparés}}", "manually-transition-to-state": "Changer manuellement l'état de la commande...", "manually-transition-to-state-message": "Changer manuellement la commande d'état. A noter que les états sont régis par dès règles qui peuvent empêcher certaines transitions.", "modification-adding-items": "Ajout de {count, plural, one {1 article} other {{count} articles}}", "modification-adding-surcharges": "Ajout de {count} {count, plural, one {surcharge} other {surcharges}}", "modification-adjusting-lines": "Ajustement de {count} {count, plural, one {line} other {lines}}", "modification-not-settled": "Non établi", "modification-recalculate-shipping": "Recal<PERSON>r le coût de livraison", "modification-settled": "Modification établie", "modification-summary": "Résumé des modifications", "modification-updating-billing-address": "Mise à jour de l'adresse de facturation", "modification-updating-shipping-address": "Mise à jour de l'adresse de livraison", "modified-items": "Articles modifiés", "modify-order": "Modifier la commande", "modify-order-price-difference": "Différence de prix", "net-price": "Prix net", "new-customer": "Nouveau client", "no-modifications-made": "Aucune modification n'a été apportée", "note": "Note", "note-is-private": "La note est privée", "note-only-visible-to-administrators": "Visible par les admins uniquement", "note-visible-to-customer": "Visible par les administrateurs et le client", "order": "Commande", "order-history": "Historique de la commande", "order-is-empty": "La commande est vide", "order-state-diagram": "Diagramme des états de la commande", "order-type": "Type de commande", "order-type-aggregate": "Assemblage", "order-type-regular": "Normal", "order-type-seller": "<PERSON><PERSON><PERSON>", "orders": "Commandes", "original-quantity-at-checkout": "Quantité d'origine au moment du paiement", "payment": "Paiement", "payment-amount": "Montant à payer", "payment-metadata": "Métadonnées de paiement", "payment-method": "Mode de paiement", "payment-state": "Etat", "payments": "Paiements", "placed-at": "<PERSON><PERSON><PERSON>", "preview-changes": "Aperçu des modifications", "previous-customer": "Client précédent", "product-name": "Nom du produit", "product-sku": "UGS", "promotions-applied": "Promotions utilisées", "prorated-unit-price": "A l'unité", "quantity": "Quantité", "refund": "Remboursement", "refund-amount": "Montant du remboursement", "refund-and-cancel-order": "Re<PERSON>urser et annuler la commande", "refund-cancellation-reason": "Raison du remboursement/annulation", "refund-cancellation-reason-required": "Raison du remboursement/annulation requise", "refund-metadata": "Métadonnées de rembousement", "refund-order-failed": "Commande en échec", "refund-order-success": "Commande remboursée", "refund-reason": "Raison du remboursement", "refund-reason-customer-request": "<PERSON><PERSON><PERSON> du <PERSON>", "refund-reason-not-available": "Non disponible", "refund-shipping": "Re<PERSON>urser la livraison", "refund-this-payment": "Re<PERSON>urser ce paiement", "refund-total": "Remboursement total", "refund-total-error": "Le remboursement total doit être entre {min} et {max}", "refund-total-warning": "Veuillez spécifier des montants de remboursement équivalents au total du remboursement.", "refund-with-amount": "Rembourser {amount}", "refundable-amount": "Montant remboursable", "refunded-count": "{count} {count, plural, one {article remboursé} other {articles remboursés}}", "removed-items": "Articles supprimés", "return-to-stock": "Retourner au stock", "search-by-order-filters": "Rehercher par numéro de commande / nom du client / Numéro de transaction", "select-address": "Sé<PERSON><PERSON><PERSON> adresse", "select-shipping-method": "Sélectionner méthode transport", "select-state": "Sélectionner un état", "seller-orders": "Commandes du vendeur", "set-billing-address": "Définir l'adresse de facturation", "set-coupon-codes": "Définir les codes promo", "set-customer-for-order": "Définir le client pour la commande", "set-customer-success": "Client défini avec succès", "set-fulfillment-state": "Marquer {state}", "set-shipping-address": "Définir l'adresse de livraison", "set-shipping-method": "Définir la méthode de livraison", "settle-payment": "Effectuer le paiement", "settle-payment-error": "<PERSON><PERSON><PERSON>", "settle-payment-success": "Paiement effectué", "settle-refund": "Effectuer le remboursement", "settle-refund-manual-instructions": "Après avoir manuellement remboursé via le prestataire de paiement ({method}), entrez le numéro de transaction ici.", "settle-refund-success": "Remboursement réglé", "shipping": "Expédition", "shipping-address": "<PERSON><PERSON><PERSON>", "shipping-cancelled": "<PERSON><PERSON><PERSON> annu<PERSON>", "shipping-method": "<PERSON><PERSON><PERSON><PERSON> de livrai<PERSON>", "state": "État", "sub-total": "Sous-total", "successfully-updated-fulfillment": "Préparation mise à jour", "surcharges": "Surcharges", "tax-base": "Prix HT", "tax-description": "Description de la taxe", "tax-rate": "Taux de TVA", "tax-summary": "Résumé de la taxe", "tax-total": "Montant total des taxes", "total": "Total", "tracking-code": "Numéro de suivi", "transaction-id": "Numéro de transaction", "transition-to-state": "Passer à l'état : { state }", "transitioned-payment-to-state-success": "Transition du paiement vers l'état { state } r<PERSON><PERSON>i", "transitioned-to-state-success": "Passage à l'état { state } avec succés", "unable-to-transition-to-state-try-another": "La commande n'a pas pu être remise à l'état \"{state}\" . Veuillez-sélectionner un autre état.", "unfulfilled": "Non préparé", "unit-price": "Prix unitaire"}, "settings": {"add-countries-to-zone": "Ajouter des pays à { zoneName }", "add-countries-to-zone-success": "{ countryCount } {countryCount, plural, one {pays ajouté} other {pays ajoutés}} à la zone \"{ zoneName }\"", "add-products-to-test-order": "Ajouter des produits à la commande de test", "administrator": "Administrateur", "channel": "Canal", "channel-token": "Jeton de canal", "country": "Pays", "create-new-channel": "Créer nouveau canal", "create-new-country": "Créer un nouveau pays", "create-new-payment-method": "<PERSON><PERSON>er une nouvelle méthode de paiement", "create-new-role": "Créer un nouveau rôle", "create-new-seller": "<PERSON><PERSON>er un nouveau vendeur", "create-new-shipping-method": "<PERSON><PERSON>er une nouvelle méthode d'expédition", "create-new-tax-category": "<PERSON><PERSON>er catégorie de taxe", "create-new-tax-rate": "Créer un nouveau taux de TVA", "create-new-zone": "Créer nouvelle zone", "default-currency": "<PERSON><PERSON> par défaut", "default-role-label": "Ceci est le role par défaut et ne peut pas être modifié", "default-shipping-zone": "Zone de livraison par défaut", "default-tax-zone": "Zone de taxe par défaut", "defaults": "Valeurs par défaut", "eligible": "Eligible", "email-address": "<PERSON><PERSON><PERSON> email", "email-address-or-identifier": "Adresse e-mail ou identifiant", "first-name": "Prénom", "fulfillment-handler": "Gestionnaire de remplissage", "global-available-languages-tooltip": "Définit les langues disponibles pour tous les canaux. Les canaux individuels peuvent alors prendre en charge un sous-ensemble de ces langues.", "global-out-of-stock-threshold": "Limite de rupture de stock globale", "global-out-of-stock-threshold-tooltip": "Définir le seuil de stock à partir duquel une variante est considérée comme en rupture. Une valeur négative autorise les commandes en attente.", "last-name": "Nom", "no-eligible-shipping-methods": "Aucun mode d'expédition", "password": "Mot de passe", "payment-eligibility-checker": "Contrôleur d'éligibilité de paiement", "payment-handler": "Gestionnaire de paiement", "payment-method": "Méthode de paiement", "permissions": "Permissions", "prices-include-tax": "Les prix TTC pour la zone par défaut", "profile": "Profil", "rate": "<PERSON><PERSON>", "remove-countries-from-zone-success": "{ countryCount } {countryCount, plural, one {pays retiré} other {pays retirés}} de la zone \"{ zoneName }\"", "remove-from-zone": "Supprimer de la zone", "role": "Role", "roles": "Roles", "search-by-product-name-or-sku": "Chercher par nom de produit ou par UGS", "seller": "<PERSON><PERSON><PERSON>", "shipping-calculator": "Calculateur de frais d'expédition", "shipping-eligibility-checker": "Vérificateur d'éligibilité à l'expédition", "shipping-method": "Mode d'expédition", "tax-category": "Catégorie de taxe", "tax-rate": "Taux de taxe", "test-address": "<PERSON><PERSON><PERSON> de <PERSON>", "test-result": "Résultat de test", "test-shipping-method": "Mode d'expédition de test", "test-shipping-methods": "Modes d'expédition de test", "track-inventory-default": "Suivre le stock par défaut", "view-zone-members": "Voir les membres", "zone": "Zone"}, "state": {"adding-items": "Ajout d'articles", "arranging-additional-payment": "Paiement additionel en cours", "arranging-payment": "Paiement en cours", "authorized": "Autorisé", "cancelled": "<PERSON><PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON>", "declined": "<PERSON><PERSON><PERSON>", "delivered": "Livré", "draft": "Brouillon", "error": "<PERSON><PERSON><PERSON>", "failed": "Echec", "modifying": "En cours de modification", "partially-delivered": "Partiellement livré", "partially-shipped": "Partiellement expédié", "payment-authorized": "Paiement autorisé", "payment-settled": "Paiement finalisé", "pending": "En attente", "settled": "Finalisé", "shipped": "Expédié"}, "system": {"all-job-queues": "Toute la file de tâches", "could-not-trigger-task": "Impossible de déclencher la tâche", "health-all-systems-up": "Tous les système en route", "health-error": "Erreur: un ou plusieurs systèmes sont en pannes!", "health-last-checked": "Dernière vérification", "health-message": "Message", "health-refresh": "<PERSON><PERSON><PERSON><PERSON>", "health-status": "Statut", "health-status-down": "En panne", "health-status-up": "En route", "job-data": "<PERSON><PERSON><PERSON>", "job-duration": "<PERSON><PERSON><PERSON>", "job-error": "<PERSON><PERSON><PERSON> de la tâche", "job-queue-name": "Nom de la file d'attente", "job-result": "Résultat de la tâche", "job-state": "Etat de la tâche", "job-state-all": "Tous les états", "job-state-cancelled": "<PERSON><PERSON><PERSON>", "job-state-completed": "<PERSON><PERSON><PERSON><PERSON>", "job-state-failed": "<PERSON><PERSON><PERSON>", "job-state-pending": "En attente", "job-state-running": "En cours", "last-executed-at": "Dernière exécution", "last-result": "<PERSON><PERSON>", "next-execution-at": "Prochaine exécution", "run-task": "Exécuter la tâche", "schedule": "Programmation", "task-id": "ID de la tâche", "task-will-be-triggered": "La tâche sera exécutée"}}