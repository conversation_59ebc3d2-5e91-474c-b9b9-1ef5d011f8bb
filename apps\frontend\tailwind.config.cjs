const colors = require('tailwindcss/colors');

module.exports = {
	content: ['./src/**/*.{js,ts,jsx,tsx}'],
	darkMode: 'class', // Enable dark mode with class strategy
	theme: {
		extend: {
			colors: {
				primary: {
					DEFAULT: '#E91E63', // Professional Pink (Material Design)
					50: '#FCE4EC',
					100: '#F8BBD9',
					200: '#F48FB1',
					300: '#F06292',
					400: '#EC407A',
					500: '#E91E63',
					600: '#D81B60',
					700: '#C2185B',
					800: '#AD1457',
					900: '#880E4F',
					...colors.pink,
				},
				secondary: {
					DEFAULT: '#9C27B0', // Professional Purple (Material Design)
					...colors.purple,
				},
				accent1: '#00BCD4', // Professional Cyan (Material Design)
				accent2: '#4CAF50', // Professional Green (Material Design)
				background: '#1A1A1A', // Dark background
				text: '#FFFFFF', // White text
				// Dark mode specific colors
				'dark-surface': '#2A2A2A', // Dark surface color
				'dark-surface-secondary': '#3A3A3A', // Secondary dark surface
				'dark-border': '#4A4A4A', // Dark border color
			},
			animation: {
				'fade-in': 'fadeIn 0.3s ease-out forwards',
			},
			keyframes: {
				fadeIn: {
					'0%': { opacity: '0' },
					'100%': { opacity: '1' },
				},
			},
		},
		variants: {
			extend: {
				opacity: ['disabled'],
				transform: ['hover', 'focus'],
				scale: ['hover', 'focus'],
			},
		},
	},
	plugins: [require('@tailwindcss/forms')],
};
