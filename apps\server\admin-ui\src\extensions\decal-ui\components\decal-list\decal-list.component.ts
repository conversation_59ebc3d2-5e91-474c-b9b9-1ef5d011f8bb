import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { SharedModule } from '@vendure/admin-ui/core';

@Component({
	selector: 'decal-list',
	template: `
		<vdr-page-block>
			<vdr-action-bar>
				<vdr-ab-left>
					<vdr-page-title [title]="'Decals'" icon="image"></vdr-page-title>
				</vdr-ab-left>
				<vdr-ab-right>
					<button class="btn btn-primary" type="button" [routerLink]="['./create']">
						<clr-icon shape="plus"></clr-icon>
						Create new decal
					</button>
				</vdr-ab-right>
			</vdr-action-bar>

			<div class="table-responsive">
				<table class="table table-sm table-hover">
					<thead>
						<tr>
							<th>Image</th>
							<th>Name</th>
							<th>Category</th>
							<th>Status</th>
							<th>Updated</th>
						</tr>
					</thead>
					<tbody>
						<tr
							*ngFor="let decal of decals"
							[routerLink]="['./', decal.id]"
							style="cursor: pointer;"
						>
							<td>
								<img
									[src]="decal.asset?.preview + '?preset=tiny'"
									[alt]="decal.name"
									style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;"
									*ngIf="decal.asset"
								/>
								<div
									*ngIf="!decal.asset"
									class="bg-gray-200"
									style="width: 40px; height: 40px; border-radius: 4px;"
								></div>
							</td>
							<td>{{ decal.name }}</td>
							<td>{{ decal.category }}</td>
							<td>
								<vdr-chip [colorType]="decal.isActive ? 'success' : 'warning'">
									{{ decal.isActive ? 'Active' : 'Inactive' }}
								</vdr-chip>
							</td>
							<td>{{ decal.updatedAt | localeDate: 'short' }}</td>
						</tr>
						<tr *ngIf="decals.length === 0">
							<td colspan="5" class="text-center">No decals found</td>
						</tr>
					</tbody>
				</table>
			</div>
		</vdr-page-block>
	`,
	standalone: true,
	imports: [SharedModule],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DecalListComponent implements OnInit {
	decals = [
		{
			id: '1',
			name: 'Sample Decal 1',
			description: 'A sample decal',
			category: 'Sports',
			isActive: true,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
			asset: {
				id: '1',
				preview: 'https://via.placeholder.com/40',
				name: 'sample-image.jpg',
			},
		},
		{
			id: '2',
			name: 'Sample Decal 2',
			description: 'Another sample decal',
			category: 'Music',
			isActive: false,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
			asset: {
				id: '2',
				preview: 'https://via.placeholder.com/40',
				name: 'sample-image2.jpg',
			},
		},
	];

	ngOnInit() {
		// Component initialization
		console.log('DecalListComponent initialized');
	}
}
