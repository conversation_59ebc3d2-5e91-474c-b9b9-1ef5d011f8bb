# INFORMATION
# We are not exposing the default ports for the services in this file.
# This is to avoid conflicts with existing services on your machine.
# In case you don't have any services running on the default ports, you can expose them by changing the
# ports section in the services block. Please don't forget to update the ports in the .env file as well.

services:
  postgres_db:
    image: postgres:16-alpine
    volumes:
      - postgres_db_data:/var/lib/postgresql/data
    ports:
      - '6543:5432'
    environment:
      POSTGRES_DB: vendure
      POSTGRES_USER: vendure
      POSTGRES_PASSWORD: s22WL1eropDn_bnBLZ6OjQ
    labels:
      - 'io.vendure.create.name=sugarjays-shop'

  mysql_db:
    image: mysql:8
    volumes:
      - mysql_db_data:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: 'ROOT'
      MYSQL_DATABASE: vendure
      MYSQL_USER: vendure
      MYSQL_PASSWORD: s22WL1eropDn_bnBLZ6OjQ
    ports:
      - '4306:3306'
    labels:
      - 'io.vendure.create.name=sugarjays-shop'

  mariadb_db:
    image: mariadb:10
    volumes:
      - mariadb_db_data:/var/lib/mysql
    environment:
      MARIADB_ROOT_PASSWORD: 'ROOT'
      MARIADB_DATABASE: vendure
      MARIADB_USER: vendure
      MARIADB_PASSWORD: s22WL1eropDn_bnBLZ6OjQ
    ports:
      - '3306:3306'
    labels:
      - 'io.vendure.create.name=sugarjays-shop'

  # RECOMMENDED (especially for production)
  # Want to use our BullMQ with Redis instead of our default database job queue?
  # Checkout our BullMQ plugin: https://docs.vendure.io/reference/core-plugins/job-queue-plugin/bull-mqjob-queue-plugin/
  redis:
    image: redis:7-alpine
    ports:
      - '6479:6379'
    volumes:
      - redis_data:/data
    labels:
      - 'io.vendure.create.name=sugarjays-shop'

  # RECOMMENDED
  # Want to use Typesense instead of our default search engine?
  # Checkout our advanced search plugin: https://vendure.io/hub/vendure-plus-advanced-search-plugin
  # To run the typesense container run "docker compose up -d typesense"
  typesense:
    image: typesense/typesense:27.0
    command: ['--data-dir', '/data', '--api-key', 'SuperSecret']
    ports:
      - '8208:8108'
    volumes:
      - typesense_data:/data
    labels:
      - 'io.vendure.create.name=sugarjays-shop'

  # Want to use Elasticsearch instead of our default database engine?
  # Checkout our Elasticsearch plugin: https://docs.vendure.io/reference/core-plugins/elasticsearch-plugin/
  # To run the elasticsearch container run "docker compose up -d elasticsearch"
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.1.1
    environment:
      discovery.type: single-node
      bootstrap.memory_lock: true
      ES_JAVA_OPTS: -Xms512m -Xmx512m
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - '9300:9200'
    labels:
      - 'io.vendure.create.name=sugarjays-shop'

volumes:
  postgres_db_data:
    driver: local
    labels:
      - 'io.vendure.create.name=sugarjays-shop'
  mysql_db_data:
    driver: local
    labels:
      - 'io.vendure.create.name=sugarjays-shop'
  mariadb_db_data:
    driver: local
    labels:
      - 'io.vendure.create.name=sugarjays-shop'
  typesense_data:
    driver: local
    labels:
      - 'io.vendure.create.name=sugarjays-shop'
  elasticsearch_data:
    driver: local
    labels:
      - 'io.vendure.create.name=sugarjays-shop'
  redis_data:
    driver: local
    labels:
      - 'io.vendure.create.name=sugarjays-shop'
