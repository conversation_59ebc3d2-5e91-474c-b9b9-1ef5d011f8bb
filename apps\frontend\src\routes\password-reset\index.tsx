import { $, component$, useSignal } from '@qwik.dev/core';
import { useLocation, useNavigate } from '@qwik.dev/router';
import XCircleIcon from '~/components/icons/XCircleIcon';
import { resetPasswordMutation } from '~/providers/shop/account/account';

export default component$(() => {
	const password = useSignal('');
	const error = useSignal('');
	const location = useLocation();
	const navigate = useNavigate();

	const reset = $(async () => {
		const resetPassword = await resetPasswordMutation(
			location.url.href.split('=')[1],
			password.value
		);

		resetPassword.__typename !== 'CurrentUser'
			? (error.value = resetPassword.message)
			: navigate('/account');
	});

	return (
		<div class="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-background">
			<div class="sm:mx-auto sm:w-full sm:max-w-md">
				<h2 class="mt-6 text-center text-3xl font-bold text-text">Reset password</h2>
				<p class="mt-2 text-center text-sm text-gray-600">Choose a new password</p>
			</div>
			<div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
				<div class="bg-dark-surface py-8 px-4 shadow sm:rounded-lg sm:px-10">
					<div>
						<div class="mt-1 mb-8">
							<input
								type="password"
								value={password.value}
								required
								onInput$={(_, el) => (password.value = el.value)}
								onKeyUp$={(ev, el) => {
									error.value = '';
									if (ev.key === 'Enter' && !!el.value) {
										reset();
									}
								}}
								class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
							/>
						</div>
					</div>
					{error.value !== '' && (
						<div class="rounded-md bg-red-50 p-4 mb-8">
							<div class="flex">
								<div class="flex-shrink-0">
									<XCircleIcon />
								</div>
								<div class="ml-3">
									<h3 class="text-sm font-medium text-red-800">
										We ran into a problem verifying your account!
									</h3>
									<p class="text-sm text-red-700 mt-2">{error.value}</p>
								</div>
							</div>
						</div>
					)}
					<div>
						<button
							class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
							onClick$={reset}
						>
							Reset password
						</button>
					</div>
				</div>
			</div>
		</div>
	);
});
