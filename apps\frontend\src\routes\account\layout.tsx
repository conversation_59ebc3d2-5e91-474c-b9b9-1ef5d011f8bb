import { Slot, component$, useContext, useVisibleTask$ } from '@qwik.dev/core';
import { TabsContainer } from '~/components/account/TabsContainer';
import { APP_STATE } from '~/constants';
import { getActiveCustomerQuery } from '~/providers/shop/customer/customer';
import { fullNameWithTitle } from '~/utils';

export default component$(() => {
	const appState = useContext(APP_STATE);

	useVisibleTask$(async () => {
		const activeCustomer = await getActiveCustomerQuery();
		if (activeCustomer) {
			appState.customer = {
				title: activeCustomer.title ?? '',
				firstName: activeCustomer.firstName,
				id: activeCustomer.id,
				lastName: activeCustomer.lastName,
				emailAddress: activeCustomer.emailAddress,
				phoneNumber: activeCustomer.phoneNumber ?? '',
			};
		} else {
			window.location.href = '/';
		}
	});

	return (
		<div class="min-h-screen bg-background">
			<div class="max-w-6xl mx-auto px-4 py-8">
				<div class="mb-8">
					<h1 class="text-white text-2xl font-semibold">
						Welcome back, {fullNameWithTitle(appState.customer)}
					</h1>
				</div>
				<div class="w-full">
					<TabsContainer>
						<Slot />
					</TabsContainer>
				</div>
			</div>
		</div>
	);
});
