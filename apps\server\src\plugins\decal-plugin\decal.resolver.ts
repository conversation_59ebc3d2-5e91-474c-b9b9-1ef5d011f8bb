import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { Allow, Ctx, ID, Permission, RequestContext } from '@vendure/core';
import { CreateDecalInput, DecalService, UpdateDecalInput } from './decal.service';

@Resolver()
export class DecalAdminResolver {
	constructor(private decalService: DecalService) {}

	@Query()
	@Allow(Permission.ReadCatalog)
	async decals(@Ctx() ctx: RequestContext) {
		return this.decalService.findAll(ctx);
	}

	@Query()
	@Allow(Permission.ReadCatalog)
	async decal(@Ctx() ctx: RequestContext, @Args('id') id: ID) {
		return this.decalService.findOne(ctx, id);
	}

	@Mutation()
	@Allow(Permission.CreateCatalog)
	async createDecal(@Ctx() ctx: RequestContext, @Args('input') input: CreateDecalInput) {
		return this.decalService.create(ctx, input);
	}

	@Mutation()
	@Allow(Permission.UpdateCatalog)
	async updateDecal(@Ctx() ctx: RequestContext, @Args('input') input: UpdateDecalInput) {
		return this.decalService.update(ctx, input);
	}

	@Mutation()
	@Allow(Permission.DeleteCatalog)
	async deleteDecal(@Ctx() ctx: RequestContext, @Args('id') id: ID) {
		return this.decalService.delete(ctx, id);
	}
}

@Resolver()
export class DecalShopResolver {
	constructor(private decalService: DecalService) {}

	@Query()
	async decals(@Ctx() ctx: RequestContext) {
		return this.decalService.findAll(ctx);
	}

	@Query()
	async decalsByCategory(@Ctx() ctx: RequestContext, @Args('category') category: string) {
		return this.decalService.findByCategory(ctx, category);
	}
}
