/**
 * Design Studio Types
 * These types define the structure for the custom t-shirt design system
 */

export interface Decal {
	id: string;
	name: string;
	description: string;
	asset: {
		id: string;
		preview: string;
	};
	category: string;
	maxWidth: number;
	maxHeight: number;
	minScale: number;
	maxScale: number;
}

export interface PlacedDecal {
	id: string;
	decalId: string;
	area: DesignArea;
	position: { x: number; y: number };
	scale: number;
	rotation: number;
	zIndex: number;
}

export type DesignArea = 'front' | 'back' | 'leftSleeve' | 'rightSleeve';

export interface DesignState {
	selectedArea: DesignArea;
	decals: PlacedDecal[];
	productVariant: any; // TODO: Type with proper Vendure ProductVariant type
}

export interface AreaDesign {
	decals: PlacedDecal[];
	backgroundImage?: string;
}

export interface SavedDesign {
	version: string;
	productVariantId: string;
	areas: {
		front?: AreaDesign;
		back?: AreaDesign;
		leftSleeve?: AreaDesign;
		rightSleeve?: AreaDesign;
	};
	metadata: {
		createdAt: string;
		totalDecals: number;
		previewUrls: string[];
	};
}

export interface DesignConstraints {
	maxDecalsPerArea: number;
	minDecalSize: number;
	maxDecalSize: number;
	printableArea: {
		x: number;
		y: number;
		width: number;
		height: number;
	};
}

/**
 * Design Studio Configuration
 * Default constraints and settings for the design system
 */
export const DESIGN_CONSTRAINTS: DesignConstraints = {
	maxDecalsPerArea: 10,
	minDecalSize: 20,
	maxDecalSize: 200,
	printableArea: {
		x: 50,
		y: 50,
		width: 300,
		height: 400,
	},
};

/**
 * Available design areas with display names
 */
export const DESIGN_AREAS: Record<DesignArea, string> = {
	front: 'Front',
	back: 'Back',
	leftSleeve: 'Left Sleeve',
	rightSleeve: 'Right Sleeve',
} as const;
