import { Injectable } from '@nestjs/common';
import { DeletionResponse, DeletionResult } from '@vendure/common/lib/generated-types';
import {
	AssetService,
	ID,
	ListQueryOptions,
	PaginatedList,
	RequestContext,
	TransactionalConnection,
} from '@vendure/core';
import { Decal } from './decal.entity';

export interface CreateDecalInput {
	name: string;
	description: string;
	assetId: ID;
	category: string;
	maxWidth?: number;
	maxHeight?: number;
	minScale?: number;
	maxScale?: number;
}

export interface UpdateDecalInput {
	id: ID;
	name?: string;
	description?: string;
	assetId?: ID;
	category?: string;
	isActive?: boolean;
	maxWidth?: number;
	maxHeight?: number;
	minScale?: number;
	maxScale?: number;
}

@Injectable()
export class DecalService {
	constructor(
		private connection: TransactionalConnection,
		private assetService: AssetService
	) {}

	async findAll(
		ctx: RequestContext,
		options?: ListQueryOptions<Decal>
	): Promise<PaginatedList<Decal>> {
		const [items, totalItems] = await this.connection.getRepository(ctx, Decal).findAndCount({
			where: { isActive: true },
			relations: ['asset'],
			skip: options?.skip ?? undefined,
			take: options?.take ?? undefined,
		});

		return {
			items,
			totalItems,
		};
	}

	async findOne(ctx: RequestContext, id: ID): Promise<Decal | null> {
		return this.connection.getRepository(ctx, Decal).findOne({
			where: { id },
			relations: ['asset'],
		});
	}

	async create(ctx: RequestContext, input: CreateDecalInput): Promise<Decal> {
		const asset = await this.assetService.findOne(ctx, input.assetId);
		if (!asset) {
			throw new Error(`Asset with id ${input.assetId} not found`);
		}

		const decal = new Decal({
			name: input.name,
			description: input.description,
			category: input.category,
			asset,
			maxWidth: input.maxWidth || 100,
			maxHeight: input.maxHeight || 100,
			minScale: input.minScale || 0.5,
			maxScale: input.maxScale || 2.0,
		});

		return this.connection.getRepository(ctx, Decal).save(decal);
	}

	async update(ctx: RequestContext, input: UpdateDecalInput): Promise<Decal> {
		const decal = await this.connection.getEntityOrThrow(ctx, Decal, input.id);

		if (input.name !== undefined) decal.name = input.name;
		if (input.description !== undefined) decal.description = input.description;
		if (input.category !== undefined) decal.category = input.category;
		if (input.isActive !== undefined) decal.isActive = input.isActive;
		if (input.maxWidth !== undefined) decal.maxWidth = input.maxWidth;
		if (input.maxHeight !== undefined) decal.maxHeight = input.maxHeight;
		if (input.minScale !== undefined) decal.minScale = input.minScale;
		if (input.maxScale !== undefined) decal.maxScale = input.maxScale;

		if (input.assetId) {
			const asset = await this.assetService.findOne(ctx, input.assetId);
			if (!asset) {
				throw new Error(`Asset with id ${input.assetId} not found`);
			}
			decal.asset = asset;
		}

		return this.connection.getRepository(ctx, Decal).save(decal);
	}

	async delete(ctx: RequestContext, id: ID): Promise<DeletionResponse> {
		const decal = await this.connection.getEntityOrThrow(ctx, Decal, id);
		await this.connection.getRepository(ctx, Decal).remove(decal);

		return {
			result: DeletionResult.DELETED,
		};
	}

	async findByCategory(ctx: RequestContext, category: string): Promise<Decal[]> {
		return this.connection.getRepository(ctx, Decal).find({
			where: { category, isActive: true },
			relations: ['asset'],
		});
	}
}
