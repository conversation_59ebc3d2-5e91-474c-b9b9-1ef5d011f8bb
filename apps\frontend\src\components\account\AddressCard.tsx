import { QRL, component$ } from '@qwik.dev/core';
import { useNavigate } from '@qwik.dev/router';
import { ShippingAddress } from '~/types';
import { Button } from '../buttons/Button';
import { HighlightedButton } from '../buttons/HighlightedButton';
import PencilIcon from '../icons/PencilIcon';
import XCircleIcon from '../icons/XCircleIcon';

type IProps = {
	address: ShippingAddress;
	onDelete$?: QRL<(id: string) => void>;
};

export default component$<IProps>(({ address, onDelete$ }) => {
	const navigate = useNavigate();

	return (
		<div class="bg-gray-800 border border-gray-600 rounded-lg p-6 transition-colors hover:border-gray-500">
			<div class="space-y-4">
				<div>
					<h3 class="text-lg font-semibold text-white">{address.fullName}</h3>
					{address.company && <p class="text-gray-400 text-sm">{address.company}</p>}
				</div>

				<div class="text-gray-300 space-y-1">
					<p>{address.streetLine1}</p>
					{address.streetLine2 && <p>{address.streetLine2}</p>}
					<p>
						{address.city}, {address.province} {address.postalCode}
					</p>
				</div>

				{address.phoneNumber && (
					<div class="flex items-center text-gray-400">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							fill="none"
							viewBox="0 0 24 24"
							stroke-width="1.5"
							stroke="currentColor"
							class="w-4 h-4 mr-2"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z"
							/>
						</svg>
						<span class="text-sm">{address.phoneNumber}</span>
					</div>
				)}

				<div class="flex gap-3 pt-2">
					<HighlightedButton
						extraClass="flex-1"
						onClick$={() => {
							navigate(`/account/address-book/${address.id}`);
						}}
					>
						<PencilIcon /> &nbsp; Edit
					</HighlightedButton>
					<Button
						extraClass="flex-1"
						onClick$={() => {
							if (onDelete$ && address.id) {
								onDelete$(address.id);
							}
						}}
					>
						<XCircleIcon /> &nbsp; Delete
					</Button>
				</div>
				{(address.defaultShippingAddress || address.defaultBillingAddress) && (
					<div class="flex flex-wrap gap-2 pt-4 border-t border-gray-600">
						{address.defaultShippingAddress && (
							<span class="bg-accent2 text-white text-xs px-2 py-1 rounded-full">
								Default Shipping
							</span>
						)}
						{address.defaultBillingAddress && (
							<span class="bg-accent1 text-white text-xs px-2 py-1 rounded-full">
								Default Billing
							</span>
						)}
					</div>
				)}
			</div>
		</div>
	);
});
