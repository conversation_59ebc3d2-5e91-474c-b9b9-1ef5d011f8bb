import { component$, useSignal, useVisibleTask$ } from '@qwik.dev/core';
import { Link } from '@qwik.dev/router';
import { $localize } from '~/utils/localize';

export default component$(() => {
	const heroRef = useSignal<Element>();
	const isLoaded = useSignal(false);

	// T-shirt floating elements data
	const floatingElements = [
		{ emoji: '👕', size: 'text-4xl', delay: 0 },
		{ emoji: '🎽', size: 'text-3xl', delay: 0.5 },
		{ emoji: '👔', size: 'text-5xl', delay: 1 },
		{ emoji: '🩱', size: 'text-3xl', delay: 1.5 },
		{ emoji: '👗', size: 'text-4xl', delay: 2 },
		{ emoji: '🎨', size: 'text-3xl', delay: 2.5 },
		{ emoji: '✨', size: 'text-2xl', delay: 3 },
		{ emoji: '🌟', size: 'text-3xl', delay: 3.5 },
		{ emoji: '💫', size: 'text-2xl', delay: 4 },
		{ emoji: '⚡', size: 'text-3xl', delay: 4.5 },
		{ emoji: '🔥', size: 'text-4xl', delay: 5 },
		{ emoji: '🎪', size: 'text-3xl', delay: 5.5 },
	];

	// Neon circles data
	const neonCircles = [
		{ size: 80, color: '#00ffff', opacity: 0.3, delay: 0 },
		{ size: 120, color: '#ff0080', opacity: 0.2, delay: 1 },
		{ size: 60, color: '#00ff88', opacity: 0.4, delay: 2 },
		{ size: 100, color: '#ff4080', opacity: 0.25, delay: 0.5 },
		{ size: 40, color: '#8000ff', opacity: 0.35, delay: 1.5 },
		{ size: 150, color: '#0080ff', opacity: 0.15, delay: 2.5 },
		{ size: 70, color: '#ffff00', opacity: 0.3, delay: 3 },
		{ size: 90, color: '#ff8000', opacity: 0.25, delay: 0.8 },
	];

	// Pink bubbles (filled circles)
	const pinkBubbles = [
		{ size: 25, delay: 0.5 },
		{ size: 35, delay: 1.2 },
		{ size: 20, delay: 2.1 },
		{ size: 30, delay: 0.8 },
		{ size: 15, delay: 1.8 },
		{ size: 40, delay: 2.5 },
		{ size: 18, delay: 3.2 },
		{ size: 28, delay: 1.1 },
	];

	// Green square line elements
	const greenElements = [
		{ type: 'rectangle', width: 80, height: 8, delay: 1 },
		{ type: 'rectangle', width: 120, height: 6, delay: 2.5 },
		{ type: 'rectangle', width: 60, height: 10, delay: 1.8 },
		{ type: 'square', size: 25, delay: 0.7 },
		{ type: 'square', size: 15, delay: 2.2 },
		{ type: 'rectangle', width: 100, height: 4, delay: 3.1 },
	];

	// Dotted lines
	const dottedLines = [
		{ length: 150, angle: 15, delay: 1.5 },
		{ length: 100, angle: -30, delay: 2.8 },
		{ length: 80, angle: 45, delay: 0.9 },
	];

	// Small particles data
	const particles = Array.from({ length: 30 }, () => ({
		size: Math.random() * 8 + 4,
		color: ['#00ffff', '#ff0080', '#00ff88', '#ff4080', '#8000ff'][Math.floor(Math.random() * 5)],
		delay: Math.random() * 4,
	}));

	// Initialize animations when component mounts
	useVisibleTask$(() => {
		setTimeout(() => {
			isLoaded.value = true;
		}, 100);
	});

	return (
		<>
			<style>{`
				@keyframes float {
					0%, 100% { transform: translateY(0px) translateX(0px) scale(1); }
					25% { transform: translateY(-20px) translateX(10px) scale(1.1); }
					50% { transform: translateY(-10px) translateX(-10px) scale(1.05); }
					75% { transform: translateY(-15px) translateX(5px) scale(1.08); }
				}

				@keyframes fadeInUp {
					from { opacity: 0; transform: translateY(50px); }
					to { opacity: 1; transform: translateY(0); }
				}

				@keyframes neonPulse {
					0%, 100% { 
						transform: scale(1) rotate(0deg);
						opacity: var(--base-opacity);
					}
					50% { 
						transform: scale(1.1) rotate(180deg);
						opacity: calc(var(--base-opacity) * 1.5);
					}
				}

				@keyframes neonFloat {
					0%, 100% { 
						transform: translateY(0px) translateX(0px);
					}
					33% { 
						transform: translateY(-15px) translateX(10px);
					}
					66% { 
						transform: translateY(5px) translateX(-8px);
					}
				}

				@keyframes particleFloat {
					0%, 100% { 
						transform: translateY(0px) translateX(0px) scale(1);
						opacity: 0.6;
					}
					50% { 
						transform: translateY(-25px) translateX(15px) scale(1.2);
						opacity: 1;
					}
				}

				@keyframes bounce {
					0%, 100% { transform: translateY(0); }
					50% { transform: translateY(-10px); }
				}

				.hero-background {
					background: radial-gradient(circle at 20% 50%, #0a0a0a 0%, #000000 40%), 
					           radial-gradient(circle at 80% 20%, #1a0a1a 0%, #000000 40%),
					           radial-gradient(circle at 40% 80%, #0a1a1a 0%, #000000 40%),
					           #000000;
					position: relative;
				}

				.neon-circle {
					position: absolute;
					border-radius: 50%;
					border: 2px solid;
					animation: neonPulse 4s ease-in-out infinite, neonFloat 6s ease-in-out infinite;
					pointer-events: none;
				}

				.neon-circle::before {
					content: '';
					position: absolute;
					top: -2px;
					left: -2px;
					right: -2px;
					bottom: -2px;
					border-radius: 50%;
					border: 1px solid;
					border-color: inherit;
					opacity: 0.5;
				}

				.particle {
					position: absolute;
					border-radius: 50%;
					animation: particleFloat 3s ease-in-out infinite;
					pointer-events: none;
				}

				.floating-element {
					animation: float 6s ease-in-out infinite;
				}

				.fade-in-up {
					animation: fadeInUp 0.8s ease-out forwards;
					opacity: 0;
				}

				.bounce-scroll {
					animation: bounce 2s ease-in-out infinite;
				}

				.hover-scale {
					transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
				}

				.hover-scale:hover {
					transform: scale(1.05) translateY(-2px);
				}

				.btn-primary {
					background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 50%, #ffa8a8 100%);
					color: white;
					box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
					transition: all 0.3s ease;
					border: none;
				}

				.btn-primary:hover {
					box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
					transform: translateY(-2px);
					background: linear-gradient(135deg, #ff5252 0%, #ff7979 50%, #ff9999 100%);
				}

				.btn-secondary {
					transition: all 0.3s ease;
					backdrop-filter: blur(10px);
					background: rgba(255, 255, 255, 0.1);
					border: 2px solid rgba(255, 255, 255, 0.3);
					position: relative;
					z-index: 1;
				}

				.btn-secondary:hover {
					background: rgba(255, 255, 255, 0.2);
					border-color: rgba(255, 255, 255, 0.6);
					transform: translateY(-2px);
					box-shadow: 0 10px 25px rgba(255, 255, 255, 0.1);
				}

				.neon-glow {
					text-shadow: 0 0 10px rgba(255, 255, 255, 0.5), 
					           0 0 20px rgba(255, 255, 255, 0.3), 
					           0 0 30px rgba(255, 255, 255, 0.2);
				}

				.pink-bubble {
					position: absolute;
					border-radius: 50%;
					background: linear-gradient(135deg, #ff69b4, #ff1493);
					animation: particleFloat 4s ease-in-out infinite;
					pointer-events: none;
					box-shadow: 0 0 15px rgba(255, 105, 180, 0.6), inset 0 0 10px rgba(255, 255, 255, 0.2);
				}

				.green-element {
					position: absolute;
					background: linear-gradient(135deg, #00ff88, #00cc6a);
					animation: neonFloat 5s ease-in-out infinite;
					pointer-events: none;
					box-shadow: 0 0 12px rgba(0, 255, 136, 0.5);
				}

				.green-rectangle {
					border-radius: 2px;
				}

				.green-square {
					border-radius: 3px;
					transform: rotate(45deg);
				}

				.dotted-line {
					position: absolute;
					height: 2px;
					background: repeating-linear-gradient(
						to right,
						#00ff88 0px,
						#00ff88 8px,
						transparent 8px,
						transparent 16px
					);
					animation: neonFloat 6s ease-in-out infinite;
					pointer-events: none;
					box-shadow: 0 0 8px rgba(0, 255, 136, 0.4);
				}
			`}</style>

			<div
				ref={heroRef}
				class={`relative min-h-screen overflow-hidden hero-background ${isLoaded.value ? 'loaded' : ''}`}
			>
				{/* Neon Circles */}
				<div class="absolute inset-0 pointer-events-none">
					{neonCircles.map((circle, index) => (
						<div
							key={`neon-${index}`}
							class="neon-circle"
							style={{
								width: `${circle.size}px`,
								height: `${circle.size}px`,
								borderColor: circle.color,
								left: `${Math.random() * 90 + 5}%`,
								top: `${Math.random() * 90 + 5}%`,
								'--base-opacity': circle.opacity,
								animationDelay: `${circle.delay}s`,
								opacity: isLoaded.value ? circle.opacity : 0,
								transition: `opacity 1s ease ${circle.delay}s`,
								boxShadow: `0 0 20px ${circle.color}40, inset 0 0 20px ${circle.color}20`,
							}}
						/>
					))}
				</div>

				{/* Particles */}
				<div class="absolute inset-0 pointer-events-none">
					{particles.map((particle, index) => (
						<div
							key={`particle-${index}`}
							class="particle"
							style={{
								width: `${particle.size}px`,
								height: `${particle.size}px`,
								backgroundColor: particle.color,
								left: `${Math.random() * 100}%`,
								top: `${Math.random() * 100}%`,
								animationDelay: `${particle.delay}s`,
								opacity: isLoaded.value ? 0.6 : 0,
								transition: `opacity 0.8s ease ${particle.delay}s`,
								boxShadow: `0 0 10px ${particle.color}`,
							}}
						/>
					))}
				</div>

				{/* Pink Bubbles */}
				<div class="absolute inset-0 pointer-events-none">
					{pinkBubbles.map((bubble, index) => (
						<div
							key={`bubble-${index}`}
							class="pink-bubble"
							style={{
								width: `${bubble.size}px`,
								height: `${bubble.size}px`,
								left: `${Math.random() * 90 + 5}%`,
								top: `${Math.random() * 90 + 5}%`,
								animationDelay: `${bubble.delay}s`,
								opacity: isLoaded.value ? 0.8 : 0,
								transition: `opacity 1s ease ${bubble.delay}s`,
							}}
						/>
					))}
				</div>

				{/* Green Elements */}
				<div class="absolute inset-0 pointer-events-none">
					{greenElements.map((element, index) => (
						<div
							key={`green-${index}`}
							class={`green-element ${element.type === 'rectangle' ? 'green-rectangle' : 'green-square'}`}
							style={{
								width: element.type === 'rectangle' ? `${element.width}px` : `${element.size}px`,
								height: element.type === 'rectangle' ? `${element.height}px` : `${element.size}px`,
								left: `${Math.random() * 85 + 5}%`,
								top: `${Math.random() * 85 + 5}%`,
								animationDelay: `${element.delay}s`,
								opacity: isLoaded.value ? 0.7 : 0,
								transition: `opacity 1.2s ease ${element.delay}s`,
							}}
						/>
					))}
				</div>

				{/* Dotted Lines */}
				<div class="absolute inset-0 pointer-events-none">
					{dottedLines.map((line, index) => (
						<div
							key={`line-${index}`}
							class="dotted-line"
							style={{
								width: `${line.length}px`,
								left: `${Math.random() * 80 + 10}%`,
								top: `${Math.random() * 80 + 10}%`,
								transform: `rotate(${line.angle}deg)`,
								animationDelay: `${line.delay}s`,
								opacity: isLoaded.value ? 0.6 : 0,
								transition: `opacity 1s ease ${line.delay}s`,
							}}
						/>
					))}
				</div>

				{/* Floating T-shirt Elements */}
				<div class="absolute inset-0 pointer-events-none">
					{floatingElements.map((element, index) => (
						<div
							key={index}
							class={`absolute ${element.size} select-none floating-element`}
							style={{
								left: `${Math.random() * 80 + 10}%`,
								top: `${Math.random() * 80 + 10}%`,
								animationDelay: `${element.delay}s`,
								opacity: isLoaded.value ? 1 : 0,
								transition: `opacity 0.5s ease ${element.delay}s`,
								filter: 'drop-shadow(0 0 8px rgba(255, 255, 255, 0.3))',
							}}
						>
							{element.emoji}
						</div>
					))}
				</div>

				{/* Main Hero Content */}
				<div class="relative z-10 flex items-center justify-center min-h-screen px-6">
					<div class="text-center max-w-6xl mx-auto">
						<div
							class={`fade-in-up ${isLoaded.value ? 'loaded' : ''}`}
							style={{ animationDelay: '0.3s' }}
						>
							<div class="flex justify-center mb-6">
								<img
									src="/sjlogo.png"
									alt="SugarJays"
									class="h-16 md:h-24 lg:h-60 max-w-full w-auto hover-scale cursor-default neon-glow"
									style={{
										filter:
											'drop-shadow(0 0 10px rgba(255, 255, 255, 0.5)) drop-shadow(0 0 20px rgba(255, 255, 255, 0.3)) drop-shadow(0 0 30px rgba(255, 255, 255, 0.2))',
										width: 'clamp(250px, 65vw, 800px)',
										height: 'auto',
									}}
								/>
							</div>
						</div>

						<div
							class={`fade-in-up ${isLoaded.value ? 'loaded' : ''}`}
							style={{ animationDelay: '0.6s' }}
						>
							<p class="text-xl md:text-3xl text-white/90 mb-4 font-light">
								{$localize`Custom T-Shirts That Tell Your Story`}
							</p>
						</div>

						<div
							class={`fade-in-up ${isLoaded.value ? 'loaded' : ''}`}
							style={{ animationDelay: '0.9s' }}
						>
							<p class="text-lg md:text-xl text-white/80 mb-8 font-medium">
								{$localize`Premium Quality • Unique Designs • Express Yourself`}
							</p>
						</div>

						<div
							class={`flex flex-col sm:flex-row gap-6 justify-center items-center fade-in-up ${isLoaded.value ? 'loaded' : ''}`}
							style={{ animationDelay: '1.2s' }}
						>
							<Link
								href="/collections"
								class="inline-flex items-center px-6 py-2.5 rounded-full btn-primary font-medium text-base hover-scale"
							>
								<span class="mr-1.5">🛍️</span>
								{$localize`Shop Collection`}
							</Link>

							<Link
								href="/collections"
								class="inline-flex items-center px-6 py-2.5 rounded-full text-white font-medium text-base btn-secondary whitespace-nowrap"
							>
								<span class="mr-1.5">👕</span> {$localize`Design Custom`}
							</Link>
						</div>

						{/* Scroll indicator */}
						<div
							class={`absolute bottom-8 left-1/2 transform -translate-x-1/2 fade-in-up ${isLoaded.value ? 'loaded' : ''}`}
							style={{ animationDelay: '1.5s' }}
						>
							<div class="text-white/60 text-center">
								<p class="text-sm mb-2">{$localize`Scroll to explore`}</p>
								<div class="w-6 h-10 border-2 border-white/40 rounded-full mx-auto flex justify-center hover:border-white/60 transition-colors duration-300">
									<div class="w-1 h-3 bg-white/60 rounded-full mt-2 bounce-scroll" />
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Subtle overlay for text readability */}
				<div class="absolute inset-0 bg-black/20 z-0" />
			</div>
		</>
	);
});
