import { component$, QRL, Signal } from '@qwik.dev/core';
import { getDecalById } from './data/mock-decals';
import type { PlacedDecal } from './types/design-types';
import { DESIGN_CONSTRAINTS } from './types/design-types';

interface DesignControlsProps {
	selectedDecal: Signal<PlacedDecal | null>;
	onDecalUpdate$: QRL<(decal: PlacedDecal) => void>;
	onDecalDelete$: QRL<(decalId: string) => void>;
}

export default component$<DesignControlsProps>(
	({ selectedDecal, onDecalUpdate$, onDecalDelete$ }) => {
		if (!selectedDecal.value) {
			return (
				<div class="design-controls bg-gray-800 p-4 rounded-lg">
					<h3 class="text-white font-bold text-lg mb-4">Design Controls</h3>
					<div class="text-center py-8">
						<div class="text-gray-400 mb-2">
							<svg
								class="w-12 h-12 mx-auto mb-3 opacity-50"
								fill="currentColor"
								viewBox="0 0 20 20"
							>
								<path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.828-2.828z" />
							</svg>
							<p>Select a decal to edit its properties</p>
						</div>
						<p class="text-xs text-gray-500">Click on any decal in the canvas to start editing</p>
					</div>
				</div>
			);
		}

		const decal = selectedDecal.value;
		const decalData = getDecalById(decal.decalId);

		return (
			<div class="design-controls bg-gray-800 p-4 rounded-lg">
				<h3 class="text-white font-bold text-lg mb-4">Design Controls</h3>

				{/* Decal Info */}
				{decalData && (
					<div class="mb-4 p-3 bg-gray-700 rounded-lg">
						<h4 class="text-white font-medium text-sm mb-1">{decalData.name}</h4>
						<p class="text-gray-400 text-xs">{decalData.description}</p>
						<div class="flex justify-between text-xs text-gray-500 mt-2">
							<span>Area: {decal.area}</span>
							<span>Layer: {decal.zIndex}</span>
						</div>
					</div>
				)}

				<div class="space-y-4">
					{/* Scale Control */}
					<div>
						<label class="block text-white text-sm font-medium mb-2">
							Scale: {decal.scale.toFixed(2)}x
						</label>
						<input
							type="range"
							min={decalData?.minScale || 0.5}
							max={decalData?.maxScale || 2.0}
							step="0.1"
							value={decal.scale}
							class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
							onInput$={(e) => {
								const target = e.target as HTMLInputElement;
								const newScale = parseFloat(target.value);
								const updatedDecal = {
									...decal,
									scale: newScale,
								};
								onDecalUpdate$(updatedDecal);
							}}
						/>
						<div class="flex justify-between text-xs text-gray-400 mt-1">
							<span>{decalData?.minScale || 0.5}x</span>
							<span>{decalData?.maxScale || 2.0}x</span>
						</div>
					</div>

					{/* Rotation Control */}
					<div>
						<label class="block text-white text-sm font-medium mb-2">
							Rotation: {Math.round(decal.rotation)}°
						</label>
						<input
							type="range"
							min="0"
							max="360"
							step="5"
							value={decal.rotation}
							class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
							onInput$={(e) => {
								const target = e.target as HTMLInputElement;
								const updatedDecal = {
									...decal,
									rotation: parseInt(target.value),
								};
								onDecalUpdate$(updatedDecal);
							}}
						/>
						<div class="flex justify-between text-xs text-gray-400 mt-1">
							<span>0°</span>
							<span>360°</span>
						</div>
						<div class="flex gap-2 mt-2">
							<button
								type="button"
								class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-2 py-1 rounded text-xs transition-colors"
								onClick$={() => {
									const updatedDecal = { ...decal, rotation: 0 };
									onDecalUpdate$(updatedDecal);
								}}
							>
								Reset
							</button>
							<button
								type="button"
								class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-2 py-1 rounded text-xs transition-colors"
								onClick$={() => {
									const updatedDecal = { ...decal, rotation: (decal.rotation + 90) % 360 };
									onDecalUpdate$(updatedDecal);
								}}
							>
								+90°
							</button>
						</div>
					</div>

					{/* Layer Control */}
					<div>
						<label class="block text-white text-sm font-medium mb-2">Layer Order</label>
						<div class="flex gap-2">
							<button
								type="button"
								class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
								disabled={decal.zIndex <= 0}
								onClick$={() => {
									const updatedDecal = {
										...decal,
										zIndex: Math.max(0, decal.zIndex - 1),
									};
									onDecalUpdate$(updatedDecal);
								}}
							>
								Send Back
							</button>
							<button
								type="button"
								class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm transition-colors"
								onClick$={() => {
									const updatedDecal = {
										...decal,
										zIndex: decal.zIndex + 1,
									};
									onDecalUpdate$(updatedDecal);
								}}
							>
								Bring Forward
							</button>
						</div>
					</div>

					{/* Position Control */}
					<div>
						<label class="block text-white text-sm font-medium mb-2">Position</label>
						<div class="grid grid-cols-2 gap-2">
							<div>
								<label class="block text-xs text-gray-400 mb-1">X Position</label>
								<input
									type="number"
									value={Math.round(decal.position.x)}
									min={DESIGN_CONSTRAINTS.printableArea.x}
									max={DESIGN_CONSTRAINTS.printableArea.x + DESIGN_CONSTRAINTS.printableArea.width}
									class="w-full bg-gray-700 text-white px-2 py-1 rounded text-sm border border-gray-600 focus:border-primary focus:outline-none"
									onInput$={(e) => {
										const target = e.target as HTMLInputElement;
										const newX = parseInt(target.value) || 0;
										const updatedDecal = {
											...decal,
											position: {
												...decal.position,
												x: newX,
											},
										};
										onDecalUpdate$(updatedDecal);
									}}
								/>
							</div>
							<div>
								<label class="block text-xs text-gray-400 mb-1">Y Position</label>
								<input
									type="number"
									value={Math.round(decal.position.y)}
									min={DESIGN_CONSTRAINTS.printableArea.y}
									max={DESIGN_CONSTRAINTS.printableArea.y + DESIGN_CONSTRAINTS.printableArea.height}
									class="w-full bg-gray-700 text-white px-2 py-1 rounded text-sm border border-gray-600 focus:border-primary focus:outline-none"
									onInput$={(e) => {
										const target = e.target as HTMLInputElement;
										const newY = parseInt(target.value) || 0;
										const updatedDecal = {
											...decal,
											position: {
												...decal.position,
												y: newY,
											},
										};
										onDecalUpdate$(updatedDecal);
									}}
								/>
							</div>
						</div>
						<button
							type="button"
							class="w-full mt-2 bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded text-xs transition-colors"
							onClick$={() => {
								const updatedDecal = {
									...decal,
									position: { x: 200, y: 250 }, // Center position
								};
								onDecalUpdate$(updatedDecal);
							}}
						>
							Center Decal
						</button>
					</div>

					{/* Quick Actions */}
					<div>
						<label class="block text-white text-sm font-medium mb-2">Quick Actions</label>
						<div class="grid grid-cols-2 gap-2">
							<button
								type="button"
								class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm transition-colors"
								onClick$={() => {
									const updatedDecal = {
										...decal,
										scale: 1.0,
										rotation: 0,
									};
									onDecalUpdate$(updatedDecal);
								}}
							>
								Reset Transform
							</button>
							<button
								type="button"
								class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm transition-colors"
								onClick$={() => {
									// Create a duplicate with slight offset
									const duplicateDecal = {
										...decal,
										id: `decal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
										position: {
											x: decal.position.x + 20,
											y: decal.position.y + 20,
										},
										zIndex: decal.zIndex + 1,
									};
									onDecalUpdate$(duplicateDecal);
								}}
							>
								Duplicate
							</button>
						</div>
					</div>

					{/* Delete Button */}
					<div class="pt-4 border-t border-gray-700">
						<button
							type="button"
							class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors flex items-center justify-center gap-2"
							onClick$={() => {
								onDecalDelete$(decal.id);
							}}
						>
							<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
								<path
									fill-rule="evenodd"
									d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
									clip-rule="evenodd"
								/>
							</svg>
							Delete Decal
						</button>
					</div>
				</div>
			</div>
		);
	}
);
