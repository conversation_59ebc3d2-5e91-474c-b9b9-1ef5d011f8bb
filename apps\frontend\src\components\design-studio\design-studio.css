/* Design Studio Styles */
.design-studio {
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.design-canvas canvas {
	cursor: crosshair;
	user-select: none;
}

.design-canvas canvas:hover {
	cursor: grab;
}

.design-canvas canvas:active {
	cursor: grabbing;
}

/* Custom slider styles */
.slider {
	-webkit-appearance: none;
	appearance: none;
	background: transparent;
	cursor: pointer;
}

.slider::-webkit-slider-track {
	background: #374151;
	height: 8px;
	border-radius: 4px;
}

.slider::-webkit-slider-thumb {
	-webkit-appearance: none;
	appearance: none;
	background: #3b82f6;
	height: 20px;
	width: 20px;
	border-radius: 50%;
	cursor: pointer;
}

.slider::-moz-range-track {
	background: #374151;
	height: 8px;
	border-radius: 4px;
	border: none;
}

.slider::-moz-range-thumb {
	background: #3b82f6;
	height: 20px;
	width: 20px;
	border-radius: 50%;
	cursor: pointer;
	border: none;
}

/* Decal grid animations */
.decal-item {
	transition: all 0.2s ease;
}

.decal-item:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Loading spinner */
@keyframes spin {
	to {
		transform: rotate(360deg);
	}
}

.animate-spin {
	animation: spin 1s linear infinite;
}

/* Design studio modal backdrop */
.design-studio {
	backdrop-filter: blur(4px);
}

/* Scrollbar styles for webkit browsers */
.design-studio ::-webkit-scrollbar {
	width: 8px;
}

.design-studio ::-webkit-scrollbar-track {
	background: #1f2937;
}

.design-studio ::-webkit-scrollbar-thumb {
	background: #4b5563;
	border-radius: 4px;
}

.design-studio ::-webkit-scrollbar-thumb:hover {
	background: #6b7280;
}
