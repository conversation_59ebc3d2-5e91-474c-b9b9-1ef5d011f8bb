import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDecalEntity1703000000000 implements MigrationInterface {
	name = 'AddDecalEntity1703000000000';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
            CREATE TABLE "decal" (
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                "id" SERIAL NOT NULL,
                "name" character varying NOT NULL,
                "description" text NOT NULL,
                "category" character varying NOT NULL,
                "isActive" boolean NOT NULL DEFAULT true,
                "maxWidth" numeric(5,2) NOT NULL DEFAULT '100',
                "maxHeight" numeric(5,2) NOT NULL DEFAULT '100',
                "minScale" numeric(5,2) NOT NULL DEFAULT '0.5',
                "maxScale" numeric(5,2) NOT NULL DEFAULT '2.0',
                "assetId" integer,
                CONSTRAINT "PK_decal_id" PRIMARY KEY ("id")
            )
        `);

		await queryRunner.query(`
            ALTER TABLE "decal" 
            ADD CONSTRAINT "FK_decal_asset" 
            FOREIGN KEY ("assetId") 
            REFERENCES "asset"("id") 
            ON DELETE NO ACTION 
            ON UPDATE NO ACTION
        `);

		await queryRunner.query(`
            CREATE INDEX "IDX_decal_category" ON "decal" ("category")
        `);

		await queryRunner.query(`
            CREATE INDEX "IDX_decal_isActive" ON "decal" ("isActive")
        `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`DROP INDEX "IDX_decal_isActive"`);
		await queryRunner.query(`DROP INDEX "IDX_decal_category"`);
		await queryRunner.query(`ALTER TABLE "decal" DROP CONSTRAINT "FK_decal_asset"`);
		await queryRunner.query(`DROP TABLE "decal"`);
	}
}
