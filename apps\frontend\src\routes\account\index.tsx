import { $, component$, useContext, useSignal, useVisibleTask$ } from '@qwik.dev/core';
import { isBrowser } from '@qwik.dev/core/build';
import { Button } from '~/components/buttons/Button';
import { HighlightedButton } from '~/components/buttons/HighlightedButton';
import { ErrorMessage } from '~/components/error-message/ErrorMessage';
import CheckIcon from '~/components/icons/CheckIcon';
import PencilSquareIcon from '~/components/icons/PencilSquareIcon';
import ShieldCheckIcon from '~/components/icons/ShieldCheckIcon';
import XMarkIcon from '~/components/icons/XMarkIcon';
import { Modal } from '~/components/modal/Modal';
import { APP_STATE } from '~/constants';
import {
	requestUpdateCustomerEmailAddressMutation,
	updateCustomerMutation,
} from '~/providers/shop/account/account';
import { getActiveCustomerQuery } from '~/providers/shop/customer/customer';
import { ActiveCustomer } from '~/types';

export default component$(() => {
	const appState = useContext(APP_STATE);
	const isEditing = useSignal(false);
	const showModal = useSignal(false);
	const newEmail = useSignal('');
	const errorMessage = useSignal('');
	const currentPassword = useSignal('');
	const update = {
		customer: {} as ActiveCustomer,
	};

	useVisibleTask$(async () => {
		const activeCustomer = await getActiveCustomerQuery();
		appState.customer = {
			title: activeCustomer.title ?? '',
			firstName: activeCustomer.firstName,
			id: activeCustomer.id,
			lastName: activeCustomer.lastName,
			emailAddress: activeCustomer.emailAddress,
			phoneNumber: activeCustomer.phoneNumber ?? '',
		};
		newEmail.value = activeCustomer?.emailAddress as string;
	});

	const updateCustomer = $(async (): Promise<void> => {
		await updateCustomerMutation(appState.customer);

		appState.customer.emailAddress !== newEmail.value
			? (showModal.value = true)
			: (isEditing.value = false);
	});

	const updateEmail = $(async (password: string, newEmail: string) => {
		const { requestUpdateCustomerEmailAddress } = await requestUpdateCustomerEmailAddressMutation(
			password,
			newEmail
		);
		if (requestUpdateCustomerEmailAddress.__typename === 'InvalidCredentialsError') {
			errorMessage.value = requestUpdateCustomerEmailAddress.message || '';
		} else {
			errorMessage.value = '';
			isEditing.value = false;
			showModal.value = false;
		}
	});

	return (
		<div>
			<div class="max-w-6xl mx-auto p-4">
				<div class="bg-gray-800 border border-gray-600 rounded-lg p-6 mb-6">
					<div class="flex items-center justify-between mb-6">
						<div class="flex items-center space-x-4">
							<div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
								<span class="text-white text-xl font-semibold">
									{appState.customer?.firstName?.charAt(0)}
									{appState.customer?.lastName?.charAt(0)}
								</span>
							</div>
							<div>
								<h2 class="text-xl font-semibold text-white">
									{appState.customer?.title && (
										<span class="text-base font-normal mr-1 text-gray-300">
											{appState.customer?.title}
										</span>
									)}
									{appState.customer?.firstName} {appState.customer?.lastName}
								</h2>
								<p class="text-gray-300">{appState.customer?.emailAddress}</p>
								{appState.customer?.phoneNumber && (
									<p class="text-gray-400 text-sm">{appState.customer?.phoneNumber}</p>
								)}
							</div>
						</div>
						<button
							class="text-gray-400 hover:text-primary transition-colors p-2"
							onClick$={() => {
								isEditing.value = !isEditing.value;
								if (isBrowser) {
									window.scrollTo(0, 100);
								}
								if (!isEditing.value && isBrowser) {
									window.scrollTo(0, 0);
								}
							}}
						>
							<PencilSquareIcon />
						</button>
					</div>
				</div>
			</div>
			<Modal
				open={showModal.value}
				title="Confirm E-Mail address change"
				onSubmit$={() => {
					updateEmail(currentPassword.value, newEmail.value);
				}}
				onCancel$={() => {
					showModal.value = false;
				}}
			>
				<div q:slot="modalIcon">
					<ShieldCheckIcon forcedClass="h-10 w-10 text-primary-500" />
				</div>
				<div q:slot="modalContent" class="space-y-4">
					<p class="text-gray-700">We will send a verification E-Mail to {newEmail.value}</p>

					<div class="space-y-1">
						<label html-for="password" class="block text-sm font-medium text-gray-700">
							Confirm the change by entering your password:
						</label>
						<input
							type="password"
							name="password"
							onChange$={(_, el) => {
								currentPassword.value = el.value;
							}}
							class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
						/>
					</div>

					{errorMessage.value !== '' && (
						<ErrorMessage
							heading="We ran into a problem changing your E-Mail!"
							message={errorMessage.value}
						/>
					)}
				</div>
			</Modal>

			{isEditing.value && (
				<div class="max-w-6xl mx-auto p-4">
					<div class="bg-gray-800 border border-gray-600 rounded-lg p-6">
						<h3 class="text-lg font-semibold text-white mb-6">Edit Account Details</h3>
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div class="md:col-span-2 md:max-w-xs">
								<label class="block text-sm font-medium text-gray-300 mb-2">Title</label>
								<input
									type="text"
									value={appState.customer?.title}
									onInput$={(_, el) => {
										update.customer.title = el.value;
									}}
									class="input-text"
								/>
							</div>

							<div>
								<label html-for="firstName" class="block text-sm font-medium text-gray-300 mb-2">
									First Name
								</label>
								<input
									type="text"
									value={appState.customer?.firstName}
									onChange$={(_, el) => {
										if (el.value !== '') {
											update.customer.firstName = el.value;
										}
									}}
									class="input-text"
								/>
							</div>

							<div>
								<label html-for="lastName" class="block text-sm font-medium text-gray-300 mb-2">
									Last Name
								</label>
								<input
									type="text"
									value={appState.customer?.lastName}
									onChange$={(_, el) => {
										if (el.value !== '') {
											update.customer.lastName = el.value;
										}
									}}
									class="input-text"
								/>
							</div>

							<div>
								<label class="block text-sm font-medium text-gray-300 mb-2">Email</label>
								<input
									type="email"
									value={appState.customer?.emailAddress}
									onChange$={(_, el) => {
										if (el.value !== '') {
											newEmail.value = el.value;
										}
									}}
									class="input-text"
								/>
							</div>

							<div>
								<label class="block text-sm font-medium text-gray-300 mb-2">Phone Number</label>
								<input
									type="tel"
									value={appState.customer?.phoneNumber}
									onChange$={(_, el) => {
										update.customer.phoneNumber = el.value;
									}}
									class="input-text"
								/>
							</div>
						</div>

						<div class="flex gap-4 mt-8">
							<HighlightedButton
								onClick$={() => {
									appState.customer = { ...appState.customer, ...update.customer };
									updateCustomer();
								}}
							>
								<CheckIcon /> &nbsp; Save
							</HighlightedButton>

							<Button
								onClick$={() => {
									isEditing.value = false;
								}}
							>
								<XMarkIcon forcedClass="w-4 h-4" /> &nbsp; Cancel
							</Button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
});
