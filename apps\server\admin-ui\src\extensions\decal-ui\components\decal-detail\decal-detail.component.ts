import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SharedModule } from '@vendure/admin-ui/core';

@Component({
	selector: 'decal-detail',
	template: `
		<vdr-page-block>
			<vdr-action-bar>
				<vdr-ab-left>
					<vdr-page-title [title]="decal?.name || 'Decal Detail'" icon="image"></vdr-page-title>
				</vdr-ab-left>
				<vdr-ab-right>
					<button class="btn btn-secondary" type="button" [routerLink]="['/extensions/decal']">
						<clr-icon shape="arrow left"></clr-icon>
						Back to list
					</button>
					<button class="btn btn-primary" type="button" (click)="save()" [disabled]="!decal">
						<clr-icon shape="floppy"></clr-icon>
						Save
					</button>
				</vdr-ab-right>
			</vdr-action-bar>

			<div class="form-grid" *ngIf="decal">
				<div class="form-group">
					<label>Name</label>
					<input
						type="text"
						class="form-control"
						[(ngModel)]="decal.name"
						placeholder="Enter decal name"
					/>
				</div>

				<div class="form-group">
					<label>Category</label>
					<input
						type="text"
						class="form-control"
						[(ngModel)]="decal.category"
						placeholder="Enter category"
					/>
				</div>

				<div class="form-group">
					<label>Description</label>
					<textarea
						class="form-control"
						[(ngModel)]="decal.description"
						placeholder="Enter description"
						rows="3"
					></textarea>
				</div>

				<div class="form-group">
					<label>Status</label>
					<div class="form-check">
						<input
							type="checkbox"
							class="form-check-input"
							[(ngModel)]="decal.isActive"
							id="isActive"
						/>
						<label class="form-check-label" for="isActive"> Active </label>
					</div>
				</div>

				<div class="form-group">
					<label>Max Width</label>
					<input
						type="number"
						class="form-control"
						[(ngModel)]="decal.maxWidth"
						placeholder="Maximum width"
						step="0.1"
					/>
				</div>

				<div class="form-group">
					<label>Max Height</label>
					<input
						type="number"
						class="form-control"
						[(ngModel)]="decal.maxHeight"
						placeholder="Maximum height"
						step="0.1"
					/>
				</div>

				<div class="form-group">
					<label>Min Scale</label>
					<input
						type="number"
						class="form-control"
						[(ngModel)]="decal.minScale"
						placeholder="Minimum scale"
						step="0.1"
						min="0"
						max="1"
					/>
				</div>

				<div class="form-group">
					<label>Max Scale</label>
					<input
						type="number"
						class="form-control"
						[(ngModel)]="decal.maxScale"
						placeholder="Maximum scale"
						step="0.1"
						min="1"
					/>
				</div>

				<div class="form-group" *ngIf="decal.asset">
					<label>Image Preview</label>
					<div>
						<img
							[src]="decal.asset.preview + '?preset=medium'"
							[alt]="decal.name"
							style="max-width: 200px; max-height: 200px; object-fit: cover; border-radius: 8px; border: 1px solid #ddd;"
						/>
					</div>
				</div>
			</div>

			<div class="alert alert-info" *ngIf="!decal">Loading decal details...</div>
		</vdr-page-block>
	`,
	standalone: true,
	imports: [SharedModule],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DecalDetailComponent implements OnInit {
	decal: any = null;
	decalId: string;

	constructor(
		private route: ActivatedRoute,
		private router: Router
	) {}

	ngOnInit() {
		this.decalId = this.route.snapshot.params.id;
		this.loadDecal();
	}

	private loadDecal() {
		// Mock data for demonstration - in real implementation, fetch from API
		if (this.decalId === '1') {
			this.decal = {
				id: '1',
				name: 'Sample Decal 1',
				description: 'A sample decal for demonstration',
				category: 'Sports',
				isActive: true,
				maxWidth: 10.0,
				maxHeight: 10.0,
				minScale: 0.5,
				maxScale: 2.0,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
				asset: {
					id: '1',
					preview: 'https://via.placeholder.com/200',
					name: 'sample-image.jpg',
				},
			};
		} else if (this.decalId === '2') {
			this.decal = {
				id: '2',
				name: 'Sample Decal 2',
				description: 'Another sample decal',
				category: 'Music',
				isActive: false,
				maxWidth: 15.0,
				maxHeight: 15.0,
				minScale: 0.3,
				maxScale: 3.0,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
				asset: {
					id: '2',
					preview: 'https://via.placeholder.com/200',
					name: 'sample-image2.jpg',
				},
			};
		} else {
			// Default for create/new mode or unknown IDs
			this.decal = {
				id: this.decalId,
				name: '',
				description: '',
				category: '',
				isActive: true,
				maxWidth: 10.0,
				maxHeight: 10.0,
				minScale: 0.5,
				maxScale: 2.0,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			};
		}
	}

	save() {
		if (!this.decal) return;

		// Mock save operation - in real implementation, call mutation
		console.log('Saving decal:', this.decal);

		// Navigate back to list after save
		this.router.navigate(['/extensions/decal']);
	}
}
