import { component$, useContext } from '@qwik.dev/core';
import { Link } from '@qwik.dev/router';
import { APP_STATE } from '~/constants';
import { $localize } from '~/utils/localize';

export default component$(() => {
	const collections = useContext(APP_STATE).collections.filter(
		(item) => item.parent?.name === '__root_collection__' && !!item.featuredAsset
	);

	const navigation = {
		support: [
			{ name: $localize`Help`, href: '/contact' },
			{ name: $localize`Track order`, href: '/account/orders' },
			{ name: $localize`Shipping`, href: '/services#shipping' },
			{ name: $localize`Returns`, href: '/services#returns' },
		],
		company: [
			{ name: $localize`About`, href: '/about' },
			{ name: $localize`Contact us`, href: '/contact' },
			{ name: $localize`FAQ`, href: '/contact#faq' },
			{ name: $localize`Services`, href: '/services' },
		],
	};

	return (
		<footer class="pt-6 border-t border-primary/40 bg-black">
			<div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
				<div class="xl:grid xl:grid-cols-3 xl:gap-8">
					<div class="grid grid-cols-2 gap-8 xl:col-span-2">
						<div class="md:grid md:grid-cols-2 md:gap-8">
							<div>
								<h3 class="text-sm font-semibold tracking-wider uppercase text-white">{$localize`Shop`}</h3>
								<ul class="mt-4 space-y-4">
									{collections.map((collection) => (
										<li key={collection.id}>
											<Link
												class="text-base text-text hover:text-accent1 transition-colors"
												href={`/collections/${collection.slug}`}
												key={collection.id}
											>
												{collection.name}
											</Link>
										</li>
									))}
								</ul>
							</div>
							<div class="mt-12 md:mt-0">
								<h3 class="text-sm font-semibold tracking-wider uppercase text-white">
									{$localize`Support`}
								</h3>
								<ul class="mt-4 space-y-4">
									{navigation.support.map((item) => (
										<li key={item.name}>
											<Link
												href={item.href}
												class="text-base text-text hover:text-accent1 transition-colors"
											>
												{item.name}
											</Link>
										</li>
									))}
								</ul>
							</div>
						</div>
						<div class="md:grid md:grid-cols-2 md:gap-8">
							<div>
								<h3 class="text-sm font-semibold tracking-wider uppercase text-white">
									{$localize`Company`}
								</h3>
								<ul class="mt-4 space-y-4">
									{navigation.company.map((item) => (
										<li key={item.name}>
											<Link
												href={item.href}
												class="text-base text-text hover:text-accent1 transition-colors"
											>
												{item.name}
											</Link>
										</li>
									))}
								</ul>
							</div>
						</div>
					</div>
					<div class="mt-8 xl:mt-0">
						<h3 class="text-sm font-semibold tracking-wider uppercase text-white">
							{$localize`Subscribe to our newsletter`}
						</h3>
						<p class="mt-4 text-base text-text">
							{$localize`Be the first to know about exclusive offers & deals.`}
						</p>
						<div class="mt-4 sm:flex sm:max-w-md">
							<label id="email-subscription" class="sr-only">
								Email address
							</label>
							<input
								type="email"
								autoComplete="email"
								required
								class="input-text"
								placeholder={$localize`Enter your email`}
								aria-labelledby="email-subscription"
							/>
							<div class="mt-3 rounded-md sm:mt-0 sm:ml-3 sm:flex-shrink-0">
								<button class="btn-primary" onClick$={() => {}}>
									{$localize`Subscribe`}
								</button>
							</div>
						</div>
					</div>
				</div>
				<div class="mt-8 border-t border-primary/40 pt-8">
					<Link
						class="flex items-center space-x-4 font-medium text-text hover:text-accent1 transition-colors"
						target="_blank"
						href="https://simpsoft.info"
					>
						<span>Powered By SimpShop</span>
					</Link>
				</div>
			</div>
		</footer>
	);
});
