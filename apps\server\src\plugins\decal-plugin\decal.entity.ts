import { DeepPartial } from '@vendure/common/lib/shared-types';
import { Asset, VendureEntity } from '@vendure/core';
import { Column, Entity, ManyToOne } from 'typeorm';

@Entity()
export class Decal extends VendureEntity {
	constructor(input?: DeepPartial<Decal>) {
		super(input);
	}

	@Column()
	name: string;

	@Column('text')
	description: string;

	@ManyToOne(() => Asset, { eager: true })
	asset: Asset;

	@Column()
	category: string;

	@Column({ default: true })
	isActive: boolean;

	@Column('decimal', { precision: 5, scale: 2, default: 100 })
	maxWidth: number;

	@Column('decimal', { precision: 5, scale: 2, default: 100 })
	maxHeight: number;

	@Column('decimal', { precision: 5, scale: 2, default: 0.5 })
	minScale: number;

	@Column('decimal', { precision: 5, scale: 2, default: 2.0 })
	maxScale: number;
}
